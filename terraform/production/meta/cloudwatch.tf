variable "prod_lambda_invocations_alarm_threshold" {
  description = "Alarm over this many invocations per 15m"
  type        = number
  default     = 100
}


resource "aws_cloudwatch_dashboard" "prod_shaka_overview" {
  dashboard_name = "prod-shaka-overview"

  dashboard_body = templatefile("${path.module}/cloudwatch_dashboard.json", {
    prod_lambda_invocations_alarm_threshold = var.prod_lambda_invocations_alarm_threshold,
    alarms                                  = concat([aws_cloudwatch_metric_alarm.prod_lambda_invocations_alarm], values(aws_cloudwatch_metric_alarm.prod_eb_errors_alarm), values(aws_cloudwatch_metric_alarm.prod_lambda_errors_alarm)),
    queues                                  = ["prod-pipeline-notification-queue", "prod-transatel-new-file-queue", "prod-gamma-new-file-queue"],
    lambdas                                 = { for name, deets in local.all_lambdas : name => merge({ index : index(keys(local.all_lambdas), name) }, deets) },
    lambda_height                           = 4 + (5 * length(local.all_lambdas)),
    beanstalks                              = values(var.elastic_beanstalks),
  })
}


resource "aws_cloudwatch_metric_alarm" "prod_lambda_invocations_alarm" {
  alarm_name          = "prod-lambda-invocations-alarm"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 1
  // https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/aws-services-cloudwatch-metrics.html
  // https://docs.aws.amazon.com/lambda/latest/dg/monitoring-metrics.html
  metric_name        = "Invocations"
  namespace          = "AWS/Lambda"
  period             = 900
  statistic          = "Sum"
  threshold          = var.prod_lambda_invocations_alarm_threshold
  alarm_description  = "Lambda Invocations over expected (maybe recursion?)"
  treat_missing_data = "breaching"
  alarm_actions = [
    aws_sns_topic.prod_cloudwatch_alarms.arn
  ]
  ok_actions = [
    aws_sns_topic.prod_cloudwatch_alarms.arn
  ]
}

resource "aws_cloudwatch_metric_alarm" "prod_lambda_errors_alarm" {
  for_each            = local.all_lambdas
  alarm_name          = "prod-lambda-errors-alarm-${each.key}"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 1
  metric_name         = "Errors"
  dimensions = {
    FunctionName = each.value.lambda_function_name
  }
  namespace          = "AWS/Lambda"
  period             = 900
  statistic          = "Sum"
  threshold          = 1
  alarm_description  = "Lambda Errors over expected"
  treat_missing_data = lookup(each.value, "treat_missing_data", "breaching")
  alarm_actions = [
    aws_sns_topic.prod_cloudwatch_alarms.arn
  ]
  ok_actions = [
    aws_sns_topic.prod_cloudwatch_alarms.arn
  ]
}

resource "aws_cloudwatch_metric_alarm" "prod_eb_errors_alarm" {
  for_each            = var.elastic_beanstalks
  alarm_name          = "prod-eb-errors-alarm-${each.key}"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 1
  metric_name         = "HTTPCode_Target_5XX_Count"
  dimensions = {
    LoadBalancer = each.value.lb_name
  }
  namespace          = "AWS/ApplicationELB"
  period             = 900
  statistic          = "Sum"
  threshold          = 1
  alarm_description  = "EB Errors over expected"
  treat_missing_data = lookup(each.value, "treat_missing_data", "breaching")
  alarm_actions = [
    aws_sns_topic.prod_cloudwatch_alarms.arn
  ]
  ok_actions = [
    aws_sns_topic.prod_cloudwatch_alarms.arn
  ]
}
