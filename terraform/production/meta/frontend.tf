resource "aws_iam_policy" "prod_frontend_deploy_pipeline_policy" {
  name        = "prod-frontend-deploy-pipeline-policy"
  description = "Policy to allow codepipeline to deploy frontends"
  policy      = <<EOF
{
    "Statement": [
        {
            "Action": [
                "iam:PassRole"
            ],
            "Resource": "*",
            "Effect": "Allow",
            "Condition": {
                "StringEqualsIfExists": {
                    "iam:PassedToService": [
                        "cloudformation.amazonaws.com",
                        "elasticbeanstalk.amazonaws.com",
                        "ec2.amazonaws.com",
                        "ecs-tasks.amazonaws.com"
                    ]
                }
            }
        },
        {
            "Action": [
              "logs:*"
            ],
            "Resource": "*",
            "Effect": "Allow"
        },
        {
            "Action": [
                "codedeploy:*"
            ],
            "Resource": "*",
            "Effect": "Allow"
        },
        {
            "Action": [
                "ec2:*",
                "autoscaling:*",
                "cloudwatch:*",
                "cloudformation:*",
                "lambda:*",
                "s3:*",
                "elasticbeanstalk:*",
                "elasticloadbalancing:*"
            ],
            "Resource": "*",
            "Effect": "Allow"
        },
        {
            "Action": [
                "cloudformation:CreateStack",
                "cloudformation:DeleteStack",
                "cloudformation:DescribeStacks",
                "cloudformation:UpdateStack",
                "cloudformation:CreateChangeSet",
                "cloudformation:DeleteChangeSet",
                "cloudformation:DescribeChangeSet",
                "cloudformation:ExecuteChangeSet",
                "cloudformation:SetStackPolicy",
                "cloudformation:ValidateTemplate"
            ],
            "Resource": "*",
            "Effect": "Allow"
        },
        {
            "Action": [
                "codebuild:*"
            ],
            "Resource": "*",
            "Effect": "Allow"
        },
        {
            "Action": [
                "codepipeline:*"
            ],
            "Resource": "*",
            "Effect": "Allow"
        },
        {
            "Effect": "Allow",
            "Action": [
              "codestar-notifications:*",
              "codestar-connections:*"
            ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": [
              "sqs:*"
            ],
            "Resource": "${aws_sqs_queue.prod_pipeline_notification_queue.arn}"
        },
        {
            "Effect": "Allow",
            "Action": [
                "appconfig:StartDeployment",
                "appconfig:StopDeployment",
                "appconfig:GetDeployment"
            ],
            "Resource": "*"
        }
    ],
    "Version": "2012-10-17"
}
EOF
}

resource "aws_iam_role" "prod_frontend_deploy_pipeline_role" {
  name               = "prod-frontend-deploy-pipeline-role"
  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "codepipeline.amazonaws.com"
      },
      "Effect": "Allow"
    },
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "codebuild.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "prod_frontend_deploy_pipeline_attach" {
  role       = aws_iam_role.prod_frontend_deploy_pipeline_role.name
  policy_arn = aws_iam_policy.prod_frontend_deploy_pipeline_policy.arn
}


resource "aws_codepipeline" "prod_frontend_deploy_pipeline" {
  for_each = var.frontends
  name     = "prod-frontend-deploy-pipeline-${each.key}"
  role_arn = aws_iam_role.prod_frontend_deploy_pipeline_role.arn

  artifact_store {
    location = aws_s3_bucket.prod_terraform_pipeline_artifact_store.bucket
    type     = "S3"
  }

  stage {
    name = "Source"

    action {
      name             = "Source"
      category         = "Source"
      owner            = "AWS"
      provider         = "S3"
      version          = "1"
      output_artifacts = ["source_output"]
      namespace        = "Source"

      configuration = {
        S3Bucket             = aws_s3_bucket.prod_terraform_pipeline_artifact_store.bucket
        S3ObjectKey          = "frontend-builds/prod/${each.key}.zip"
        PollForSourceChanges = "false"
      }
    }
  }

  stage {
    name = "Deploy"
    action {
      name            = "Deploy"
      category        = "Build"
      owner           = "AWS"
      provider        = "CodeBuild"
      version         = "1"
      input_artifacts = ["source_output"]

      configuration = {
        ProjectName = aws_codebuild_project.prod_meta_frontend_deploy_codebuild[each.key].name
      }
    }
  }
}

// https://repost.aws/questions/QUiKyt_-5-SdW2Gia9ICH7dA/canary-deployments-for-lambda-using-codedeploy-with-codepipeline

resource "aws_codebuild_project" "prod_meta_frontend_deploy_codebuild" {
  for_each     = var.frontends
  name         = "prod-meta-frontend-deploy-codebuild-${each.key}"
  service_role = aws_iam_role.prod_frontend_deploy_pipeline_role.arn
  artifacts {
    type = "CODEPIPELINE"
  }
  environment {
    compute_type = "BUILD_GENERAL1_SMALL"
    image        = "aws/codebuild/amazonlinux2-x86_64-standard:4.0"
    type         = "LINUX_CONTAINER"
  }
  logs_config {
    cloudwatch_logs {
      status = "ENABLED"
    }
  }
  source {
    type      = "CODEPIPELINE"
    buildspec = <<EOF
version: 0.2

phases:
  build:
    commands:
      - "aws s3 cp --recursive ./ s3://${each.value.bucket}"
      - "sleep 1"
      - "aws sqs send-message --queue-url ${aws_sqs_queue.prod_pipeline_notification_queue.url} --message-body \"{\\\"completed_deploy\\\": \\\"${each.key}\\\"}\""
    EOF
  }
}

resource "aws_codestarnotifications_notification_rule" "prod_frontend_pipeline_notification_rule" {
  for_each    = var.frontends
  detail_type = "FULL"
  // https://docs.aws.amazon.com/dtconsole/latest/userguide/concepts.html#events-ref-buildproject
  event_type_ids = ["codepipeline-pipeline-stage-execution-failed"]

  name     = "prod-pipeline-notification-${each.key}"
  resource = aws_codepipeline.prod_frontend_deploy_pipeline[each.key].arn

  target {
    address = aws_sns_topic.prod_pipeline_notifications.arn
  }
}
