resource "aws_sns_topic" "prod_provider_specific_cdr_topic" {
  name = "prod-provider-specific-cdr-topic"
}

resource "aws_sns_topic" "prod_provider_agnostic_cdr_topic" {
  name = "prod-provider-agnostic-cdr-topic"
}

/* Converting lambda */
data "aws_iam_policy_document" "prod_cdr_converter_policy_data" {
  statement {
    actions = [
      "lambda:InvokeFunction",
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents",
      "sns:*",
    ]
    effect    = "Allow"
    resources = ["*"]
  }
}


resource "aws_iam_policy" "prod_cdr_converter_policy" {
  name        = "prod-cdr-converter-policy"
  path        = "/"
  description = "IAM policy for converting cdrs to a common format"
  policy      = data.aws_iam_policy_document.prod_cdr_converter_policy_data.json
}


resource "aws_iam_role_policy_attachment" "prod_cdr_converter_attach" {
  role       = aws_iam_role.prod_cdr_converter_role.name
  policy_arn = aws_iam_policy.prod_cdr_converter_policy.arn
}

resource "aws_iam_role" "prod_cdr_converter_role" {
  name = "prod_cdr_converter_role"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}


resource "aws_lambda_function" "prod_provider_specific_cdr_converter" {
  function_name                  = "prod-provider-specific-cdr-converter"
  role                           = aws_iam_role.prod_cdr_converter_role.arn
  handler                        = "lambda.provider_specific_cdr_received.lambda_handler"
  runtime                        = "python3.11"
  s3_bucket                      = var.lambda_artifact_bucket
  s3_key                         = "lambda-builds/prod/empty.zip"
  publish                        = false // Don't publish because the alias will reset the version
  timeout                        = 30
  reserved_concurrent_executions = 1

  memory_size = 256

  environment {
    variables = {
      DJANGO_SETTINGS_MODULE           = "cdr_db.lambda_settings"
      DB_USER                          = local.cdr_db_creds["DB_USER"]
      DB_PASSWORD                      = local.cdr_db_creds["DB_PASSWORD"]
      DB_HOST                          = aws_db_instance.prod_cdr_db.address
      DB_NAME                          = aws_db_instance.prod_cdr_db.db_name
      AGNOSTIC_CDR_GENERATED_TOPIC_ARN = aws_sns_topic.prod_provider_agnostic_cdr_topic.arn
      PROVIDER_SPECIFIC_CDR_TOPIC_ARN  = aws_sns_topic.prod_provider_specific_cdr_topic.arn
    }
  }
}

resource "aws_lambda_alias" "prod_cdr_converter_alias" {
  name             = "deployed"
  description      = "Deployed version"
  function_name    = aws_lambda_function.prod_provider_specific_cdr_converter.arn
  function_version = "1"
  lifecycle {
    ignore_changes = [
      function_version
    ]
  }
}

/* lambda end */

resource "aws_sns_topic_subscription" "prod_cdr_conversion_subscription" {
  topic_arn = aws_sns_topic.prod_provider_specific_cdr_topic.arn
  protocol  = "lambda"
  endpoint  = aws_lambda_function.prod_provider_specific_cdr_converter.arn
}



/* Receiving lambda */
data "aws_iam_policy_document" "prod_agnostic_cdr_receiver_policy_data" {
  statement {
    actions = [
      "lambda:InvokeFunction",
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents",
      "sns:*",
    ]
    effect    = "Allow"
    resources = ["*"]
  }
}


resource "aws_iam_policy" "prod_agnostic_cdr_receiver_policy" {
  name        = "prod-cdr-receiver-policy"
  path        = "/"
  description = "IAM policy for receiving cdrs in a common format"
  policy      = data.aws_iam_policy_document.prod_agnostic_cdr_receiver_policy_data.json
}


resource "aws_iam_role_policy_attachment" "prod_agnostic_cdr_receiver_attach" {
  role       = aws_iam_role.prod_agnostic_cdr_receiver_role.name
  policy_arn = aws_iam_policy.prod_agnostic_cdr_receiver_policy.arn
}

resource "aws_iam_role" "prod_agnostic_cdr_receiver_role" {
  name = "prod_agnostic_cdr_receiver_role"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}


resource "aws_lambda_function" "prod_agnostic_cdr_receiver" {
  function_name                  = "prod-agnostic-cdr-receiver"
  role                           = aws_iam_role.prod_agnostic_cdr_receiver_role.arn
  handler                        = "lambda.agnostic_specific_cdr_received.lambda_handler"
  runtime                        = "python3.11"
  s3_bucket                      = var.lambda_artifact_bucket
  s3_key                         = "lambda-builds/prod/empty.zip"
  publish                        = false // Don't publish because the alias will reset the version
  timeout                        = 30
  reserved_concurrent_executions = 1

  memory_size = 256

  environment {
    variables = {
      DJANGO_SETTINGS_MODULE           = "cdr_db.lambda_settings"
      DB_USER                          = local.cdr_db_creds["DB_USER"]
      DB_PASSWORD                      = local.cdr_db_creds["DB_PASSWORD"]
      DB_HOST                          = aws_db_instance.prod_cdr_db.address
      DB_NAME                          = aws_db_instance.prod_cdr_db.db_name
      AGNOSTIC_CDR_GENERATED_TOPIC_ARN = aws_sns_topic.prod_provider_agnostic_cdr_topic.arn
      PROVIDER_SPECIFIC_CDR_TOPIC_ARN  = aws_sns_topic.prod_provider_specific_cdr_topic.arn
    }
  }
}

resource "aws_lambda_alias" "prod_agnostic_cdr_receiver_alias" {
  name             = "deployed"
  description      = "Deployed version"
  function_name    = aws_lambda_function.prod_agnostic_cdr_receiver.arn
  function_version = "1"
  lifecycle {
    ignore_changes = [
      function_version
    ]
  }
}

/* lambda end */


resource "aws_sns_topic_subscription" "prod_cdr_receiver_subscription" {
  topic_arn = aws_sns_topic.prod_provider_agnostic_cdr_topic.arn
  protocol  = "lambda"
  endpoint  = aws_lambda_function.prod_agnostic_cdr_receiver.arn
}
