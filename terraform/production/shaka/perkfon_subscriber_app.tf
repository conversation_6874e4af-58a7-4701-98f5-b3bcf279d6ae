# AWS S3 bucket for static hosting
resource "aws_s3_bucket" "prod_perkfon_subscriber_app_dashboard_frontend" {
  bucket = "prod-perkfon-subscriber-app-dashboard-frontend"

}

resource "aws_s3_bucket_policy" "prod_perkfon_subscriber_app_dashboard_frontend_bucket_policy" {
  bucket = aws_s3_bucket.prod_perkfon_subscriber_app_dashboard_frontend.id
  policy = <<EOF
{
  "Version": "2008-10-17",
  "Statement": [
    {
      "Sid": "PublicReadForGetBucketObjects",
      "Effect": "Allow",
      "Principal": {
        "AWS": "*"
      },
      "Action": "s3:GetObject",
      "Resource": "arn:aws:s3:::prod-perkfon-subscriber-app-dashboard-frontend/*"
    }
  ]
}
EOF

}

resource "aws_s3_bucket_public_access_block" "prod_perkfon_subscriber_app_dashboard_frontend_pulic_policy" {
  bucket = aws_s3_bucket.prod_perkfon_subscriber_app_dashboard_frontend.id

  block_public_acls       = false
  block_public_policy     = false
  ignore_public_acls      = false
  restrict_public_buckets = false
}

resource "aws_s3_bucket_website_configuration" "prod_perkfon_subscriber_app_dashboard_frontend_configuration" {
  bucket = aws_s3_bucket.prod_perkfon_subscriber_app_dashboard_frontend.id

  index_document {
    suffix = "index.html"
  }

  error_document {
    key = "error.html"
  }
}

resource "aws_acm_certificate" "prod_perkfon_subscriber_app_dashboard_frontend" {
  provider          = aws.use1
  domain_name       = "perkfon-app.shaka.tel"
  validation_method = "DNS"
}

resource "aws_route53_record" "prod_perkfon_subscriber_app_dashboard_frontend_dns" {
  allow_overwrite = true
  name            = "perkfon-app"
  type            = "A"
  zone_id         = "Z00399212MJ5MNUCH698X"
  alias {
    name                   = aws_cloudfront_distribution.prod-perkfon-subscriber-app-dashboard-frontend-distribution.domain_name
    zone_id                = aws_cloudfront_distribution.prod-perkfon-subscriber-app-dashboard-frontend-distribution.hosted_zone_id
    evaluate_target_health = true
  }
}

resource "aws_route53_record" "prod_perkfon_subscriber_app_dashboard_frontend" {
  for_each = {
    for dvo in aws_acm_certificate.prod_perkfon_subscriber_app_dashboard_frontend.domain_validation_options : dvo.domain_name => {
      name   = dvo.resource_record_name
      record = dvo.resource_record_value
      type   = dvo.resource_record_type
    }
  }

  allow_overwrite = true
  name            = each.value.name
  records         = [each.value.record]
  ttl             = 60
  type            = each.value.type
  zone_id         = "Z00399212MJ5MNUCH698X"
}

resource "aws_acm_certificate_validation" "prod_perkfon_subscriber_app_dashboard_frontend" {
  certificate_arn         = aws_acm_certificate.prod_perkfon_subscriber_app_dashboard_frontend.arn
  validation_record_fqdns = [for record in aws_route53_record.prod_perkfon_subscriber_app_dashboard_frontend : record.fqdn]
  provider                = aws.use1
}

resource "aws_cloudfront_distribution" "prod-perkfon-subscriber-app-dashboard-frontend-distribution" {
  origin {
    domain_name = "${aws_s3_bucket.prod_perkfon_subscriber_app_dashboard_frontend.bucket}.s3.amazonaws.com"
    origin_id   = "prod-perkfon-subscriber-app-dashboard-frontend"
  }

  enabled             = true
  is_ipv6_enabled     = true
  comment             = "Managed by Terraform"
  default_root_object = "index.html"

  aliases = ["app.perkfon.com"]

  default_cache_behavior {
    allowed_methods  = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
    cached_methods   = ["GET", "HEAD"]
    target_origin_id = "prod-perkfon-subscriber-app-dashboard-frontend"

    forwarded_values {
      query_string = false

      cookies {
        forward = "none"
      }
    }

    viewer_protocol_policy = "redirect-to-https"
    min_ttl                = 0
    default_ttl            = 3600
    max_ttl                = 86400
  }

  price_class = "PriceClass_100"

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  viewer_certificate {
    acm_certificate_arn      = "arn:aws:acm:us-east-1:727907215122:certificate/eaf124d9-9a39-4d6c-bb25-8aaa9beae2f0"
    ssl_support_method       = "sni-only"
    minimum_protocol_version = "TLSv1"
  }

  custom_error_response {
    error_code         = "403"
    response_page_path = "/"
    response_code      = "200"
  }

}
