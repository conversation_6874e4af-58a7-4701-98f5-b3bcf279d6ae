terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.28"
      configuration_aliases = [
        aws.use1,
      ]

    }
  }
}

variable "lambda_artifact_bucket" {
  type = string
}


locals {
  transatel_sftp_creds   = sensitive(yamldecode(data.aws_kms_secrets.prod_transatel_sftp_creds.plaintext["sftp-creds"]))
  gamma_sftp_creds       = sensitive(yamldecode(data.aws_kms_secrets.prod_gamma_sftp_creds.plaintext["sftp-creds"]))
  cdr_db_creds           = sensitive(yamldecode(data.aws_kms_secrets.prod_cdr_db_creds.plaintext["cdr-db-creds"]))
  client_dashboard_creds = sensitive(yamldecode(data.aws_kms_secrets.prod_client_dashboard_creds.plaintext["client-dashboard-creds"]))
  nexus_creds            = sensitive(yamldecode(data.aws_kms_secrets.prod_nexus_creds.plaintext["nexus-creds"]))
  simpnexus_creds        = sensitive(yamldecode(data.aws_kms_secrets.prod_simpnexus_creds.plaintext["simpnexus-creds"]))
  sandbox_creds          = sensitive(yamldecode(data.aws_kms_secrets.prod_sandbox_creds.plaintext["sandbox-creds"]))
}

data "aws_kms_secrets" "prod_transatel_sftp_creds" {
  secret {
    name    = "sftp-creds"
    payload = file("${path.module}/transatel_sftp.yml.encrypted")
    key_id  = "e73d0e08-6282-47b4-a049-5648fb584809"
  }
}

data "aws_kms_secrets" "prod_gamma_sftp_creds" {
  secret {
    name    = "sftp-creds"
    payload = file("${path.module}/gamma.yml.encrypted")
    key_id  = "e73d0e08-6282-47b4-a049-5648fb584809"
  }
}

data "aws_kms_secrets" "prod_cdr_db_creds" {
  secret {
    name    = "cdr-db-creds"
    payload = file("${path.module}/cdr_db.yml.encrypted")
    key_id  = "e73d0e08-6282-47b4-a049-5648fb584809"
  }
}

data "aws_kms_secrets" "prod_client_dashboard_creds" {
  secret {
    name    = "client-dashboard-creds"
    payload = file("${path.module}/client_dashboard.yml.encrypted")
    key_id  = "e73d0e08-6282-47b4-a049-5648fb584809"
  }
}

data "aws_kms_secrets" "prod_nexus_creds" {
  secret {
    name    = "nexus-creds"
    payload = file("${path.module}/nexus.yml.encrypted")
    key_id  = "e73d0e08-6282-47b4-a049-5648fb584809"
  }
}

data "aws_kms_secrets" "prod_simpnexus_creds" {
  secret {
    name    = "simpnexus-creds"
    payload = file("${path.module}/simpnexus.yml.encrypted")
    key_id  = "e73d0e08-6282-47b4-a049-5648fb584809"
  }
}

data "aws_kms_secrets" "prod_sandbox_creds" {
  secret {
    name    = "sandbox-creds"
    payload = file("${path.module}/sandbox.yml.encrypted")
    key_id  = "e73d0e08-6282-47b4-a049-5648fb584809"
  }
}
