resource "aws_iam_role" "prod_sfn_iam_role" {
  name = "prod-sfn-iam-role"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "states.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })
  inline_policy {
    name = "prod-sfn-iam-policy"
    policy = jsonencode({
      Version = "2012-10-17"
      Statement = [
        {
          Effect = "Allow"
          Action = [
            "lambda:InvokeFunction"
          ]
          Resource = "*"
        }
      ]
    })
  }
}

resource "aws_sfn_state_machine" "prod_plan_upgrade_state_machine" {
  name     = "prod-plan-upgrade"
  role_arn = aws_iam_role.prod_sfn_iam_role.arn
  publish  = true

  definition = templatefile("${path.module}/../../../lambda/shaka/nexus/state-machines/plan_upgrade.json", {
    slack_debug_fn_arn = aws_lambda_function.prod_slack_debug.arn,
    plan_change_fn_arn = aws_lambda_function.prod_plan_change.arn,
  })
}


resource "aws_sfn_state_machine" "prod_plan_downgrade_state_machine" {
  name     = "prod-plan-downgrade"
  role_arn = aws_iam_role.prod_sfn_iam_role.arn
  publish  = true

  definition = templatefile("${path.module}/../../../lambda/shaka/nexus/state-machines/plan_downgrade.json", {
    slack_debug_fn_arn = aws_lambda_function.prod_slack_debug.arn,
    plan_change_fn_arn = aws_lambda_function.prod_plan_change.arn,
  })
}

resource "aws_sfn_state_machine" "prod_plan_change_cancel_state_machine" {
  name     = "prod-plan-change-cancel"
  role_arn = aws_iam_role.prod_sfn_iam_role.arn
  publish  = true

  definition = templatefile("${path.module}/../../../lambda/shaka/nexus/state-machines/plan_change_cancel.json", {
    slack_debug_fn_arn = aws_lambda_function.prod_slack_debug.arn,
    plan_change_fn_arn = aws_lambda_function.prod_plan_change.arn,
  })
}




resource "aws_sfn_state_machine" "prod_cancel_subscription_state_machine" {
  name     = "prod-cancel-subscription"
  role_arn = aws_iam_role.prod_sfn_iam_role.arn
  publish  = true

  definition = templatefile("${path.module}/../../../lambda/shaka/nexus/state-machines/cancel_subscription.json", {
    slack_debug_fn_arn = aws_lambda_function.prod_slack_debug.arn,
    plan_change_fn_arn = aws_lambda_function.prod_plan_change.arn,
  })
}
