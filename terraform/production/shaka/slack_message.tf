data "aws_iam_policy_document" "prod_slack_messages_policy_data" {
  statement {
    effect = "Allow"

    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents",
    ]

    resources = ["arn:aws:logs:*:*:*"]
  }

  statement {
    actions   = ["ec2:CreateNetworkInterface", "ec2:DescribeNetworkInterfaces", "ec2:DeleteNetworkInterface", "ec2:AssignPrivateIpAddresses", "ec2:UnassignPrivateIpAddresses"]
    effect    = "Allow"
    resources = ["*"]
  }

}

resource "aws_iam_policy" "prod_slack_messages_policy" {
  name        = "prod-slack-messages-policy"
  path        = "/"
  description = "IAM policy for the dangerous users"
  policy      = data.aws_iam_policy_document.prod_slack_messages_policy_data.json
}


resource "aws_iam_role_policy_attachment" "prod_slack_messages_attach" {
  role       = aws_iam_role.prod_slack_messages_role.name
  policy_arn = aws_iam_policy.prod_slack_messages_policy.arn
}

resource "aws_iam_role" "prod_slack_messages_role" {
  name = "prod_slack_messages_role"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

resource "aws_lambda_function" "prod_slack_messages" {
  function_name                  = "prod-slack-messages"
  role                           = aws_iam_role.prod_slack_messages_role.arn
  handler                        = "lambda.slack_message_posted.lambda_handler"
  runtime                        = "python3.11"
  s3_bucket                      = var.lambda_artifact_bucket
  s3_key                         = "lambda-builds/prod/empty.zip"
  publish                        = false // Don't publish because the alias will reset the version
  timeout                        = 60
  reserved_concurrent_executions = 1

  memory_size = 256

  environment {
    variables = {
      SLACK_TOPIC_ARN         = aws_sns_topic.prod_slack_messages_topic.arn
      SLACK_WEBHOOK_URL       = "*********************************************************************************"
      SLACK_DEBUG_WEBHOOK_URL = "*********************************************************************************"
      DJANGO_SETTINGS_MODULE  = "nexus.lambda_settings"
    }
  }
}

resource "aws_lambda_alias" "prod_slack_messages_alias" {
  name             = "deployed"
  description      = "Deployed version"
  function_name    = aws_lambda_function.prod_slack_messages.arn
  function_version = "1"
  lifecycle {
    ignore_changes = [
      function_version
    ]
  }
}



resource "aws_sns_topic" "prod_slack_messages_topic" {
  name = "prod-slack-messages-topic"
}

resource "aws_lambda_permission" "prod_slack_messages_allow_sns" {
  statement_id  = "AllowExecutionFromSNS"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.prod_slack_messages.function_name
  principal     = "sns.amazonaws.com"
  source_arn    = aws_sns_topic.prod_slack_messages_topic.arn
}

resource "aws_sns_topic_subscription" "lambda_subscription" {
  topic_arn = aws_sns_topic.prod_slack_messages_topic.arn
  protocol  = "lambda"
  endpoint  = aws_lambda_function.prod_slack_messages.arn
}
