resource "aws_sns_topic" "prod_send_webhook_topic" {
  name = "prod-send-webhook-topic"
}

resource "aws_iam_role" "prod_send_webhook_role" {
  name = "prod_send_webhook_role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })
}

data "aws_iam_policy_document" "prod_send_webhook_policy_data" {
  statement {
    effect = "Allow"
    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents",
    ]
    resources = ["arn:aws:logs:*:*:*"]
  }

  statement {
    effect = "Allow"
    actions = [
      "sns:*"
    ]
    resources = [aws_sns_topic.prod_send_webhook_topic.arn]
  }
}


resource "aws_iam_policy" "prod_send_webhook_policy" {
  name        = "prod-send-webhook-policy"
  path        = "/"
  description = "IAM policy for send webhook Lambda"
  policy      = data.aws_iam_policy_document.prod_send_webhook_policy_data.json
}

resource "aws_iam_role_policy_attachment" "prod_send_webhook_attach" {
  role       = aws_iam_role.prod_send_webhook_role.name
  policy_arn = aws_iam_policy.prod_send_webhook_policy.arn
}


resource "aws_lambda_function" "prod_send_webhook_function" {
  function_name                  = "prod-send-webhook-function"
  role                           = aws_iam_role.prod_send_webhook_role.arn
  handler                        = "send_webhook.lambda_handler"
  runtime                        = "python3.11"
  s3_bucket                      = var.lambda_artifact_bucket
  s3_key                         = "lambda-builds/prod/empty.zip"
  publish                        = false // Don't publish because the alias will reset the version
  timeout                        = 60
  reserved_concurrent_executions = 1

  memory_size = 256

  environment {
  }
}

resource "aws_lambda_alias" "prod_send_webhook_alias" {
  name             = "deployed"
  description      = "Deployed version"
  function_name    = aws_lambda_function.prod_send_webhook_function.arn
  function_version = "1"
  lifecycle {
    ignore_changes = [
      function_version
    ]
  }
}


resource "aws_lambda_permission" "prod_send_webhook_allow_sns" {
  statement_id  = "AllowExecutionFromSNS"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.prod_send_webhook_function.function_name
  principal     = "sns.amazonaws.com"
  source_arn    = aws_sns_topic.prod_send_webhook_topic.arn
}

resource "aws_sns_topic_subscription" "prod_send_webhook_subscription" {
  topic_arn = aws_sns_topic.prod_send_webhook_topic.arn
  protocol  = "lambda"
  endpoint  = aws_lambda_function.prod_send_webhook_function.arn
}
