resource "aws_eip" "prod_millwall_smtp_proxy_ip" {
  domain = "vpc"

  lifecycle {
    prevent_destroy = true
  }
}

resource "aws_security_group" "prod_millwall_smtp_proxy_sg" {
  name        = "prod-millwall-smtp-proxy-sg"
  description = "Allow SMTP and SSH access"

  ingress {
    from_port   = 25
    to_port     = 25
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    from_port   = 465
    to_port     = 465
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    from_port   = 587
    to_port     = 587
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}


resource "aws_instance" "prod_millwall_smtp_proxy" {
  ami             = "ami-091f18e98bc129c4e"
  instance_type   = "t3.micro"
  security_groups = [aws_security_group.prod_millwall_smtp_proxy_sg.name]

  user_data = file("shaka/millwall_smtp_proxy_init.sh")

  tags = {
    Name = "Millwall SMTP Proxy"
  }
}


resource "aws_eip_association" "prod_millwall_smtp_proxy_eip_assoc" {
  instance_id   = aws_instance.prod_millwall_smtp_proxy.id
  allocation_id = aws_eip.prod_millwall_smtp_proxy_ip.id
}
