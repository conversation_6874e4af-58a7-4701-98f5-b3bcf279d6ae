resource "aws_elastic_beanstalk_application" "prod_nexus_eb_app" {
  name = "prod-nexus-eb-app"
}

resource "aws_cloudwatch_log_group" "prod_nexus_eb_log_group" {
  name              = "/aws/elasticbeanstalk/prod-nexus-eb-env/django"
  retention_in_days = 7
}

resource "aws_elastic_beanstalk_environment" "prod_nexus_eb_env" {
  name                = "prod-nexus-eb-env"
  application         = aws_elastic_beanstalk_application.prod_nexus_eb_app.name
  solution_stack_name = "64bit Amazon Linux 2023 v4.0.6 running Python 3.11"

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "DJANGO_SETTINGS_MODULE"
    value     = "nexus.production_settings"
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "DB_USER"
    value     = local.nexus_creds["DB_USER"]
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "DB_PASSWORD"
    value     = local.nexus_creds["DB_PASSWORD"]
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "DB_HOST"
    value     = aws_db_instance.prod_nexus.address
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "DB_NAME"
    value     = aws_db_instance.prod_nexus.db_name
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "CDR_DB_API_TOKEN"
    value     = local.cdr_db_creds["NEXUS_API_TOKEN"] # Not a bug, it's the nexus token for cdr, needs renaming
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "LAMBDA_API_TOKEN"
    value     = local.nexus_creds["LAMBDA_API_TOKEN"]
    resource  = ""
  }


  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "DELEGATE_API_TOKEN"
    value     = local.nexus_creds["DELEGATE_API_TOKEN"]
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "TRANSATEL_WEBHOOK_SECRET_KEY"
    value     = local.nexus_creds["TRANSATEL_WEBHOOK_SECRET_KEY"]
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "BETA_TRANSATEL_WEBHOOK_SECRET_KEY"
    value     = local.nexus_creds["BETA_TRANSATEL_WEBHOOK_SECRET_KEY"]
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "YAYZI_TRANSATEL_WEBHOOK_SECRET_KEY"
    value     = local.nexus_creds["YAYZI_TRANSATEL_WEBHOOK_SECRET_KEY"]
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "GAMMA_WEBHOOK_SECRET_KEY"
    value     = local.nexus_creds["GAMMA_WEBHOOK_SECRET_KEY"]
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "PLAN_UPGRADE_STATE_MACHINE_ARN"
    value     = aws_sfn_state_machine.prod_plan_upgrade_state_machine.arn
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "PLAN_DOWNGRADE_STATE_MACHINE_ARN"
    value     = aws_sfn_state_machine.prod_plan_downgrade_state_machine.arn
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "CANCEL_PLAN_CHANGE_STATE_MACHINE_ARN"
    value     = aws_sfn_state_machine.prod_plan_change_cancel_state_machine.arn
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "CANCEL_SUBSCRIPTION_STATE_MACHINE_ARN"
    value     = aws_sfn_state_machine.prod_cancel_subscription_state_machine.arn
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "SLACK_MESSAGE_ARN"
    value     = aws_sns_topic.prod_slack_messages_topic.arn
    resource  = ""
  }


  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "SIM_RECONCILIATION_TOPIC_ARN"
    value     = aws_sns_topic.prod_sim_reconciliation_topic.arn
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "SEND_WEBHOOK_TOPIC_ARN"
    value     = aws_sns_topic.prod_send_webhook_topic.arn
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "NEXTUS_ADMIN_KEY"
    value     = local.nexus_creds["NEXTUS_ADMIN_KEY"]
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "ASYNCAPI_REDIS_WRITER_PASSWORD"
    value     = local.nexus_creds["ASYNCAPI_REDIS_WRITER_PASSWORD"]
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "ASYNCAPI_REDIS_WRITER_USERNAME"
    value     = "rw-user"
    resource  = ""
  }


  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "ASYNCAPI_REDIS_HOST"
    value     = aws_elasticache_replication_group.prod_asyncapi_redis_replication_group.primary_endpoint_address
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "TELNA_API_KEY"
    value     = local.nexus_creds["TELNA_API_KEY"]
    resource  = ""
  }


  setting {
    namespace = "aws:ec2:instances"
    name      = "InstanceTypes"
    value     = "t2.medium"
    resource  = ""
  }
  setting {
    namespace = "aws:elasticbeanstalk:cloudwatch:logs"
    name      = "StreamLogs"
    value     = "true"
    resource  = ""
  }

  setting {
    namespace = "aws:autoscaling:asg"
    name      = "MaxSize"
    value     = 8
    resource  = ""
  }

  setting {
    namespace = "aws:autoscaling:asg"
    name      = "MinSize"
    value     = 4
    resource  = ""
  }

  setting {
    namespace = "aws:autoscaling:launchconfiguration"
    name      = "IamInstanceProfile"
    value     = aws_iam_instance_profile.prod_eb_profile.name
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:container:python"
    name      = "WSGIPath"
    value     = "nexus.wsgi:application"
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:environment"
    name      = "LoadBalancerType"
    value     = "application"
    resource  = ""
  }

  setting {
    namespace = "aws:elbv2:listener:443"
    name      = "ListenerEnabled"
    value     = "true"
    resource  = ""
  }

  setting {
    namespace = "aws:elbv2:listener:443"
    name      = "Protocol"
    value     = "HTTPS"
    resource  = ""
  }

  setting {
    namespace = "aws:elbv2:listener:443"
    name      = "SSLCertificateArns"
    value     = "arn:aws:acm:eu-west-2:************:certificate/f8c81598-cf63-453d-9ac9-afa7e92c5927"
    resource  = ""
  }
}


# https://epam.github.io/edp-install/operator-guide/waf-tf-configuration/
resource "aws_wafv2_regex_pattern_set" "prod_nexus_host_regex" {
  name  = "prod-nexus-host-regex"
  scope = "REGIONAL"

  regular_expression {
    regex_string = "nexus.shaka.tel"
  }
}

resource "aws_wafv2_web_acl" "prod_nexus_host_acl" {
  name  = "prod-nexus-host-acl"
  scope = "REGIONAL"

  default_action {
    block {}
  }

  rule {
    name     = "AWS-AWSManagedRulesCommonRuleSet"
    priority = 1

    override_action {
      none {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesCommonRuleSet"
        vendor_name = "AWS"
      }
    }


    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "AWS-AWSManagedRulesCommonRuleSet"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AWS-AWSManagedRulesLinuxRuleSet"
    priority = 2

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesLinuxRuleSet"
        vendor_name = "AWS"
      }
    }

    override_action {
      none {}
    }


    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "AWS-AWSManagedRulesLinuxRuleSet"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AWS-AWSManagedRulesKnownBadInputsRuleSet"
    priority = 3

    override_action {
      none {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesKnownBadInputsRuleSet"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "AWS-AWSManagedRulesKnownBadInputsRuleSet"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "PreventHostInjections"
    priority = 0

    statement {
      regex_pattern_set_reference_statement {
        arn = aws_wafv2_regex_pattern_set.prod_nexus_host_regex.arn

        field_to_match {
          single_header {
            name = "host"
          }
        }

        text_transformation {
          priority = 0
          type     = "NONE"
        }
      }
    }

    action {
      allow {}
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "PreventHostInjections"
      sampled_requests_enabled   = true
    }
  }

  visibility_config {
    cloudwatch_metrics_enabled = true
    metric_name                = "prod-nexus-host-acl"
    sampled_requests_enabled   = true
  }
}

resource "aws_wafv2_web_acl_association" "prod_waf_nexus_alb" {
  resource_arn = aws_elastic_beanstalk_environment.prod_nexus_eb_env.load_balancers[0]
  web_acl_arn  = aws_wafv2_web_acl.prod_nexus_host_acl.arn
}



resource "aws_db_instance" "prod_nexus" {
  allocated_storage   = 10
  db_name             = "nexus"
  engine              = "postgres"
  engine_version      = "15.12"
  instance_class      = "db.t3.small"
  username            = local.nexus_creds["DB_USER"]
  password            = local.nexus_creds["DB_PASSWORD"]
  identifier          = "prod-nexus"
  deletion_protection = true
  publicly_accessible = true
  lifecycle {
    prevent_destroy = true
  }
  backup_retention_period = 7
}
