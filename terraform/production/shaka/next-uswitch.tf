resource "aws_elastic_beanstalk_application" "prod_next_uswitch_eb_app" {
  name = "prod-next-uswitch-eb-app"
}


resource "aws_elastic_beanstalk_environment" "prod_next_uswitch_eb_env" {
  name                = "prod-next-uswitch-eb-env"
  application         = aws_elastic_beanstalk_application.prod_next_uswitch_eb_app.name
  solution_stack_name = "64bit Amazon Linux 2023 v6.4.1 running Node.js 22"

  setting {
    namespace = "aws:ec2:instances"
    name      = "InstanceTypes"
    value     = "t2.small"
    resource  = ""
  }
  setting {
    namespace = "aws:elasticbeanstalk:cloudwatch:logs"
    name      = "StreamLogs"
    value     = "true"
    resource  = ""
  }

  setting {
    namespace = "aws:autoscaling:asg"
    name      = "MaxSize"
    value     = 1
    resource  = ""
  }

  setting {
    namespace = "aws:autoscaling:asg"
    name      = "MinSize"
    value     = 1
    resource  = ""
  }

  setting {
    namespace = "aws:autoscaling:launchconfiguration"
    name      = "IamInstanceProfile"
    value     = aws_iam_instance_profile.prod_eb_profile.name
    resource  = ""
  }


  setting {
    namespace = "aws:elasticbeanstalk:environment"
    name      = "LoadBalancerType"
    value     = "application"
    resource  = ""
  }

  setting {
    namespace = "aws:elbv2:listener:443"
    name      = "ListenerEnabled"
    value     = "true"
    resource  = ""
  }

  setting {
    namespace = "aws:elbv2:listener:443"
    name      = "Protocol"
    value     = "HTTPS"
    resource  = ""
  }

  setting {
    namespace = "aws:elbv2:listener:443"
    name      = "SSLCertificateArns"
    value     = "arn:aws:acm:eu-west-2:************:certificate/047739f2-65bc-4923-bfe0-ec5a114b676a"
    resource  = ""
  }
}


# https://epam.github.io/edp-install/operator-guide/waf-tf-configuration/
resource "aws_wafv2_regex_pattern_set" "prod_next_uswitch_host_regex" {
  name  = "prod-next-uswitch-host-regex"
  scope = "REGIONAL"

  regular_expression {
    regex_string = "fr28ghi21.shaka.tel"
  }
}

resource "aws_wafv2_web_acl" "prod_next_uswitch_host_acl" {
  name  = "prod-next-uswitch-host-acl"
  scope = "REGIONAL"

  default_action {
    block {}
  }

  rule {
    name     = "AWS-AWSManagedRulesCommonRuleSet"
    priority = 1

    override_action {
      none {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesCommonRuleSet"
        vendor_name = "AWS"
      }
    }


    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "AWS-AWSManagedRulesCommonRuleSet"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AWS-AWSManagedRulesLinuxRuleSet"
    priority = 2

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesLinuxRuleSet"
        vendor_name = "AWS"
      }
    }

    override_action {
      none {}
    }


    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "AWS-AWSManagedRulesLinuxRuleSet"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AWS-AWSManagedRulesKnownBadInputsRuleSet"
    priority = 3

    override_action {
      none {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesKnownBadInputsRuleSet"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "AWS-AWSManagedRulesKnownBadInputsRuleSet"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "PreventHostInjections"
    priority = 0

    statement {
      regex_pattern_set_reference_statement {
        arn = aws_wafv2_regex_pattern_set.prod_next_uswitch_host_regex.arn

        field_to_match {
          single_header {
            name = "host"
          }
        }

        text_transformation {
          priority = 0
          type     = "NONE"
        }
      }
    }

    action {
      allow {}
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "PreventHostInjections"
      sampled_requests_enabled   = true
    }
  }

  visibility_config {
    cloudwatch_metrics_enabled = true
    metric_name                = "prod-next-uswitch-host-acl"
    sampled_requests_enabled   = true
  }
}

resource "aws_wafv2_web_acl_association" "prod_waf_next_uswitch_alb" {
  resource_arn = aws_elastic_beanstalk_environment.prod_next_uswitch_eb_env.load_balancers[0]
  web_acl_arn  = aws_wafv2_web_acl.prod_next_uswitch_host_acl.arn
}
