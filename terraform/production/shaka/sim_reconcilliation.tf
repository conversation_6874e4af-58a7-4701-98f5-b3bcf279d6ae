resource "aws_sns_topic" "prod_sim_reconciliation_topic" {
  name = "prod-sim-reconciliation-topic"
}


resource "aws_s3_bucket" "prod_sim_reconciliation_data" {
  bucket = "prod-sim-reconciliation-data"
}

resource "aws_s3_bucket_versioning" "prod_sim_reconciliation_data_versioning" {
  bucket = aws_s3_bucket.prod_sim_reconciliation_data.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_iam_role" "prod_sim_reconciliation_s3_writer_role" {
  name = "prod_sim_reconciliation_s3_writer_role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })
}

data "aws_iam_policy_document" "prod_sim_reconciliation_s3_writer_policy_data" {
  statement {
    effect = "Allow"
    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents",
    ]
    resources = ["arn:aws:logs:*:*:*"]
  }

  statement {
    effect = "Allow"
    actions = [
      "s3:PutObject",
      "s3:PutObjectAcl",
      "s3:GetObject",
      "s3:ListBucket"
    ]
    resources = [
      aws_s3_bucket.prod_sim_reconciliation_data.arn,
      "${aws_s3_bucket.prod_sim_reconciliation_data.arn}/*"
    ]
  }

  statement {
    effect = "Allow"
    actions = [
      "sns:Receive",
      "sns:GetTopicAttributes"
    ]
    resources = [aws_sns_topic.prod_sim_reconciliation_topic.arn]
  }
}


resource "aws_iam_policy" "prod_sim_reconciliation_s3_writer_policy" {
  name        = "prod-sim-reconciliation-s3-writer-policy"
  path        = "/"
  description = "IAM policy for SIM reconciliation S3 writer Lambda"
  policy      = data.aws_iam_policy_document.prod_sim_reconciliation_s3_writer_policy_data.json
}

resource "aws_iam_role_policy_attachment" "prod_sim_reconciliation_s3_writer_attach" {
  role       = aws_iam_role.prod_sim_reconciliation_s3_writer_role.name
  policy_arn = aws_iam_policy.prod_sim_reconciliation_s3_writer_policy.arn
}




resource "aws_lambda_function" "prod_sim_reconciliation_s3_writer" {
  function_name                  = "prod-sim-reconciliation-s3-writer"
  role                           = aws_iam_role.prod_sim_reconciliation_s3_writer_role.arn
  handler                        = "sim_reconciliation_s3_writer.lambda_handler"
  runtime                        = "python3.11"
  s3_bucket                      = var.lambda_artifact_bucket
  s3_key                         = "lambda-builds/prod/empty.zip"
  publish                        = false // Don't publish because the alias will reset the version
  timeout                        = 60
  reserved_concurrent_executions = 1

  memory_size = 256

  environment {
    variables = {
      S3_BUCKET_NAME = aws_s3_bucket.prod_sim_reconciliation_data.bucket
    }
  }
}

resource "aws_lambda_alias" "prod_sim_reconciliation_s3_writer_alias" {
  name             = "deployed"
  description      = "Deployed version"
  function_name    = aws_lambda_function.prod_sim_reconciliation_s3_writer.arn
  function_version = "1"
  lifecycle {
    ignore_changes = [
      function_version
    ]
  }
}


resource "aws_lambda_permission" "prod_sim_reconciliation_s3_writer_allow_sns" {
  statement_id  = "AllowExecutionFromSNS"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.prod_sim_reconciliation_s3_writer.function_name
  principal     = "sns.amazonaws.com"
  source_arn    = aws_sns_topic.prod_sim_reconciliation_topic.arn
}

resource "aws_sns_topic_subscription" "prod_sim_reconciliation_s3_writer_subscription" {
  topic_arn = aws_sns_topic.prod_sim_reconciliation_topic.arn
  protocol  = "lambda"
  endpoint  = aws_lambda_function.prod_sim_reconciliation_s3_writer.arn
}
