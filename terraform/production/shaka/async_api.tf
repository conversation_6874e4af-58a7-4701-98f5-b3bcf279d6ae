resource "aws_elasticache_user_group" "prod_asyncapi_redis_user_group" {
  user_group_id = "prod-asyncapi-redis-user-group"
  engine        = "REDIS"
  user_ids = [
    aws_elasticache_user.prod_asyncapi_writer_user.user_id,
    aws_elasticache_user.prod_asyncapi_reader_user.user_id,
    aws_elasticache_user.prod_asyncapi_default_user.user_id
  ]
}

resource "aws_elasticache_user" "prod_asyncapi_default_user" {
  user_id       = "prod-asyncapi-default-user"
  user_name     = "default"
  access_string = "on ~app::* -@all +@read +@hash +@bitmap +@geo -setbit -bitfield -hset -hsetnx -hmset -hincrby -hincrbyfloat -hdel -bitop -geoadd -georadius -georadiusbymember"
  engine        = "REDIS"
  passwords     = [local.nexus_creds["ASYNCAPI_REDIS_WRITER_PASSWORD"]]
}

resource "aws_elasticache_user" "prod_asyncapi_writer_user" {
  user_id              = "prod-asyncapi-writer-user"
  user_name            = "rw-user"
  engine               = "REDIS"
  passwords            = [local.nexus_creds["ASYNCAPI_REDIS_WRITER_PASSWORD"]]
  access_string        = "on ~* +@all" # full access
  no_password_required = false
}

resource "aws_elasticache_user" "prod_asyncapi_reader_user" {
  user_id              = "prod-asyncapi-reader-user"
  user_name            = "ro-user"
  engine               = "REDIS"
  passwords            = [local.nexus_creds["ASYNCAPI_REDIS_READER_PASSWORD"]]
  access_string        = "on ~* -@all +@read" # read-only
  no_password_required = false
}

resource "aws_elasticache_replication_group" "prod_asyncapi_redis_replication_group" {
  replication_group_id       = "prod-asyncapi-redis-replication-group"
  description                = "Production AsyncAPI Redis Replication Group"
  node_type                  = "cache.t3.micro"
  num_cache_clusters         = 1
  automatic_failover_enabled = false
  engine                     = "redis"
  engine_version             = "7.0"
  port                       = 6379
  user_group_ids             = [aws_elasticache_user_group.prod_asyncapi_redis_user_group.id]
  multi_az_enabled           = false
  transit_encryption_enabled = true
  at_rest_encryption_enabled = true
}






resource "aws_apprunner_connection" "prod_asyncapi_github" {
  connection_name = "prod-asyncapi-github"
  provider_type   = "GITHUB"
}

resource "aws_apprunner_service" "prod_asyncapi_service" {
  service_name = "prod-asyncapi-service"

  source_configuration {
    authentication_configuration {
      connection_arn = "arn:aws:apprunner:eu-west-2:727907215122:connection/asyncapi/d02278b55e6b4c1f9cbe5557aa730c5b"
    }

    auto_deployments_enabled = true

    code_repository {
      repository_url   = "https://github.com/shakatel/shaka"
      source_directory = "lambda/shaka/async_api"
      source_code_version {
        type  = "BRANCH"
        value = "asyncapi-release"
      }

      code_configuration {
        configuration_source = "REPOSITORY"
        code_configuration_values {
          runtime = "PYTHON_3"
        }
      }
    }
  }

  instance_configuration {
    cpu    = "256"
    memory = "512"
  }
}


resource "aws_secretsmanager_secret" "prod_asyncapi_redis_reader_password" {
  name = "prod-asyncapi-redis-reader-password"
}

resource "aws_secretsmanager_secret_version" "prod_asyncapi_redis_reader_password_value" {
  secret_id = aws_secretsmanager_secret.prod_asyncapi_redis_reader_password.id
  secret_string = jsonencode({
    token = local.nexus_creds["ASYNCAPI_REDIS_READER_PASSWORD"]
  })
}


resource "aws_apprunner_vpc_connector" "prod_asyncapi_connector" {
  vpc_connector_name = "prod-asyncapi-connector"
  subnets            = ["subnet-0bf717b067734bbac", "subnet-043ded321d0fab1dd", "subnet-01190ee0916d2651a"]
  security_groups    = ["sg-034b2cf978c016575"]
}
