data "aws_iam_policy_document" "prod_slack_debug_policy_data" {
  statement {
    effect = "Allow"

    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents",
    ]

    resources = ["arn:aws:logs:*:*:*"]
  }

  statement {
    actions   = ["ec2:CreateNetworkInterface", "ec2:DescribeNetworkInterfaces", "ec2:DeleteNetworkInterface", "ec2:AssignPrivateIpAddresses", "ec2:UnassignPrivateIpAddresses"]
    effect    = "Allow"
    resources = ["*"]
  }

}

resource "aws_iam_policy" "prod_slack_debug_policy" {
  name        = "prod-slack-debug-policy"
  path        = "/"
  description = "IAM policy for slack debug"
  policy      = data.aws_iam_policy_document.prod_slack_debug_policy_data.json
}


resource "aws_iam_role_policy_attachment" "prod_slack_debug_attach" {
  role       = aws_iam_role.prod_slack_debug_role.name
  policy_arn = aws_iam_policy.prod_slack_debug_policy.arn
}

resource "aws_iam_role" "prod_slack_debug_role" {
  name = "prod_slack_debug_role"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

resource "aws_lambda_function" "prod_slack_debug" {
  function_name                  = "prod-slack-debug"
  role                           = aws_iam_role.prod_slack_debug_role.arn
  handler                        = "lambda.slack_debug.lambda_handler"
  runtime                        = "python3.11"
  s3_bucket                      = var.lambda_artifact_bucket
  s3_key                         = "lambda-builds/prod/empty.zip"
  publish                        = false // Don't publish because the alias will reset the version
  timeout                        = 180
  reserved_concurrent_executions = 1

  memory_size = 128

  environment {
    variables = {
      SLACK_ENDPOINT = "*********************************************************************************"
    }
  }
}

resource "aws_lambda_alias" "prod_slack_debug_alias" {
  name             = "deployed"
  description      = "Deployed version"
  function_name    = aws_lambda_function.prod_slack_debug.arn
  function_version = "1"

  lifecycle {
    ignore_changes = [
      function_version
    ]
  }
}
