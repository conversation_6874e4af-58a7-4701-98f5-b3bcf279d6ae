terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.28"
    }
    local = {
      source  = "hashicorp/local"
      version = "2.4.0"
    }
  }

  required_version = ">= 1.2.0"

  backend "s3" {
    bucket         = "shaka-terraform-state-container"
    key            = "infra-staging"
    region         = "eu-west-2"
    encrypt        = true
    dynamodb_table = "shaka-terraform-state-locking"
  }
}

provider "aws" {
}

provider "aws" {
  region = "us-east-1"
  alias  = "use1"
}

module "meta" {
  source = "./meta"
  providers = {
    aws      = aws
    aws.use1 = aws.use1
  }
}

module "shaka" {
  source                 = "./shaka"
  lambda_artifact_bucket = module.meta.lambda_artifact_bucket
  providers = {
    aws      = aws
    aws.use1 = aws.use1
  }
}
