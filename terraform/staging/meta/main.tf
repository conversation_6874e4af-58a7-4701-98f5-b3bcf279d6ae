terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.28"
      configuration_aliases = [
        aws.use1,
      ]

    }
  }
}

locals {
  tf_dir                  = "terraform/staging"
  undeployed_lambda_names = []
  eb_paths = {
    staging = "shaka/nexus"
  }
  lambda_paths = {
  }
  lambda_names = concat([for name, v in var.lambdas : name], local.undeployed_lambda_names)
  eb_names     = [for name, v in var.elastic_beanstalks : name]
}



variable "lambdas" {
  description = "Map of lambdas to deploy. "
  type        = map(any)

  default = {
  }
}

variable "frontends" {
  type = map(any)
  default = {
    staging_stagingalpha_subscriber_app = {
      deploy_enabled = true
      path           = "shaka/client_subscriber-app_web"
      bucket         = "staging-stagingalpha-subscriber-app-dashboard-frontend"
      config         = <<EOF
    VITE_CLIENT_ID = 'stagingstagingalpha-X5523'
    VITE_BASE_URL = 'https://staging.shaka.tel'
    VITE_STRIPE_PK = pk_test_51ReXbrEDmAVvmEcqWN4kySgJa05gptasTuS9yXpLZQ1nnwfV2M4aO3ctxOihl3qlF3uiJkuZn5YF7FEZDXl1gVV3009ONZQHBj
    VITE_CUSTOM_CLIENT="stagingalpha"
    VITE_INTERCOM_KEY = P3xeUIpQBwG-gORxpUuvhn4GFr_ofTnxSVfMYqvB
          EOF
    }
  }
}



variable "elastic_beanstalks" {
  description = "Map of beanstalks to deploy."
  type        = map(any)

  default = {
    staging = {
      application_name    = "staging-staging-eb-app"
      environment_name    = "staging-staging-eb-env"
      name                = "Staging EB"
      domain_name         = "staging"
      lb_name             = "app/awseb--AWSEB-VDF4LPTFGkpv/ded285296898290d"
      lb_domain           = "awseb--AWSEB-VDF4LPTFGkpv-1823649892.eu-west-2.elb.amazonaws.com"
      lb_zone_id          = "ZHURV8PSTC4K8"
      lb_dns              = "staging-staging-eb-env.eba-ct9zuqgf.eu-west-2.elasticbeanstalk.com"
      treat_missing_data  = "notBreaching" # Because not many requests
      default_root_object = "index.html"
    }
  }
}


data "aws_iam_policy_document" "staging_meta_lambda_logging" {
  statement {
    effect = "Allow"

    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents",
    ]

    resources = ["arn:aws:logs:*:*:*"]
  }
}

resource "aws_iam_policy" "staging_meta_lambda_sqs_read" {
  name        = "staging_meta_lambda_sqs_read"
  path        = "/"
  description = "IAM policy for reading from a sqs queue for a lambda"
  policy      = data.aws_iam_policy_document.staging_meta_lambda_sqs_read.json
}


data "aws_iam_policy_document" "staging_meta_lambda_sqs_read" {
  statement {
    effect = "Allow"

    actions = [
      "sqs:ReceiveMessage",
      "sqs:DeleteMessage",
      "sqs:GetQueueAttributes"
    ]

    resources = ["*"]
  }
}

resource "aws_iam_policy" "staging_meta_lambda_logging" {
  name        = "staging_meta_lambda_logging"
  path        = "/"
  description = "IAM policy for logging from a lambda for staging meta"
  policy      = data.aws_iam_policy_document.staging_meta_lambda_logging.json
}

resource "aws_iam_role" "staging_meta_iam_for_lambda" {
  name = "staging_iam_for_lambda"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "staging_meta_lambda_logs" {
  role       = aws_iam_role.staging_meta_iam_for_lambda.name
  policy_arn = aws_iam_policy.staging_meta_lambda_logging.arn
}


resource "aws_iam_role_policy_attachment" "staging_meta_lambda_sqs_read_attach" {
  role       = aws_iam_role.staging_meta_iam_for_lambda.name
  policy_arn = aws_iam_policy.staging_meta_lambda_sqs_read.arn
}

output "lambda_artifact_bucket" {
  value = aws_s3_bucket.staging_terraform_pipeline_artifact_store.bucket
}

locals {
  test_db_creds = sensitive(yamldecode(data.aws_kms_secrets.staging_test_db_creds.plaintext["test-db-creds"]))
}

data "aws_kms_secrets" "staging_test_db_creds" {
  secret {
    name    = "test-db-creds"
    payload = file("${path.module}/test_db_creds.yml.encrypted")
    key_id  = "e73d0e08-6282-47b4-a049-5648fb584809"
  }
}
