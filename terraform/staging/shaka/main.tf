terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.28"
      configuration_aliases = [
        aws.use1,
      ]

    }
  }
}

variable "lambda_artifact_bucket" {
  type = string
}


locals {
  staging_creds = sensitive(yamldecode(data.aws_kms_secrets.staging_staging_creds.plaintext["staging-creds"]))
}


data "aws_kms_secrets" "staging_staging_creds" {
  secret {
    name    = "staging-creds"
    payload = file("${path.module}/staging.yml.encrypted")
    key_id  = "e73d0e08-6282-47b4-a049-5648fb584809"
  }
}
