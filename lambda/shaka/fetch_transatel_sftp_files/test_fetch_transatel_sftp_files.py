import unittest

from .fetch_transatel_sftp_files import (cdr_filename_to_key, is_cdr_file,
                                         key_to_filename, key_to_key_prefix)


class TestCDRFile(unittest.TestCase):
    def test_does_not_identify_imported(self):
        self.assertFalse(is_cdr_file('imported'))

    def test_does_identify_cdr_files(self):
        self.assertTrue(is_cdr_file('cdr_data_MVNA_UK_EEL_SHAKA_140774_20231129160851.csv.gz'))
        self.assertTrue(is_cdr_file('cdr_voice_MVNA_UK_EEL_SHAKA_31828_20231129160434.csv.gz'))

    def test_cdr_to_key(self):
        self.assertEqual('voice/2023/11/29/cdr_voice_MVNA_UK_EEL_SHAKA_31828_20231129160434.csv.gz', cdr_filename_to_key('cdr_voice_MVNA_UK_EEL_SHAKA_31828_20231129160434.csv.gz'))

    def test_key_to_key_prefix(self):
        self.assertEqual('voice/2023/11/29', key_to_key_prefix('voice/2023/11/29/cdr_voice_MVNA_UK_EEL_SHAKA_31828_20231129160434.csv.gz'))

    def test_key_to_filename(self):
        self.assertEqual('cdr_voice_MVNA_UK_EEL_SHAKA_31828_20231129160434.csv.gz', key_to_filename('voice/2023/11/29/cdr_voice_MVNA_UK_EEL_SHAKA_31828_20231129160434.csv.gz'))
