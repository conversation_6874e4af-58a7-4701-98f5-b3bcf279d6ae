import { useQuery } from '@tanstack/react-query';
import { subscriptionKeys } from '@/query-keys/query-keys';
import { subscriptionsService } from '@/services/subscriptionsService';
import { useAuth } from '@/auth/hooks/use-auth';

export function useSubscriber() {
  const { apiClient } = useAuth();

  const {
    data: subscriber,
    isPending: loadingSubscriber,
    error: subscriberError
  } = useQuery({
    queryKey: subscriptionKeys.subscriber,
    queryFn: () => subscriptionsService.getSubscriber(apiClient)
  });

  return {
    subscriber,
    loadingSubscriber,
    subscriberError
  };
}
