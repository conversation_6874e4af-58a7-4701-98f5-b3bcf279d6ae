import React, { useState } from 'react';
import { useAuth } from '@/auth/hooks/use-auth';
import { useMutation } from '@tanstack/react-query';
import { LoginFormData, validateLoginData } from '@/schema/schema';
import {
  flattenValidationErrors,
  standariseNetworkError
} from '@/utils/helpers';
import { AxiosError } from 'axios';
import { useRouter } from 'next/navigation';
import { ROUTES_CONFIG } from '@/src/deliveroo/routes/route-config';
import { trimWhiteSpace } from '@/utils/formatters';

export function useLoginFormSubmission() {
  const { login } = useAuth();
  const router = useRouter();
  const [errors, setErrors] = useState<string[]>([]);

  const [formData, setFormData] = useState<LoginFormData>({
    email: '',
    password: ''
  });

  const {
    mutateAsync: submitLoginForm,
    isPending,
    error: mutationError
  } = useMutation({
    mutationFn: async (payload: LoginFormData) => login(payload),
    onSuccess: () => {
      router.push(`${ROUTES_CONFIG['dashboard'].path}/esim?tab=your-plans`);
    },
    onError: (error: AxiosError) => {
      setErrors(standariseNetworkError(error));
    }
  });

  const onSubmit = async (formData: LoginFormData) => {
    setErrors([]);
    setFormData(formData);

    const result = validateLoginData(formData);
    if (result.success) {
      await submitLoginForm(formData);
      return result;
    } else {
      const formattedErrors = result.error?.format() || {};
      const flatErrors = flattenValidationErrors(formattedErrors);
      setErrors(flatErrors);
    }
  };

  const handleFormSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const form = e.currentTarget;
    const formValues = new FormData(form);
    const trimmedData: Record<string, string> = {};

    formValues.forEach((value, key) => {
      trimmedData[key] =
        typeof value === 'string' ? trimWhiteSpace(value) : String(value);
    });

    await onSubmit({
      email: trimmedData.email,
      password: trimmedData.password
    });
  };

  return {
    handleFormSubmit,
    submitLoginForm: isPending,
    errors,
    setErrors,
    mutationError,
    formData
  };
}
