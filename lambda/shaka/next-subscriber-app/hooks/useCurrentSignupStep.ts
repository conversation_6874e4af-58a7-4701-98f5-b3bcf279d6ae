'use client';

import { useCallback } from 'react';
import { useIsAuthenticated } from '@/auth/hooks/use-auth';
import { usePathname } from 'next/navigation';
import { LocalKey } from '@/hooks/useLocalStorage';

export type RouteConfig = Record<string, { path: string }>;

export type UseCurrentSignupStepOptions = {
  requiresAuth?: boolean;
  brand: 'deliveroo' | 'uswitch';
  storageKey?: string;
};

export type UseCurrentSignupStep = {
  stepId: string;
  saveCurrentStep: () => void;
  saveCurrentStepAsSuccess?: never;
  clearCurrentStep: () => void;
};

export function useCurrentSignupStep(
  options: UseCurrentSignupStepOptions
): UseCurrentSignupStep {
  const {
    requiresAuth = false,
    brand,
    storageKey = LocalKey.SIGNUP_STEP
  } = options;
  const pathname = usePathname();
  const { isAuthenticated } = useIsAuthenticated();

  const saveCurrentStep = useCallback(() => {
    if (requiresAuth && !isAuthenticated) return;
    const payload = {
      brand,
      stepId: pathname,
      at: Date.now()
    };
    localStorage.setItem(storageKey, JSON.stringify(payload));
  }, [brand, isAuthenticated, requiresAuth, pathname, storageKey]);

  const clearCurrentStep = useCallback(() => {
    localStorage.removeItem(storageKey);
  }, [storageKey]);

  return { stepId: pathname, saveCurrentStep, clearCurrentStep };
}
