// import { useRouter } from 'next/navigation';
// import { useState } from 'react';
// import { ROUTES_CONFIG } from '@/src/uswitch/routes/route-config';
// import { flattenValidationErrors } from '@/utils/helpers';
// import { AxiosError } from 'axios';
// import { validateSignInForm } from '@/schema/schema';
//
// export interface SignInFormData {
//   email: string;
// }
//
// export function useSignInFormSubmission() {
//   const router = useRouter();
//   const [isLoading, setIsLoading] = useState(false);
//   const [errors, setErrors] = useState<string[]>([]);
//   const [formData, setFormData] = useState<SignInFormData>({
//     email: ''
//   });
//
//   const handleSubmit = async (formData: SignInFormData) => {
//     setIsLoading(true);
//     setErrors([]);
//
//     try {
//       const formattedData = {
//         ...formData,
//         email: formData.email.trim()
//       };
//
//       setFormData(formData);
//
//       if (result.success) {
//         //  API call here
//         console.log('Form submitted successfully:', formData);
//         router.push(ROUTES_CONFIG['payment'].path);
//       } else {
//         const formattedErrors = result.error?.format() || {};
//         const flatErrors = flattenValidationErrors(formattedErrors);
//         setErrors(flatErrors);
//       }
//     } catch (error: unknown) {
//       if (error instanceof AxiosError) {
//         setErrors([error.message]);
//       } else {
//         setErrors(['An unexpected error occurred. Please try again.']);
//       }
//     } finally {
//       setIsLoading(false);
//     }
//   };
//
//   return { handleSubmit, isLoading, errors, setErrors, formData };
// }
