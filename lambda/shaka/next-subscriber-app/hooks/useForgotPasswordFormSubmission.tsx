import React, { useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import {
  flattenValidationErrors,
  standariseNetworkError
} from '@/utils/helpers';
import { validateSignInForm } from '@/schema/schema';
import { userService } from '@/services/userService';
import { useAuth } from '@/auth/hooks/use-auth';

export function useForgotPasswordFormSubmission() {
  const { apiClient } = useAuth();
  const [errors, setErrors] = useState<string[]>([]);
  const [successMessage, setSuccessMessage] = useState<null | boolean>(null);
  const [formData, setFormData] = useState({
    email: ''
  });

  const {
    mutateAsync: submitForgottenPasswordForm,
    isPending,
    error: mutationError
  } = useMutation({
    mutationFn: async (email: string) =>
      userService.forgottenPassword(apiClient, email),
    onSuccess: () => {
      setSuccessMessage(true);
    },
    onError: (error: AxiosError) => {
      console.log(standariseNetworkError(error), 'error');
      setErrors(standariseNetworkError(error));
    }
  });

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setFormData(formData);

    setErrors([]);

    const emailDataformData = new FormData(e.currentTarget);
    const email = emailDataformData.get('email') as string;
    const emailData = { email: email?.trim() || '' };

    try {
      const result = validateSignInForm(emailData);
      if (result.success) {
        await submitForgottenPasswordForm(emailData.email);
        return result;
      } else {
        const formattedErrors = result.error?.format() || {};
        const flatErrors = flattenValidationErrors(formattedErrors);
        setErrors(flatErrors);
      }
    } catch {
      return;
    }
  };

  return {
    errors,
    setErrors,
    formData,
    setFormData,
    isPending,
    handleSubmit,
    mutationError,
    successMessage,
    setSuccessMessage
  };
}
