import { useState } from 'react';
import {
  flattenValidationErrors,
  standariseNetworkError
} from '@/utils/helpers';
import { useMutation } from '@tanstack/react-query';
import { wizardService } from '@/services/wizardService';
import { useAuth } from '@/auth/hooks/use-auth';
import {
  NumberPortingFormData,
  validateNumberPortingForm
} from '@/schema/schema';

export function usePACFormSubmission(subscriptionId?: number) {
  const { apiClient } = useAuth();
  const [errors, setErrors] = useState<string[]>([]);
  const [successMessage, setSuccessMessage] = useState<null | boolean>(null);

  const [formData, setFormData] = useState<NumberPortingFormData>({
    pac_code: '',
    incoming_phone_number: '',
    day: '',
    month: '',
    year: '',
    desired_date: ''
  });

  const {
    mutateAsync: submitPACForm,
    isPending,
    error: mutationError
  } = useMutation({
    mutationFn: async (
      data: NumberPortingFormData & { subscription_id: number }
    ) => wizardService.submitPACForm(apiClient, data),
    onSuccess: () => {
      setSuccessMessage(true);
    },
    onError: (error) => setErrors(standariseNetworkError(error))
  });

  const handleSubmit = async (formData: NumberPortingFormData) => {
    setErrors([]);

    try {
      setFormData(formData);

      const result = validateNumberPortingForm(formData);

      if (!subscriptionId) {
        throw new Error('Subscription ID is required');
      }

      if (result.success) {
        const apiData = {
          ...formData,
          subscription_id: subscriptionId
        };

        await submitPACForm(apiData);

        return result;
      } else {
        const formattedErrors = result.error?.format() || {};
        const flatErrors = flattenValidationErrors(formattedErrors);
        setErrors(flatErrors);
      }
    } catch {
      return;
    }
  };

  return {
    handleSubmit,
    isPending,
    errors,
    setErrors,
    mutationError,
    formData,
    successMessage
  };
}
