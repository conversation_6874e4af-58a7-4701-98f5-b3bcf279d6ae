import { useState } from 'react';
import { NumberPortingFormData } from '@/schema/schema';

export function usePACStateManager() {
  const [pacDataMap, setPacDataMap] = useState<
    Map<string, NumberPortingFormData>
  >(new Map());
  const [activeModalPlanId, setActiveModalPlanId] = useState<string | null>(
    null
  );

  const openModal = (planInstanceId: string) =>
    setActiveModalPlanId(planInstanceId);

  const closeModal = () => setActiveModalPlanId(null);

  const savePacData = (planInstanceId: string, data: NumberPortingFormData) => {
    setPacDataMap((prev) => new Map(prev).set(planInstanceId, data));
  };

  const getPacData = (
    planInstanceId: string
  ): NumberPortingFormData | undefined => pacDataMap.get(planInstanceId);

  const hasEnteredPAC = (instanceId: string) => {
    const pacEntry = pacDataMap.get(instanceId);
    return (
      !!pacEntry && !!pacEntry.pac_code && !!pacEntry.incoming_phone_number
    );
  };

  const deletePacData = (planInstanceId: string) => {
    setPacDataMap((previousPacData) => {
      const newMap = new Map(previousPacData);
      newMap.delete(planInstanceId);
      return newMap;
    });
  };

  const getAllPacData = (): [string, NumberPortingFormData][] =>
    Array.from(pacDataMap.entries());

  const getPacDataCount = (): number => pacDataMap.size;

  const clearAllPacData = () => setPacDataMap(new Map());

  return {
    openModal,
    closeModal,
    activeModalPlanId,
    isModalOpen: activeModalPlanId !== null,

    savePacData,
    getPacData,
    hasEnteredPAC,
    deletePacData,
    getAllPacData,
    getPacDataCount,
    clearAllPacData,

    pacDataMap: new Map(pacDataMap)
  };
}

export function generatePlanInstanceId(
  planId: number,
  addonId: number | null,
  index: number
): string {
  return `${planId}_${addonId || 'null'}_${index}`;
}

export type PACStateManager = ReturnType<typeof usePACStateManager>;
