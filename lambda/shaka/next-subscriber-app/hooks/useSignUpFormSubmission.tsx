import { useState } from 'react';
import { useAuth } from '@/auth/hooks/use-auth';
import {
  SignupFormData,
  SignupPayload
} from '@/components/signup-form/signup-form';
import { useMutation } from '@tanstack/react-query';
import { validateSignUpData } from '@/schema/schema';
import {
  flattenValidationErrors,
  standariseNetworkError
} from '@/utils/helpers';
import { ROUTES_CONFIG } from '@/src/uswitch/routes/route-config';
import { useRouter } from 'next/navigation';
import { AxiosError } from 'axios';
import { getDesiredCodeChangeDate } from '@/src/uswitch/utils/helpers';
import { SignupProgressTrackedRoutes } from '@/context/signup-progress-context/signup-progress-tracked-routes';
import { LocalKey } from '@/hooks/useLocalStorage';

export const transformToPayload = (data: SignupFormData): SignupPayload => {
  const { day, month, year, ...rest } = data;
  return {
    ...rest,
    date_of_birth: getDesiredCodeChangeDate(day, month, year)!
  };
};

export function useSignUpFormSubmission(
  updateProgress?: (updates: Partial<SignupProgressTrackedRoutes>) => void
) {
  const { signup } = useAuth();
  const router = useRouter();
  const [errors, setErrors] = useState<string[]>([]);

  const [formData, setFormData] = useState<SignupFormData>({
    name: '',
    email: '',
    password: '',
    day: '',
    month: '',
    year: ''
  });

  const {
    mutateAsync: submitSignupForm,
    isPending,
    error: mutationError
  } = useMutation({
    mutationFn: async (payload: SignupPayload) => signup(payload),
    onSuccess: () => {
      router.push(ROUTES_CONFIG['otp'].path);
      updateProgress?.({ isRegistered: true });
      localStorage.removeItem(LocalKey.RIDER_EMAIL);
    },
    onError: (error: AxiosError) => setErrors(standariseNetworkError(error))
  });

  const handleSubmit = async (formData: SignupFormData) => {
    setErrors([]);

    try {
      setFormData(formData);

      const result = validateSignUpData(formData);
      if (result.success) {
        await submitSignupForm(transformToPayload(formData));
        return result;
      } else {
        const formattedErrors = result.error?.format() || {};
        const flatErrors = flattenValidationErrors(formattedErrors);
        setErrors(flatErrors);
      }
    } catch {
      return;
    }
  };

  return {
    handleSubmit,
    isPending,
    errors,
    setErrors,
    mutationError,
    formData
  };
}
