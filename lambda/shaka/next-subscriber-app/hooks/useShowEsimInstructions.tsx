import { useState } from 'react';
import {
  phoneOperatingSystems,
  PhoneOS
} from '@/src/uswitch/app/signup/payment/_components/video-instructions';

export function useShowEsimInstructions() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [instructionType, setInstructionType] = useState<PhoneOS | null>(null);

  const handleAppleClick = () => {
    setInstructionType(phoneOperatingSystems.iOS);
    setIsModalOpen(true);
  };

  const handleAndroidClick = () => {
    setInstructionType(phoneOperatingSystems.android);
    setIsModalOpen(true);
  };

  const handleModalClose = (open: boolean) => {
    setIsModalOpen(open);
    if (!open) {
      setInstructionType(null);
    }
  };

  return {
    isModalOpen,
    instructionType,
    handleModalClose,
    handleAppleClick,
    handleAndroidClick
  };
}
