import { useSubscription } from '@/hooks/useSubscription';
import { useCurrentPlan } from '@/context/current-plan-context';
import { SubscriptionsApiResponse } from '@/schemas/schemas';

export default function useGetCurrentlySelectedPlan() {
  const { subscriptions } = useSubscription();
  const { currentPlanId } = useCurrentPlan();
  const currentSubscription: SubscriptionsApiResponse[number] | undefined =
    subscriptions?.find((subscription) => subscription.id === currentPlanId);

  return currentSubscription;
}
