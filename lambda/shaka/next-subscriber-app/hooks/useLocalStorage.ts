'use client';

import { useState, useEffect } from 'react';

export enum LocalKey {
  SIGNUP_PLAN_STATE = 'signup-plan-state',
  SIGNUP_PLAN_STATE_DELIVEROO = 'signup-plan-state-deliveroo',
  AUTH_TOKENS = 'auth_tokens',
  SIGNUP_STEP = 'signupCurrentStep',
  SIGNUP_STEP_RESTORED = 'signupStepRestored',
  SIGNUP_PROGRESS = 'signupProgress',
  DELIVEROO_RIDER_AUTHENTICATED = 'deliveroo.rider.isAuthenticated',
  RIDER_ID = 'riderID',
  RIDER_EMAIL = 'riderEmail'
}

function useLocalStorage<T>(
  key: string,
  initialValue: T
): [T, (value: T) => void] {
  const readValue = (): T => {
    if (typeof window === 'undefined') {
      return initialValue;
    }

    try {
      const item = window.localStorage.getItem(key);
      if (item === null) {
        return initialValue;
      }

      try {
        return JSON.parse(item) as T;
      } catch {
        return item as T;
      }
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  };

  const [storedValue, setStoredValue] = useState<T>(readValue);

  const setValue = (value: T) => {
    try {
      const valueToStore =
        value instanceof Function ? value(storedValue) : value;

      setStoredValue(valueToStore);

      if (typeof window !== 'undefined') {
        window.localStorage.setItem(key, JSON.stringify(valueToStore));
      }
    } catch (error) {
      console.warn(`Error setting localStorage key "${key}":`, error);
    }
  };

  useEffect(() => {
    setStoredValue(readValue());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return [storedValue, setValue];
}

export default useLocalStorage;
