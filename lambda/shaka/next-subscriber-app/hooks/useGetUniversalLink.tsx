import { useAuth } from '@/auth/hooks/use-auth';
import { useQuery } from '@tanstack/react-query';
import { signupKeys } from '@/query-keys/query-keys';
import { wizardService } from '@/services/wizardService';

export function useGetUniversalLink(token: string) {
  const { apiClient } = useAuth();
  const { data, isPending, error } = useQuery({
    queryKey: signupKeys.universalLink,
    queryFn: () => wizardService.getUniversalLink(apiClient, token),
    enabled: !!token
  });

  console.log(data, 'response !!!');

  // what I get back ?

  return {
    universalLink: data?.universalLink,
    isPendingUniversalLink: isPending,
    universalLinkError: error
  };
}
