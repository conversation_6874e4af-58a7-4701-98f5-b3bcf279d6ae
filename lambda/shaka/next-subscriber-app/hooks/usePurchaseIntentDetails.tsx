import { useQuery } from '@tanstack/react-query';
import { paymentService } from '@/services/paymentService';
import { paymentKeys } from '@/query-keys/query-keys';
import { useAuth } from '@/auth/hooks/use-auth';
import { getClientConfig } from '@/client-config/client-config';

export function usePurchaseIntentDetails(sessionId: string | null) {
  const { apiClient } = useAuth();
  const clientId = getClientConfig().clientId;

  const {
    data: purchaseIntentDetails,
    isLoading,
    error,
    isError
  } = useQuery({
    queryKey: [paymentKeys.purchaseIntent, clientId, sessionId],
    queryFn: () =>
      paymentService.getPurchaseIntentDetails(apiClient, sessionId || ''),
    retry: false,
    enabled: !!sessionId
  });

  return {
    purchaseIntentDetails,
    isLoading,
    error,
    isError
  };
}
