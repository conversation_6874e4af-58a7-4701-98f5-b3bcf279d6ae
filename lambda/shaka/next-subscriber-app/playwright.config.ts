import { defineConfig, devices } from '@playwright/test';
import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';

/**
 * Read environment variables from file.
 * https://github.com/motdotla/dotenv
 */
// Load environment variables based on the app being tested
const app = process.env.TEST_APP || 'uswitch';
dotenv.config({ path: path.resolve(__dirname, `./client-config/${app}.env`) });

/**
 * See https://playwright.dev/docs/test-configuration.
 */
export default defineConfig({
  /* Default test directory - will be overridden by app-specific projects */
  testDir: './e2e',
  /* Run tests in files in parallel */
  fullyParallel: true,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  /* Retry on CI only */
  retries: process.env.CI ? 2 : 0,
  /* Opt out of parallel tests on CI. */
  workers: process.env.CI ? 1 : undefined,
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: [['html', { open: 'never' }], ['list']],
  /* Global timeout for the entire test run */
  globalTimeout: process.env.CI ? 60 * 60 * 1000 : undefined, // 1 hour on CI
  /* Timeout for each test */
  timeout: 30000,
  expect: {
    timeout: 25000
  },
  webServer: [
    {
      command: 'pnpm dev:uswitch',
      url: 'http://localhost:3000',
      name: 'USwitch',
      timeout: 120 * 1000,
      reuseExistingServer: !process.env.CI
    },
    {
      command: 'pnpm dev:subscriber',
      url: 'http://localhost:3001',
      name: 'Subscriber',
      timeout: 120 * 1000,
      reuseExistingServer: !process.env.CI
    },
    {
      command: 'pnpm dev:deliveroo',
      url: 'http://localhost:3002',
      name: 'Deliveroo',
      timeout: 120 * 1000,
      reuseExistingServer: !process.env.CI
    }
  ],
  use: {
    launchOptions: {
      slowMo: 1000
    },
    trace: {
      mode: 'on-first-retry',
      snapshots: true,
      screenshots: true,
      sources: true
    },
    navigationTimeout: 10000,
    actionTimeout: 15000
  },

  projects: [
    // USwitch app project
    {
      name: 'uswitch-app',
      testDir: './src/uswitch/tests/e2e',
      testIgnore: /order-confirmstion\.android\.spec\.ts$/,
      use: {
        ...devices['Desktop Chrome'],
        baseURL: 'http://localhost:3000'
      }
    },
    // Subscriber app project
    {
      name: 'subscriber-app',
      testDir: './src/subscriber-app/tests/e2e',
      use: {
        ...devices['Desktop Chrome'],
        baseURL: 'http://localhost:3001'
      }
    },
    // Deliveroo app project
    {
      name: 'deliveroo-app',
      testDir: './src/deliveroo/tests/e2e',
      use: {
        ...devices['Desktop Chrome'],
        baseURL: 'http://localhost:3002'
      }
    },
    // Global project (uses uswitch by default)
    {
      name: 'global',
      testDir: './tests/e2e',
      use: {
        ...devices['Desktop Chrome'],
        baseURL: 'http://localhost:3000'
      }
    }
  ]
});
