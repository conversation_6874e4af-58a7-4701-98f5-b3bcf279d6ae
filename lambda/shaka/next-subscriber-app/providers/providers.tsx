'use client';

import React, { useEffect } from 'react';
import {
  QueryClientProvider,
  QueryClient,
  MutationCache
} from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { initMocks } from '@/mocks';

export const queryClient = new QueryClient({
  mutationCache: new MutationCache({
    onError: (error) => {
      if (error?.cause === 401) {
        console.log('User is not authenticated');
      }
    }
  })
});

export function ReactQueryProvider({
  children
}: {
  children: React.ReactNode;
}) {
  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {/*<ReactQueryDevtools initialIsOpen={false} />*/}
      {process.env.NODE_ENV === 'development' && (
        <ReactQueryDevtools initialIsOpen={false} />
      )}
    </QueryClientProvider>
  );
}

export function MSWProvider({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    initMocks().catch(console.error);
  }, []);

  return <>{children}</>;
}
