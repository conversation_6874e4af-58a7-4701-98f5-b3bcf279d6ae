import { CreateSessionParams } from '@/lib/stripe/types';

export const stripeKeys = {
  all: ['stripe'] as const,
  sessions: () => [...stripeKeys.all, 'sessions'] as const,
  session: (params: CreateSessionParams) =>
    [...stripeKeys.sessions(), params] as const,
  sessionStatus: (sessionId: string) =>
    [...stripeKeys.sessions(), 'status', sessionId] as const
};

export const signupKeys = {
  planData: ['planData'] as const,
  iphoneInstallationCode: ['iphoneInstallationCode'] as const,
  universalLink: ['universalLink'] as const
};

export const subscriptionKeys = {
  subscriptions: ['subscriptions'],
  subscriber: ['subscriber']
};

export const simKeys = {
  all: ['sim'] as const,
  esim: (sessionId: string) => [...simKeys.all, sessionId],
  purchaseDetails: ['purchaseDetails'] as const
};

export const paymentKeys = {
  sessionID: ['sessionID'],
  purchaseIntent: ['purchaseIntent']
};
