import { AxiosClient } from '@/lib/axios-client';
import { API_ENDPOINTS, API_HOST } from '@/auth/api/endpoints';
import { DeliverooRiderFormData } from '@/schema/schema';

export type DeliverooAuthResponse = {
  valid: boolean;
};

export const userService = {
  forgottenPassword: async (
    apiClient: AxiosClient,
    email: string
  ): Promise<any> => {
    return await apiClient.post(
      `${API_HOST}/s/api/v1/1/${API_ENDPOINTS.auth.forgotPassword}`,
      { email },
      {
        skipAuth: true
      }
    );
  },
  authenticateRider: async (
    apiClient: AxiosClient,
    data: DeliverooRiderFormData
  ): Promise<DeliverooAuthResponse> => {
    return await apiClient.post(
      `${API_HOST}/s/api/v1/1/${API_ENDPOINTS.deliveroo.validateRider}`,
      data,
      {
        skipAuth: true
      }
    );
  },
  registration: async (
    apiClient: AxiosClient,
    data: DeliverooRiderFormData
  ): Promise<any> => {
    return await apiClient.post(
      `${API_HOST}/s/api/v1/1/${API_ENDPOINTS.auth.signup}`,
      data,
      {
        skipAuth: true
      }
    );
  }
};
