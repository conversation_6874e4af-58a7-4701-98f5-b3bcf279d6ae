import { AxiosClient } from '@/lib/axios-client';
import { API_ENDPOINTS } from '@/auth/api/endpoints';
import { PlansApiResponse, plansApiResponseSchema } from '@/schemas/schemas';

export const planService = {
  getPlans: async (apiClient: AxiosClient): Promise<PlansApiResponse> => {
    // return await apiClient.get(API_ENDPOINTS.plans.base);

    const response = await apiClient.get(API_ENDPOINTS.plans.base, {
      skipAuth: true
    });
    const result = plansApiResponseSchema.safeParse(response);

    if (!result.success) {
      console.error('Invalid plans data:', result.error.format());
      throw new Error('Received invalid plans data from the server');
    }

    return result.data;
  }
};
