import { NextRequest, NextResponse } from 'next/server';

const PH_PROXY_PATH = '/phg-xyz123';

export function middleware(request: NextRequest) {
  const url = request.nextUrl.clone();
  const hostname = url.pathname.startsWith(PH_PROXY_PATH + '/static/')
    ? 'us-assets.i.posthog.com'
    : 'us.i.posthog.com';
  const requestHeaders = new Headers(request.headers);

  requestHeaders.set('host', hostname);

  url.protocol = 'https';
  url.hostname = hostname;
  // @ts-expect-error error
  url.port = 443;
  url.pathname = url.pathname.replace(new RegExp('^' + PH_PROXY_PATH), '');

  return NextResponse.rewrite(url, {
    headers: requestHeaders
  });
}

export const config = {
  matcher: '/phg-xyz123/:path*'
};
