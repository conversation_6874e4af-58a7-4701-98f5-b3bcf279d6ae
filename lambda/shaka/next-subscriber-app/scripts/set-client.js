// eslint-disable-next-line @typescript-eslint/no-require-imports
const fs = require('fs');
// eslint-disable-next-line @typescript-eslint/no-require-imports
const path = require('path');

const client = process.argv[2] || 'clientA';
const clientsDir = path.join(__dirname, '../src/subscriber-app/clients');
const targetDir = path.join(clientsDir, 'active');
const sourceDir = path.join(clientsDir, client);

// Remove existing active folder
if (fs.existsSync(targetDir)) {
  fs.rmSync(targetDir, { recursive: true, force: true });
}
// Copy new client folder
fs.cpSync(sourceDir, targetDir, { recursive: true });
console.log(`Copied ${client} config to active/`);

// Copy client.css to active/client.css (not to app/globals.css)
const clientCss = path.join(sourceDir, 'client.css');
const targetCss = path.join(targetDir, 'client.css');
if (fs.existsSync(clientCss)) {
  fs.copyFileSync(clientCss, targetCss);
  console.log(`Copied ${client}'s client.css`);
}
