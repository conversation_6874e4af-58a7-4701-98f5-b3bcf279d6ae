export interface ClientConfig {
  clientId: string;
  name: string;
  apiBaseUrl: string | undefined;
  universalLinkUrl: string | undefined;
  stripePublicKey: string | undefined;
  analytics: {
    provider: 'google' | 'none';
    trackingId?: string;
  };
  features: {
    [key: string]: boolean;
  };
  theme: {
    logoUrl: string;
    faviconUrl: string;
  };
}

const defaultConfig: ClientConfig = {
  clientId: 'test-client',
  name: 'Test Client',
  // add localhost ? check how it is done in sub app - axios should inject it anyway
  apiBaseUrl: process.env.NEXT_PUBLIC_API_BASE_URL ?? 'http://localhost:8000',
  universalLinkUrl:
    process.env.NEXT_PUBLIC_UNIVERSAL_LINK_URL ??
    process.env.NEXT_PUBLIC_API_BASE_URL ??
    'http://localhost:3002',
  stripePublicKey: process.env.NEXT_PUBLIC_STRIPE_PK,
  analytics: {
    provider: 'none',
    trackingId: ''
  },
  features: {},
  theme: {
    logoUrl: '/logo.svg',
    faviconUrl: '/favicon.ico'
  }
};

/**
 * Gets client-specific configuration based on environment variables
 * This function first checks for client-specific env vars,
 * then falls back to the default config
 */

// Do we need production env file with different values ?
// Does clientID make sense here ?
export function getClientConfig(): ClientConfig {
  const clientId =
    typeof process.env.NEXT_PUBLIC_CLIENT_ID === 'string' &&
    process.env.NEXT_PUBLIC_CLIENT_ID.trim() !== ''
      ? process.env.NEXT_PUBLIC_CLIENT_ID
      : defaultConfig.clientId;

  // TODO:
  // - add description for metadata ?

  // Build config based on environment variables with fallbacks to default
  return {
    clientId,
    name: process.env.NEXT_PUBLIC_CLIENT_NAME || defaultConfig.name,
    apiBaseUrl: defaultConfig.apiBaseUrl,
    universalLinkUrl: defaultConfig.universalLinkUrl,
    stripePublicKey:
      process.env.NEXT_PUBLIC_STRIPE_PUBLIC_KEY ||
      defaultConfig.stripePublicKey,
    analytics: {
      provider:
        (process.env.NEXT_PUBLIC_ANALYTICS_PROVIDER as 'google' | 'none') ||
        defaultConfig.analytics.provider,
      trackingId: process.env.NEXT_PUBLIC_ANALYTICS_TRACKING_ID
    },
    // extra stuff - may be useful
    features: {
      ...defaultConfig.features,
      ...JSON.parse(process.env.NEXT_PUBLIC_FEATURES || '{}')
    },
    theme: {
      logoUrl:
        process.env.NEXT_PUBLIC_THEME_LOGO_URL || defaultConfig.theme.logoUrl,
      faviconUrl:
        process.env.NEXT_PUBLIC_THEME_FAVICON_URL ||
        defaultConfig.theme.faviconUrl
    }
  };
}

// TEST - may be useful ?
// export function isFeatureEnabled(featureName: string): boolean {
//   const { features } = getClientConfig();
//   return features[featureName];
// }
//
// export function getClientIdentifier(): string {
//   const { clientId, name } = getClientConfig();
//   return `${name.toLowerCase().replace(/\s+/g, '-')}-${clientId}`;
// }
