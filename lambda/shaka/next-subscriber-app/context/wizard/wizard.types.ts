import React from 'react';

export interface WizardContextValue<TStep extends string> {
  currentStep: TStep;
  currentStepNumber: number;
  currentIndex: number;
  totalSteps: number;
  goToNextStep: () => void;
  goBackToPreviousStep: () => void;
}

export interface WizardProviderProps<TStep extends string>
  extends React.PropsWithChildren {
  steps: TStep[];
  stepRoutes: Record<TStep, string>;
}
