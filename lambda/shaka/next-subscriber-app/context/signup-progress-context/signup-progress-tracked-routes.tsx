'use client';
import React, { createContext, useContext, useState, useEffect } from 'react';
import { LocalKey } from '@/hooks/useLocalStorage';

export interface SignupProgressTrackedRoutes {
  isRegistered: boolean;
  isOtpVerified: boolean;
  isPaymentCompleted: boolean;
}

interface SignupProgressContextType {
  progress: SignupProgressTrackedRoutes;
  updateProgress: (updates: Partial<SignupProgressTrackedRoutes>) => void;
  resetProgress: () => void;
}

const SignupProgressContext = createContext<
  SignupProgressContextType | undefined
>(undefined);

export function SignupProgressProvider({
  children
}: {
  children: React.ReactNode;
}) {
  const [progress, setProgress] = useState<SignupProgressTrackedRoutes>({
    isRegistered: false,
    isOtpVerified: false,
    isPaymentCompleted: false
  });

  useEffect(() => {
    const savedProgress = localStorage.getItem(LocalKey.SIGNUP_PROGRESS);
    if (savedProgress) {
      setProgress(JSON.parse(savedProgress));
    }
  }, []);

  // const updateProgress = useCallback(
  //   (updates: Partial<SignupProgressTrackedRoutes>) => {
  //     setProgress((currentProgress) => {
  //       const storageData = localStorage.getItem(LocalKey.SIGNUP_PROGRESS);
  //       let latestProgress = currentProgress;
  //
  //       if (storageData) {
  //         try {
  //           latestProgress = JSON.parse(storageData);
  //         } catch (error) {
  //           console.error('Failed to parse storage data:', error);
  //         }
  //       }
  //
  //       const newProgress = { ...latestProgress, ...updates };
  //
  //       localStorage.setItem(
  //         LocalKey.SIGNUP_PROGRESS,
  //         JSON.stringify(newProgress)
  //       );
  //
  //       return newProgress;
  //     });
  //   },
  //   []
  // );

  useEffect(() => {
    localStorage.setItem(LocalKey.SIGNUP_PROGRESS, JSON.stringify(progress));
  }, [progress]);

  const updateProgress = (updates: Partial<SignupProgressTrackedRoutes>) => {
    setProgress((prev) => ({ ...prev, ...updates }));
  };

  const resetProgress = () => {
    setProgress({
      isRegistered: false,
      isOtpVerified: false,
      isPaymentCompleted: false
    });

    localStorage.removeItem(LocalKey.SIGNUP_PROGRESS);
  };

  return (
    <SignupProgressContext.Provider
      value={{ progress, updateProgress, resetProgress }}
    >
      {children}
    </SignupProgressContext.Provider>
  );
}

export function useSignupProgress() {
  const context = useContext(SignupProgressContext);
  if (!context) {
    throw new Error(
      'useSignupProgress must be used within SignupProgressProvider'
    );
  }
  return context;
}
