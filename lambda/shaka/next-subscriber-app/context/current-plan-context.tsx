'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';

interface CurrentPlanContextValue {
  currentPlanId: number | null;
  setCurrentPlanId: (id: number) => void;
}

const CurrentPlanContext = createContext<CurrentPlanContextValue | undefined>(
  undefined
);

interface CurrentPlanProviderProps {
  children: ReactNode;
}

export function CurrentPlanProvider({ children }: CurrentPlanProviderProps) {
  const [currentPlanId, setCurrentPlanId] = useState<number | null>(null);

  const value: CurrentPlanContextValue = {
    currentPlanId,
    setCurrentPlanId
  };

  return (
    <CurrentPlanContext.Provider value={value}>
      {children}
    </CurrentPlanContext.Provider>
  );
}

export function useCurrentPlan(): CurrentPlanContextValue {
  const context = useContext(CurrentPlanContext);
  if (context === undefined) {
    throw new Error('useCurrentPlan must be used within a CurrentPlanProvider');
  }
  return context;
}

export const useCurrentPlanId = () =>
  useContext(CurrentPlanContext)?.currentPlanId ?? null;
