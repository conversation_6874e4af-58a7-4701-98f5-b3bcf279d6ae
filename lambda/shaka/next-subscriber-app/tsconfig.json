{
  "compilerOptions": {
    "target": "es2022",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "strict": true,
    "noImplicitAny": false,
    "strictNullChecks": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",
    "paths": {
      "@/api/*": ["api/*"],
      "@/auth/*": ["auth/*"],
      "@/components/*": ["components/*"],
      "@/hooks/*": ["hooks/*"],
      "@/types/*": ["types/*"],
      "@/utils/*": ["utils/*"],
      "@/src/uswitch/*": ["src/uswitch/*"],
      "@/src/subscriber-app/*": ["src/subscriber-app/*"],
      "@/context/*": ["context/*"],
      "@/config/*": ["config/*"],
      "@/client-config/*": ["client-config/*"],
      "@/styles/*": ["styles/*"],
      "@/lib/*": ["lib/*"],
      "@/services/*": ["services/*"],
      "@/mocks/*": ["mocks/*"],
      "@/*": ["*"],
      "@/global-styles": ["global-styles.css"],
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "next.config.ts"],
  "exclude": ["node_modules"],
  "types": ["vitest", "vitest/globals"]
}
