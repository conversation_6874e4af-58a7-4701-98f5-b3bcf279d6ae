async function initMocks() {
  if (typeof window === 'undefined') {
    const { server } = await import('./node');
    server.listen();
  } else {
    const { worker } = await import('./browser');
    console.log('✅ MSW started in browser');
    const workerUrl =
      process.env.NEXT_PUBLIC_MSW_URL ?? '/mockServiceWorker.js';
    await worker.start({
      serviceWorker: { url: workerUrl }
    });
  }
}

export { initMocks };
