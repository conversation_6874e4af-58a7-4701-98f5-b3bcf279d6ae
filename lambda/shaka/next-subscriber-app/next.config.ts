import type { NextConfig } from 'next';
// import path from 'path';

const nextConfig: NextConfig = {
  reactStrictMode: true,
  transpilePackages: ['msw'],
  // Optional: Configure output for better monorepo support
  distDir: 'dist',
  // Allow images from other domains
  images: {
    // formats: ['jpg', 'jpeg', 'png', 'webp', 'svg', 'gif', 'ico']
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'blogger.googleusercontent.com',
        port: '',
        pathname: '/**'
      }
    ]
  },
  compiler:
    process.env.NODE_ENV === 'production'
      ? { removeConsole: { exclude: ['error'] } }
      : {},
  // https://nextjs.org/docs/app/api-reference/config/next-config-js/turbopack
  // turbopack: {
  // },
  // Configure webpack if needed
  // webpack: (config, { isServer }) => {
  //   // Custom webpack configurations if needed
  //   return config;
  // }
  // redirects: async () => {
  //   return [
  //     {
  //       source: '/payment/return',
  //       destination: '/signup/payment/return',
  //       permanent: false
  //     }
  //   ];
  // }
  // https://www.johnnyle.io/read/analytics
  // async rewrites() {
  // return [
  //   {
  //     source: "/ingest/static/:path*",
  //     destination: "https://us-assets.i.posthog.com/static/:path*",
  //   },
  //   {
  //     source: "/ingest/:path*",
  //     destination: "https://us.i.posthog.com/:path*",
  //   },
  //   {
  //     source: "/ingest/decide",
  //     destination: "https://us.i.posthog.com/decide",
  //   },
  // ];
  // },
  skipTrailingSlashRedirect: true
};

// Function to create app-specific config - TEST
// export function createAppConfig(appDir: string): NextConfig {
//   const appPath = path.resolve(__dirname, 'src', appDir);
//
//   return {
//     ...nextConfig,
//     // Add app-specific overrides here
//     env: {
//       APP_NAME: appDir
//     },
//     // Set the app directory as the base path
//     distDir: `.next/${appDir}`
//   };
// }

export default nextConfig;
