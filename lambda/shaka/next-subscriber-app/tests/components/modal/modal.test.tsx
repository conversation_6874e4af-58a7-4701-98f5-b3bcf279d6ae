import React from 'react';
import { describe, it, expect, vi, beforeAll } from 'vitest';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import Modal from '@/components/modal/modal';
import userEvent from '@testing-library/user-event';

function renderModal(props = {}) {
  const onOpenChange = vi.fn();
  render(
    <Modal open={true} onOpenChange={onOpenChange} {...props}>
      <Modal.Overlay data-testid="modal-overlay" color="black" opacity={0.7} />
      <Modal.Content data-testid="modal-content" className="test-content">
        <Modal.Title data-testid="modal-title" as="h2" className="test-title">
          Test Modal Title
        </Modal.Title>
        <Modal.Description
          data-testid="modal-desc"
          as="p"
          className="test-desc"
        >
          Test description
        </Modal.Description>
        <Modal.Close data-testid="modal-close" className="test-close">
          X
        </Modal.Close>
      </Modal.Content>
    </Modal>
  );
  return { onOpenChange };
}

describe('Modal component', () => {
  beforeAll(() => {
    if (!window.HTMLDialogElement.prototype.showModal) {
      window.HTMLDialogElement.prototype.showModal = function () {};
    }
    if (!window.HTMLDialogElement.prototype.close) {
      window.HTMLDialogElement.prototype.close = function () {};
    }
  });

  it('renders modal content when open', () => {
    renderModal();

    expect(screen.getByTestId('modal-title')).toHaveTextContent(
      'Test Modal Title'
    );
    expect(screen.getByTestId('modal-desc')).toHaveTextContent(
      'Test description'
    );
  });

  it('does not render modal content when closed', () => {
    render(
      <Modal open={false} onOpenChange={vi.fn()}>
        <Modal.Content data-testid="modal-content">Test</Modal.Content>
      </Modal>
    );
    expect(screen.queryByTestId('modal-content')).not.toBeInTheDocument();
  });

  it('renders title and description with correct tags and classes', () => {
    renderModal();

    const title = screen.getByTestId('modal-title');
    const desc = screen.getByTestId('modal-desc');

    expect(title.tagName).toBe('H2');
    expect(title).toHaveClass('test-title');
    expect(desc.tagName).toBe('P');
    expect(desc).toHaveClass('test-desc');
  });

  it('renders close button and calls onOpenChange when clicked', async () => {
    const { onOpenChange } = renderModal();
    const closeBtn = screen.getByTestId('modal-close');

    expect(closeBtn).toBeInTheDocument();

    await userEvent.click(closeBtn);

    expect(onOpenChange).toHaveBeenCalledWith(false);
  });

  it('has correct ARIA attributes and links title/description', () => {
    renderModal();

    const content = screen.getByRole('dialog', { hidden: true });

    expect(content).toHaveAttribute('aria-modal', 'true');

    const labelledby = content.getAttribute('aria-labelledby');
    const describedby = content.getAttribute('aria-describedby');

    if (labelledby) {
      expect(document.getElementById(labelledby)).toHaveTextContent(
        'Test Modal Title'
      );
    }

    if (describedby) {
      expect(document.getElementById(describedby)).toHaveTextContent(
        'Test description'
      );
    }
  });

  it('handles double open/close calls without error', () => {
    const onOpenChange = vi.fn();

    const { rerender } = render(
      <Modal open={true} onOpenChange={onOpenChange}>
        <Modal.Content data-testid="modal-content">Test</Modal.Content>
      </Modal>
    );

    rerender(
      <Modal open={false} onOpenChange={onOpenChange}>
        <Modal.Content data-testid="modal-content">Test</Modal.Content>
      </Modal>
    );

    rerender(
      <Modal open={true} onOpenChange={onOpenChange}>
        <Modal.Content data-testid="modal-content">Test</Modal.Content>
      </Modal>
    );

    expect(true).toBe(true);
  });

  it('passes custom className to content, title, description, and close', () => {
    renderModal();

    expect(screen.getByRole('dialog', { hidden: true })).toHaveClass(
      'test-content'
    );
    expect(screen.getByTestId('modal-title')).toHaveClass('test-title');
    expect(screen.getByTestId('modal-desc')).toHaveClass('test-desc');
    expect(screen.getByTestId('modal-close')).toHaveClass('test-close');
  });
});
