import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { screen, fireEvent, waitFor, cleanup } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import OTPInput from '../../../components/otp/otp';
import { customRender } from '../../../tests/utils/custom-render';
import '@testing-library/jest-dom';

vi.mock('@/components/alert/alert', () => ({
  Alert: ({ message, variant }: { message: string; variant: string }) => (
    <div data-testid="alert" data-variant={variant}>
      {message}
    </div>
  )
}));

describe('OTPInput Component', () => {
  const defaultProps = {
    length: 4,
    onChange: vi.fn(),
    setInputError: vi.fn(),
    className: 'test-class'
  };

  let user: ReturnType<typeof userEvent.setup>;

  beforeEach(() => {
    user = userEvent.setup();
    vi.clearAllMocks();
  });

  afterEach(() => {
    cleanup();
  });

  describe('Initial Render and Focus', () => {
    it('should render 4 input fields by default', () => {
      customRender(<OTPInput {...defaultProps} />);

      const inputs = screen.getAllByRole('textbox');
      expect(inputs).toHaveLength(4);
    });

    it('should focus on the first input when component mounts', () => {
      customRender(<OTPInput {...defaultProps} />);

      const firstInput = screen.getAllByRole('textbox')[0];
      expect(firstInput).toHaveFocus();
    });

    it('should render custom number of inputs when length prop is provided', () => {
      customRender(<OTPInput {...defaultProps} length={6} />);

      const inputs = screen.getAllByRole('textbox');
      expect(inputs).toHaveLength(6);
    });
  });

  describe('Valid Numeric Input Behavior', () => {
    it('should accept single digit input and move to next field', async () => {
      customRender(<OTPInput {...defaultProps} />);

      const inputs = screen.getAllByRole('textbox');

      await user.type(inputs[0], '1');

      expect(inputs[0]).toHaveValue('1');
      expect(inputs[1]).toHaveFocus();
      expect(defaultProps.onChange).toHaveBeenCalledWith('1');
      expect(defaultProps.setInputError).toHaveBeenCalledWith([]);
    });

    it('should handle sequential input across all fields', async () => {
      customRender(<OTPInput {...defaultProps} />);

      const inputs = screen.getAllByRole('textbox');

      await user.type(inputs[0], '1');
      await user.type(inputs[1], '2');
      await user.type(inputs[2], '3');
      await user.type(inputs[3], '4');

      expect(inputs[0]).toHaveValue('1');
      expect(inputs[1]).toHaveValue('2');
      expect(inputs[2]).toHaveValue('3');
      expect(inputs[3]).toHaveValue('4');

      // Should call onChange with complete OTP
      await waitFor(() => {
        expect(defaultProps.onChange).toHaveBeenLastCalledWith('1234');
      });
    });
  });

  describe('Invalid Character Input Behavior', () => {
    it('should show error when user types a letter', async () => {
      customRender(<OTPInput {...defaultProps} />);

      const firstInput = screen.getAllByRole('textbox')[0];

      await user.type(firstInput, 'a');

      expect(firstInput).toHaveValue('a');
      expect(defaultProps.setInputError).toHaveBeenCalledWith([
        'Please enter numbers only.'
      ]);
    });

    it('should show error when user types special characters', async () => {
      customRender(<OTPInput {...defaultProps} />);

      const firstInput = screen.getAllByRole('textbox')[0];

      await user.type(firstInput, '@');

      expect(firstInput).toHaveValue('@');
      expect(defaultProps.setInputError).toHaveBeenCalledWith([
        'Please enter numbers only.'
      ]);
    });

    it('should show error when user types mixed alphanumeric characters', async () => {
      customRender(<OTPInput {...defaultProps} />);

      const firstInput = screen.getAllByRole('textbox')[0];
      const secondInput = screen.getAllByRole('textbox')[1];

      await user.type(firstInput, '1a');

      expect(firstInput).toHaveValue('1');
      expect(secondInput).toHaveValue('a');
      expect(defaultProps.setInputError).toHaveBeenCalledWith([
        'Please enter numbers only.'
      ]);
    });

    it('should clear errors when valid input is entered after invalid input', async () => {
      customRender(<OTPInput {...defaultProps} />);

      const firstInput = screen.getAllByRole('textbox')[0];

      await user.type(firstInput, 'a');
      expect(defaultProps.setInputError).toHaveBeenCalledWith([
        'Please enter numbers only.'
      ]);

      await user.keyboard('{Backspace}');

      await user.type(firstInput, '5');
      expect(firstInput).toHaveValue('5');
      expect(defaultProps.setInputError).toHaveBeenCalledWith([]);
    });

    it('should handle invalid characters in middle of sequence', async () => {
      customRender(<OTPInput {...defaultProps} />);

      const inputs = screen.getAllByRole('textbox');

      await user.type(inputs[0], '1');
      await user.type(inputs[1], '2');

      await user.click(inputs[1]);
      await user.keyboard('{Backspace}');

      await user.type(inputs[1], 'x');

      expect(inputs[0]).toHaveValue('1');
      expect(inputs[1]).toHaveValue('x');
      expect(defaultProps.setInputError).toHaveBeenLastCalledWith([
        'Please enter numbers only.'
      ]);
    });
  });

  describe('Backspace Navigation Behavior', () => {
    it('should move to previous input when backspace is pressed on empty field', async () => {
      customRender(<OTPInput {...defaultProps} />);

      const inputs = screen.getAllByRole('textbox');

      // Enter digit in first input to move to second
      await user.type(inputs[0], '1');
      expect(inputs[1]).toHaveFocus();

      // Press backspace on empty second input
      await user.keyboard('{Backspace}');

      expect(inputs[0]).toHaveFocus();
    });

    it('should clear current input when backspace is pressed on filled field', async () => {
      customRender(<OTPInput {...defaultProps} />);

      const inputs = screen.getAllByRole('textbox');

      // Enter digits
      await user.type(inputs[0], '1');
      await user.type(inputs[1], '2');

      // Press backspace on filled second input
      inputs[1].focus();
      await user.keyboard('{Backspace}');

      expect(inputs[1]).toHaveValue('');
      expect(inputs[1]).toHaveFocus();
    });

    it('should update onChange when input is cleared with backspace', async () => {
      customRender(<OTPInput {...defaultProps} />);

      const inputs = screen.getAllByRole('textbox');

      await user.type(inputs[0], '1');
      await user.type(inputs[1], '2');

      // Clear second input with backspace
      inputs[1].focus();
      await user.keyboard('{Backspace}');

      await waitFor(() => {
        expect(defaultProps.onChange).toHaveBeenLastCalledWith('1');
      });
    });
  });

  describe('Paste Behavior - Valid Content', () => {
    it('should handle pasting valid 4-digit code', async () => {
      customRender(<OTPInput {...defaultProps} />);

      const container =
        screen.getAllByRole('textbox')[0].parentElement?.parentElement;

      if (container) {
        await user.click(container);

        fireEvent.paste(container, {
          clipboardData: {
            getData: () => '1234'
          }
        });

        const inputs = screen.getAllByRole('textbox');
        expect(inputs[0]).toHaveValue('1');
        expect(inputs[1]).toHaveValue('2');
        expect(inputs[2]).toHaveValue('3');
        expect(inputs[3]).toHaveValue('4');

        expect(defaultProps.setInputError).toHaveBeenCalledWith([]);

        await waitFor(() => {
          expect(defaultProps.onChange).toHaveBeenLastCalledWith('1234');
        });
      }
    });

    it('should handle pasting fewer digits than input length', async () => {
      customRender(<OTPInput {...defaultProps} />);

      const container =
        screen.getAllByRole('textbox')[0].parentElement?.parentElement;

      if (container) {
        await user.click(container);

        fireEvent.paste(container, {
          clipboardData: {
            getData: () => '12'
          }
        });

        const inputs = screen.getAllByRole('textbox');
        expect(inputs[0]).toHaveValue('1');
        expect(inputs[1]).toHaveValue('2');
        expect(inputs[2]).toHaveValue('');
        expect(inputs[3]).toHaveValue('');

        expect(inputs[2]).toHaveFocus();

        await waitFor(() => {
          expect(defaultProps.onChange).toHaveBeenLastCalledWith('12');
        });
      }
    });

    it('should handle pasting more digits than input length', async () => {
      customRender(<OTPInput {...defaultProps} />);

      const container =
        screen.getAllByRole('textbox')[0].parentElement?.parentElement;

      if (container) {
        await user.click(container);

        fireEvent.paste(container, {
          clipboardData: {
            getData: () => '123456678'
          }
        });

        const inputs = screen.getAllByRole('textbox');
        expect(inputs[0]).toHaveValue('1');
        expect(inputs[1]).toHaveValue('2');
        expect(inputs[2]).toHaveValue('3');
        expect(inputs[3]).toHaveValue('4');

        await waitFor(() => {
          expect(defaultProps.onChange).toHaveBeenLastCalledWith('1234');
        });
      }
    });

    it('should clear existing values when pasting new content', async () => {
      customRender(<OTPInput {...defaultProps} />);

      const inputs = screen.getAllByRole('textbox');
      const container = inputs[0].parentElement?.parentElement;

      // First enter some values manually
      await user.type(inputs[0], '9');
      await user.type(inputs[1], '8');

      if (container) {
        await user.click(container);

        fireEvent.paste(container, {
          clipboardData: {
            getData: () => '5678'
          }
        });

        expect(inputs[0]).toHaveValue('5');
        expect(inputs[1]).toHaveValue('6');
        expect(inputs[2]).toHaveValue('7');
        expect(inputs[3]).toHaveValue('8');

        await waitFor(() => {
          expect(defaultProps.onChange).toHaveBeenLastCalledWith('5678');
        });
      }
    });
  });

  describe('Paste Behavior - Invalid Content', () => {
    it('should show error when pasting content with letters', async () => {
      customRender(<OTPInput {...defaultProps} />);

      const container =
        screen.getAllByRole('textbox')[0].parentElement?.parentElement;

      if (container) {
        await user.click(container);

        fireEvent.paste(container, {
          clipboardData: {
            getData: () => '1234'
          }
        });

        expect(defaultProps.setInputError).toHaveBeenCalledWith([]);
      }
    });

    it('should show error when pasting content with special characters', async () => {
      customRender(<OTPInput {...defaultProps} />);

      const container =
        screen.getAllByRole('textbox')[0].parentElement?.parentElement;

      if (container) {
        await user.click(container);

        fireEvent.paste(container, {
          clipboardData: {
            getData: () => '12@4'
          }
        });

        expect(defaultProps.setInputError).toHaveBeenCalledWith([
          'Oops! Looks like your pasted content has non-numeric characters.'
        ]);
      }
    });

    it('should show error when pasting mixed alphanumeric content', async () => {
      customRender(<OTPInput {...defaultProps} />);

      const container =
        screen.getAllByRole('textbox')[0].parentElement?.parentElement;

      if (container) {
        await user.click(container);

        fireEvent.paste(container, {
          clipboardData: {
            getData: () => 'ab12'
          }
        });

        expect(defaultProps.setInputError).toHaveBeenCalledWith([
          'Oops! Looks like your pasted content has non-numeric characters.'
        ]);
      }
    });

    it('should show error when pasting empty or whitespace content', async () => {
      customRender(<OTPInput {...defaultProps} />);

      const container =
        screen.getAllByRole('textbox')[0].parentElement?.parentElement;

      if (container) {
        await user.click(container);

        fireEvent.paste(container, {
          clipboardData: {
            getData: () => '   '
          }
        });

        expect(defaultProps.setInputError).toHaveBeenCalledWith([
          'Oops! Looks like your pasted content has non-numeric characters.'
        ]);
      }
    });
  });

  describe('Input Attributes and Accessibility', () => {
    it('should have correct input attributes for accessibility', () => {
      customRender(<OTPInput {...defaultProps} />);

      const inputs = screen.getAllByRole('textbox');

      inputs.forEach((input) => {
        expect(input).toHaveAttribute('name', 'otp');
        expect(input).toHaveAttribute('type', 'text');
        expect(input).toHaveAttribute('inputMode', 'numeric');
        expect(input).toHaveAttribute('maxLength', '1');
        expect(input).toHaveAttribute('autoComplete', 'one-time-code');
      });
    });

    it('should apply custom className to inputs', () => {
      customRender(<OTPInput {...defaultProps} className="custom-class" />);

      const inputs = screen.getAllByRole('textbox');

      inputs.forEach((input) => {
        expect(input).toHaveClass('custom-class');
      });
    });
  });

  describe('Edge Cases', () => {
    it('should handle rapid sequential typing', async () => {
      customRender(<OTPInput {...defaultProps} />);

      const inputs = screen.getAllByRole('textbox');

      // Rapid typing simulation
      await user.type(inputs[0], '1');
      await user.type(inputs[1], '2');
      await user.type(inputs[2], '3');
      await user.type(inputs[3], '4');

      await waitFor(() => {
        expect(defaultProps.onChange).toHaveBeenLastCalledWith('1234');
      });
    });

    it('should handle clicking between inputs during typing', async () => {
      customRender(<OTPInput {...defaultProps} />);

      const inputs = screen.getAllByRole('textbox');

      await user.type(inputs[0], '1');
      await user.click(inputs[2]);
      await user.type(inputs[2], '3');
      await user.click(inputs[1]);
      await user.type(inputs[1], '2');

      expect(inputs[0]).toHaveValue('1');
      expect(inputs[1]).toHaveValue('2');
      expect(inputs[2]).toHaveValue('3');

      await waitFor(() => {
        expect(defaultProps.onChange).toHaveBeenLastCalledWith('123');
      });
    });

    it('should handle zero as valid input', async () => {
      customRender(<OTPInput {...defaultProps} />);

      const firstInput = screen.getAllByRole('textbox')[0];

      await user.type(firstInput, '0');

      expect(firstInput).toHaveValue('0');
      expect(defaultProps.setInputError).toHaveBeenCalledWith([]);
      expect(defaultProps.onChange).toHaveBeenCalledWith('0');
    });
  });
});
