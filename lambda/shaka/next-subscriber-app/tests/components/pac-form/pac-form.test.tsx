import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, within } from '@testing-library/react';
import '@testing-library/jest-dom';
import { NumberPortingFormContainer } from '@/components/number-porting-form/number-porting-form';
import { customRender } from '@/tests/utils/custom-render';
import userEvent from '@testing-library/user-event';
import { UK_PUBLIC_HOLIDAYS } from '@/utils/constants';
import { getNextWorkingDay } from '@/utils/helpers';
import { getDesiredCodeChangeDate } from '@/src/uswitch/utils/helpers';

const getAutoSelectDate = () => {
  const today = new Date();
  let daysToAdd;

  switch (today.getDay()) {
    case 5: // Friday
      daysToAdd = 3;
      break;
    case 6: // Saturday
      daysToAdd = 2;
      break;
    case 0: // Sunday
      daysToAdd = 1;
      break;
    default:
      daysToAdd = 1;
  }

  const nextWorkingDayDate = new Date(today);
  nextWorkingDayDate.setDate(today.getDate() + daysToAdd);

  return {
    year: nextWorkingDayDate.getFullYear().toString(),
    month: (nextWorkingDayDate.getMonth() + 1).toString().padStart(2, '0'),
    day: nextWorkingDayDate.getDate().toString().padStart(2, '0')
  };
};

describe('NumberPortingForm', () => {
  const mockHandleSubmit = vi.fn();
  const defaultFormData = {
    pacCode: '',
    incoming_phone_number: '',
    desired_date: ''
  };

  const renderPACForm = (props = {}) => {
    return customRender(
      <>
        <NumberPortingFormContainer
          setErrors={() => {}}
          errors={[]}
          handleSubmit={mockHandleSubmit}
          formData={defaultFormData}
          {...props}
        />
        <button type="submit" form="pacForm">
          Continue
        </button>
      </>
    );
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('successfully submits WITH selected date', async () => {
    const user = userEvent.setup();
    renderPACForm({});

    const pacCodeInput = screen.getByLabelText('Your PAC code');
    const phoneNumberInput = screen.getByLabelText('Your phone number');
    const submitButton = screen.getByRole('button', { name: /continue/i });

    await user.type(pacCodeInput, 'PAC123456');
    await user.type(phoneNumberInput, '07123456789');
    await user.click(submitButton);

    const { day, month, year } = getAutoSelectDate();
    const desired_date = getDesiredCodeChangeDate(day, month, year);

    expect(mockHandleSubmit).toHaveBeenCalledWith({
      pacCode: 'PAC123456',
      incoming_phone_number: '07123456789',
      desired_date
      // for now
      // subscription_id: null
    });
  });

  it('shows errors when form is invalid', async () => {
    const user = userEvent.setup();
    renderPACForm({
      errors: [
        'pacCode: Invalid PAC code format',
        'incoming_phone_number: Phone number is required'
      ]
    });

    const pacCodeInput = screen.getByLabelText('Your PAC code');
    const phoneNumberInput = screen.getByLabelText('Your phone number');

    await user.type(pacCodeInput, 'abc');
    await user.type(phoneNumberInput, '07123456');

    expect(screen.getAllByRole('alert')).toHaveLength(2);
  });

  it('preserves user input after invalid submission', async () => {
    const user = userEvent.setup();
    renderPACForm({
      errors: [
        'pacCode: Invalid PAC code format',
        'incoming_phone_number: Phone number is required'
      ]
    });

    const pacCodeInput = screen.getByLabelText('Your PAC code');
    const phoneNumberInput = screen.getByLabelText('Your phone number');

    await user.type(pacCodeInput, 'abc');
    await user.type(phoneNumberInput, '07123456');

    expect(pacCodeInput).toHaveValue('abc');
    expect(phoneNumberInput).toHaveValue('07123456');
  });

  it('renders with correct default values', async () => {
    renderPACForm();

    const autoSelectedDate = getAutoSelectDate();

    const daySelect = screen.getByLabelText('Day');
    const monthSelect = screen.getByLabelText('Month');
    const yearSelect = screen.getByLabelText('Year');

    expect(yearSelect).toHaveValue(autoSelectedDate.year);
    expect(monthSelect).toHaveValue(autoSelectedDate.month);
    expect(daySelect).toHaveValue(autoSelectedDate.day);
  });

  it('enables year select when dates span into next year (December scenario)', async () => {
    const originalDate = global.Date;
    const mockDate = new Date(2025, 11, 20);

    // @ts-expect-error - Testing
    global.Date = class extends Date {
      // @ts-expect-error - Testing
      constructor(...args) {
        if (args.length === 0) {
          return new originalDate(mockDate);
        }
        // @ts-expect-error - Testing
        return new originalDate(...args);
      }
    };

    try {
      renderPACForm();

      const yearSelect = screen.getByLabelText('Year');

      const yearOptions = within(yearSelect).getAllByRole('option');
      expect(yearOptions).toHaveLength(2);
      expect(yearOptions[0]).toHaveValue('2025');
      expect(yearOptions[1]).toHaveValue('2026');
    } finally {
      global.Date = originalDate;
    }
  });

  it('should skip weekends and UK bank holidays when calculating next working day', async () => {
    const friday = new Date('2025-08-22T12:00:00Z');
    vi.setSystemTime(friday);

    const expectedDate = getNextWorkingDay(friday, UK_PUBLIC_HOLIDAYS);
    const expectedDay = expectedDate.getDate().toString().padStart(2, '0');
    const expectedMonth = (expectedDate.getMonth() + 1)
      .toString()
      .padStart(2, '0');
    const expectedYear = expectedDate.getFullYear().toString();

    renderPACForm();

    const daySelect = screen.getByLabelText(/day/i);
    const monthSelect = screen.getByLabelText(/month/i);
    const yearSelect = screen.getByLabelText(/year/i);
    expect(daySelect).toHaveValue(expectedDay);
    expect(monthSelect).toHaveValue(expectedMonth);
    expect(yearSelect).toHaveValue(expectedYear);
  });

  describe('Accessibility tests', () => {
    it('has proper form labelling and associations', () => {
      renderPACForm();

      expect(screen.getByLabelText('Your PAC code')).toBeInTheDocument();
      expect(screen.getByLabelText('Your phone number')).toBeInTheDocument();
      expect(screen.getByLabelText('Day')).toBeInTheDocument();
      expect(screen.getByLabelText('Month')).toBeInTheDocument();
      expect(screen.getByLabelText('Year')).toBeInTheDocument();
    });

    it('displays errors with proper accessibility attributes', async () => {
      renderPACForm({
        errors: ['pacCode: Invalid PAC code format']
      });

      const errorMessage = screen.getByText('Invalid PAC code format');
      expect(errorMessage).toBeInTheDocument();

      expect(errorMessage.closest('[role="alert"]')).toBeInTheDocument();
    });

    it('supports keyboard navigation through the form', async () => {
      const user = userEvent.setup();
      renderPACForm();

      const pacCodeInput = screen.getByLabelText('Your PAC code');
      pacCodeInput.focus();
      expect(document.activeElement).toBe(pacCodeInput);

      await user.tab();
      const phoneNumberInput = screen.getByLabelText('Your phone number');
      expect(document.activeElement).toBe(phoneNumberInput);

      await user.tab();
      const daySelect = screen.getByLabelText('Day');
      expect(document.activeElement).toBe(daySelect);

      await user.tab();
      const monthSelect = screen.getByLabelText('Month');
      expect(document.activeElement).toBe(monthSelect);

      await user.tab();
      const yearSelect = screen.getByLabelText('Year');
      expect(document.activeElement).toBe(yearSelect);

      await user.tab();
      const submitButton = screen.getByRole('button', { name: /continue/i });
      expect(document.activeElement).toBe(submitButton);
    });

    it('focuses first field with error when form has errors', async () => {
      renderPACForm({
        errors: [
          'pacCode: Invalid PAC code',
          'incoming_phone_number: Invalid phone number'
        ]
      });

      const pacCodeInput = screen.getByLabelText('Your PAC code');

      expect(document.activeElement).toBe(pacCodeInput);
    });
  });

  describe('Mobile input modes', () => {
    it('has numeric inputMode for phone number field', () => {
      renderPACForm();

      const phoneNumberInput = screen.getByLabelText('Your phone number');
      expect(phoneNumberInput).toHaveAttribute('inputMode', 'numeric');
    });

    it('has appropriate input types for all fields', () => {
      renderPACForm();

      const pacCodeInput = screen.getByLabelText('Your PAC code');
      expect(pacCodeInput).toHaveAttribute('type', 'text');

      const phoneNumberInput = screen.getByLabelText('Your phone number');
      expect(phoneNumberInput).toHaveAttribute('type', 'tel');

      const daySelect = screen.getByLabelText('Day');
      expect(daySelect.tagName.toLowerCase()).toBe('select');

      const monthSelect = screen.getByLabelText('Month');
      expect(monthSelect.tagName.toLowerCase()).toBe('select');

      const yearSelect = screen.getByLabelText('Year');
      expect(yearSelect.tagName.toLowerCase()).toBe('select');
    });
  });
});
