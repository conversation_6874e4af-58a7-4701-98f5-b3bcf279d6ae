import { test, expect } from '@playwright/test';

test.describe('Signup Page', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('http://localhost:3000/signup/register');
  });

  test('renders the signup form and social buttons', async ({ page }) => {
    await expect(
      page.getByRole('heading', { name: /user details/i })
    ).toBeVisible();

    await expect(
      page.getByRole('button', { name: /sign up with google/i })
    ).toBeVisible();
    await expect(
      page.getByRole('button', { name: /sign up with apple/i })
    ).toBeVisible();

    await expect(page.getByText(/or use your email/i)).toBeVisible();

    await expect(page.getByLabel(/name/i)).toBeVisible();
    await expect(page.getByLabel(/email/i)).toBeVisible();
    await expect(page.getByLabel(/password/i)).toBeVisible();

    await expect(page.getByRole('combobox', { name: /day/i })).toBeVisible();
    await expect(page.getByRole('combobox', { name: /month/i })).toBeVisible();
    await expect(page.getByRole('combobox', { name: /year/i })).toBeVisible();

    await expect(page.getByRole('button', { name: /continue/i })).toBeVisible();
  });

  test('shows validation errors when submitting empty form', async ({
    page
  }) => {
    await page.getByRole('button', { name: /continue/i }).click();

    await expect(page.getByText(/Name is required/i)).toBeVisible();
    await expect(page.getByText(/Email address is required/i)).toBeVisible();
    await expect(
      page.getByText(/Password must be at least 8 characters long/i)
    ).toBeVisible();
    await expect(page.getByText(/Please select a date/i)).toBeVisible();
  });

  test('shows error for invalid password - too short', async ({ page }) => {
    await page.getByLabel(/name/i).fill('Test User');
    await page.getByLabel(/email/i).fill('<EMAIL>');
    await page.getByLabel(/password/i).fill('Pawel');
    await page.getByRole('combobox', { name: 'Day' }).selectOption('10');
    await page.getByRole('combobox', { name: 'Month' }).selectOption('12');
    await page.getByRole('combobox', { name: 'Year' }).selectOption('1985');

    await page.getByRole('button', { name: /continue/i }).click();

    await expect(
      page.getByText(/Password must be at least 8 characters long/i)
    ).toBeVisible();
  });

  test('shows error for invalid password - lack of special character', async ({
    page
  }) => {
    await page.getByLabel(/name/i).fill('Test User');
    await page.getByLabel(/email/i).fill('<EMAIL>');
    await page.getByLabel(/password/i).fill('Pawel123');
    await page.getByRole('combobox', { name: 'Day' }).selectOption('10');
    await page.getByRole('combobox', { name: 'Month' }).selectOption('12');
    await page.getByRole('combobox', { name: 'Year' }).selectOption('1985');

    await page.getByRole('button', { name: /continue/i }).click();

    await expect(
      page.getByText(
        /Password must contain at least one uppercase letter, one lowercase letter, one number and one special character/i
      )
    ).toBeVisible();
  });

  test('can fill and submit the form (happy path)', async ({ page }) => {
    await page.getByLabel(/name/i).fill('Jane Doe');
    await page.getByLabel(/email/i).fill('<EMAIL>');
    await page.getByLabel(/password/i).fill('SuperSecret123!');
    await page.getByRole('combobox', { name: 'Day' }).selectOption('10');
    await page.getByRole('combobox', { name: 'Month' }).selectOption('12');
    await page.getByRole('combobox', { name: 'Year' }).selectOption('1985');

    await page.getByRole('button', { name: /continue/i }).click();

    // await expect(
    //   page.getByRole('button', { name: /continue/i })
    // ).toBeDisabled();

    // await expect(page).toHaveURL('/signup/success');
  });

  test('social signup buttons are clickable', async ({ page }) => {
    await page.getByRole('button', { name: /sign up with google/i }).click();
    await page.getByRole('button', { name: /sign up with apple/i }).click();
  });

  test('date of birth selection works', async ({ page }) => {
    await page.getByRole('combobox', { name: 'Day' }).selectOption('10');
    await page.getByRole('combobox', { name: 'Month' }).selectOption('12');
    await page.getByRole('combobox', { name: 'Year' }).selectOption('1985');

    await expect(page.getByRole('combobox', { name: 'Day' })).toHaveValue('10');
    await expect(page.getByRole('combobox', { name: 'Month' })).toHaveValue(
      '12'
    );
    await expect(page.getByRole('combobox', { name: 'Year' })).toHaveValue(
      '1985'
    );
  });

  test('shows network error', async ({ page }) => {
    await page.getByLabel(/name/i).fill('Jane Doe');
    await page.getByLabel(/email/i).fill('<EMAIL>');
    await page.getByLabel(/password/i).fill('SuperSecret123!');
    await page.getByRole('combobox', { name: 'Day' }).selectOption('10');
    await page.getByRole('combobox', { name: 'Month' }).selectOption('12');
    await page.getByRole('combobox', { name: 'Year' }).selectOption('1985');

    await page.getByRole('button', { name: /continue/i }).click();
    await expect(page.getByRole('alert').first()).toBeVisible();
  });
});
