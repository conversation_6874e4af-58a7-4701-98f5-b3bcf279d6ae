import { test, expect } from '@playwright/test';
import { seedAuthenticatedState } from '@/src/uswitch/tests/e2e/utils/auth';

test.describe('OTP Verification Page', () => {
  test.beforeEach(async ({ page }) => {
    await page.route('**/auth/verify/', async (route) => {
      const json = { success: true };
      await route.fulfill({ json });
    });

    await page.route('**/auth/verify/resend/', async (route) => {
      const json = { success: true };
      await route.fulfill({ json });
    });

    await seedAuthenticatedState(page);
    await page.goto('http://localhost:3000/signup/otp');
  });

  test('shows 4 OTP input boxes', async ({ page }) => {
    await expect(page.locator('input[name="otp"]')).toHaveCount(4);
  });

  test('shows alert when typing non-numeric character', async ({ page }) => {
    await page.locator('input[name="otp"]').first().fill('a');
    await expect(page.getByRole('alert').first()).toBeVisible();
  });

  test('shows alert when pasting non-numeric string', async ({ page }) => {
    await page.locator('input[name="otp"]').first().focus();
    await page.evaluate(() => {
      const clipboardEvent = new ClipboardEvent('paste', {
        clipboardData: new DataTransfer(),
        bubbles: true,
        cancelable: true
      });
      clipboardEvent.clipboardData?.setData('text/plain', '12ab');
      document.activeElement?.dispatchEvent(clipboardEvent);
    });
    await expect(page.getByRole('alert').first()).toBeVisible();
  });

  test('shows alert if less than 4 digits and submit', async ({ page }) => {
    await page.locator('input[name="otp"]').nth(0).fill('1');
    await page.locator('input[name="otp"]').nth(1).fill('2');
    await page.getByRole('button', { name: /continue/i }).click();
    await expect(page.getByRole('alert').first()).toBeVisible();
  });

  test('resets OTP errors when requesting new OTP code', async ({ page }) => {
    await page.locator('input[name="otp"]').nth(0).fill('a');
    await page.getByRole('button', { name: /new one/i }).click();
    for (let i = 0; i < 4; i++) {
      await expect(page.locator('input[name="otp"]').nth(i)).toHaveValue('');
    }
  });

  // these test faild ue to not working mock !!
  //   test('shows success alert when a new otp code is requested', async ({
  //     page
  //   }) => {
  //     await page.getByRole('button', { name: /new one/i }).click();
  //     const successMessage = page.getByText(
  //       'Your PAC code has been successfully sent'
  //     );
  //     await expect(successMessage).toBeVisible();
  //   });
  //
  //   test('shows success alert message when submitting valid 4 digit input', async ({
  //     page
  //   }) => {
  //     await page.locator('input[name="otp"]').nth(0).fill('1');
  //     await page.locator('input[name="otp"]').nth(1).fill('2');
  //     await page.locator('input[name="otp"]').nth(2).fill('3');
  //     await page.locator('input[name="otp"]').nth(3).fill('4');
  //     await page.getByRole('button', { name: /continue/i }).click();
  //     const successMessage = page.getByText(
  //       'Your PAC code has been successfully sent'
  //     );
  //     await expect(successMessage).toBeVisible();
  //   });
});
