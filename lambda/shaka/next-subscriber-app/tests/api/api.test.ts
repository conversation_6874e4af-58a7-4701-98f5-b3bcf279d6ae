import { describe, it, expect } from 'vitest';
import { API_ENDPOINTS } from '@/auth/api/endpoints';

// Happy and unhappy path tests for auth endpoints

describe('Auth API endpoints (MSW contract)', () => {
  it('refresh: should succeed with valid refresh token', async () => {
    const response = await fetch(API_ENDPOINTS.auth.refresh, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ refreshToken: 'signup-access-token' })
    });

    expect(response.status).toBe(200);

    const data = await response.json();
    expect(data.tokens.accessToken).toBe('new-mock-access-token');
  });

  it('refresh: should fail with invalid refresh token', async () => {
    const response = await fetch(API_ENDPOINTS.auth.refresh, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ refreshToken: 'invalid' })
    });

    expect(response.status).toBe(401);

    const data = await response.json();
    expect(data.message).toBe('Invalid or expired refresh token');
  });

  it('logout: should succeed with valid refresh token', async () => {
    const response = await fetch(API_ENDPOINTS.auth.logout, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ refreshToken: 'signup-access-token' })
    });

    expect(response.status).toBe(200);

    const data = await response.json();
    expect(data.message).toBe('Logged out');
  });

  it('logout: should fail with invalid refresh token', async () => {
    const response = await fetch(API_ENDPOINTS.auth.logout, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ refreshToken: 'invalid' })
    });

    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data.message).toBe('Invalid refresh token');
  });

  it('signup: should succeed for new user', async () => {
    const response = await fetch(API_ENDPOINTS.auth.signup, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
      })
    });

    expect(response.status).toBe(200);

    const data = await response.json();
    expect(data.tokens.accessToken).toBe('signup-access-token');
  });

  it('signup: should fail for existing user', async () => {
    const response = await fetch(API_ENDPOINTS.auth.signup, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
      })
    });

    expect(response.status).toBe(409);

    const data = await response.json();
    expect(data.message).toBe('User already exists');
  });
});
