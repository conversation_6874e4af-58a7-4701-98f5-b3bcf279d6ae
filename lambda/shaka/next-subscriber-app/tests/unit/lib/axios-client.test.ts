import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { vi } from 'vitest';
import { AxiosClient } from '@/lib/axios-client';
import { TokenService } from '@/auth/tokens/token-service';
import { getClientConfig } from '@/client-config/client-config';

// Mock the config module with proper TypeScript types
vi.mock('@/client-config/client-config', () => ({
  getClientConfig: vi.fn().mockReturnValue({
    apiBaseUrl: 'https://api.shaka.tel',
    clientId: 'default-test-client'
  })
}));

// Mock the actual implementation for specific tests
// @ts-expect-error cannot figure it out
const mockGetClientConfig = getClientConfig as vi.Mock<typeof getClientConfig>;

describe('AxiosClient Base URL Construction', () => {
  let tokenService: TokenService;

  beforeEach(() => {
    // Reset all mocks before each test
    vi.clearAllMocks();

    // Reset to default mock implementation
    mockGetClientConfig.mockReturnValue({
      apiBaseUrl: 'https://api.shaka.tel',
      clientId: 'default-test-client'
    });

    // Setup TokenService mock
    tokenService = {
      getAccessToken: vi.fn(),
      setTokens: vi.fn(),
      clearTokens: vi.fn()
    } as unknown as TokenService;
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Basic URL Construction', () => {
    it('should use default client ID from config when no options provided', () => {
      const client = new AxiosClient(tokenService);
      const expectedUrl =
        'https://api.shaka.tel/next/api/v1/default-test-client';
      expect(client['client'].defaults.baseURL).toBe(expectedUrl);
    });

    it('should use provided client ID in options', () => {
      const customClientId = 'custom-client-123';
      const client = new AxiosClient(tokenService, {
        clientId: customClientId
      });
      const expectedUrl = `https://api.shaka.tel/next/api/v1/${customClientId}`;
      expect(client['client'].defaults.baseURL).toBe(expectedUrl);
    });

    it('should use provided base URL in options', () => {
      const customBaseUrl = 'https://custom-api.example.com';
      const client = new AxiosClient(tokenService, { baseURL: customBaseUrl });
      expect(client['client'].defaults.baseURL).toBe(customBaseUrl);
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty string as client ID by using default', () => {
      mockGetClientConfig.mockReturnValueOnce({
        apiBaseUrl: 'https://api.shaka.tel',
        clientId: 'default-from-config'
      });
      const client = new AxiosClient(tokenService, { clientId: '' });
      const expectedUrl =
        'https://api.shaka.tel/next/api/v1/default-from-config';
      expect(client['client'].defaults.baseURL).toBe(expectedUrl);
    });

    it('should handle null/undefined client ID by using default', () => {
      mockGetClientConfig.mockReturnValue({
        apiBaseUrl: 'https://api.shaka.tel',
        clientId: 'default-from-config'
      });

      const client1 = new AxiosClient(tokenService, { clientId: null as any });
      const client2 = new AxiosClient(tokenService, { clientId: undefined });
      const expectedUrl =
        'https://api.shaka.tel/next/api/v1/default-from-config';

      expect(client1['client'].defaults.baseURL).toBe(expectedUrl);
      expect(client2['client'].defaults.baseURL).toBe(expectedUrl);
    });

    it('should handle URL-unsafe characters in client ID', () => {
      const unsafeId = '<EMAIL>';
      const client = new AxiosClient(tokenService, { clientId: unsafeId });
      // Note: The actual encoding should be handled by Axios/HTTP client
      expect(client['client'].defaults.baseURL).toContain(unsafeId);
    });

    it('should handle very long client IDs', () => {
      const longId = 'a'.repeat(1000);
      const client = new AxiosClient(tokenService, { clientId: longId });
      expect(client['client'].defaults.baseURL).toContain(longId);
    });
  });

  describe('URL Construction with Different Base URLs', () => {
    it('should handle base URL with trailing slash', () => {
      mockGetClientConfig.mockReturnValueOnce({
        apiBaseUrl: 'https://api.shaka.tel',
        clientId: 'test-client'
      });

      const client = new AxiosClient(tokenService);
      // The URL will contain a double slash which is valid but not ideal
      const baseUrl = client['client'].defaults.baseURL;
      expect(baseUrl).toBe('https://api.shaka.tel/next/api/v1/test-client');
    });

    it('should handle custom ports in base URL', () => {
      mockGetClientConfig.mockReturnValueOnce({
        apiBaseUrl: 'http://localhost:3000',
        clientId: 'test-client'
      });

      const client = new AxiosClient(tokenService);
      expect(client['client'].defaults.baseURL).toBe(
        'http://localhost:3000/next/api/v1/test-client'
      );
    });
  });

  describe('Configuration Precedence', () => {
    it('should prioritize baseURL over clientId in options', () => {
      const customBaseUrl = 'https://custom-api.example.com';
      const client = new AxiosClient(tokenService, {
        baseURL: customBaseUrl,
        clientId: 'should-be-ignored'
      });
      expect(client['client'].defaults.baseURL).toBe(customBaseUrl);
    });

    it('should use environment config when no options provided', () => {
      mockGetClientConfig.mockReturnValueOnce({
        apiBaseUrl: 'https://staging.api.shaka.tel',
        clientId: 'test-client'
      });

      const client = new AxiosClient(tokenService);
      expect(client['client'].defaults.baseURL).toBe(
        'https://staging.api.shaka.tel/next/api/v1/test-client'
      );
    });
  });

  describe('Type Safety', () => {
    it('should convert non-string client ID to string', () => {
      mockGetClientConfig.mockReturnValueOnce({
        apiBaseUrl: 'https://api.shaka.tel',
        clientId: 'default-client'
      });

      // @ts-expect-error Testing invalid input
      const client = new AxiosClient(tokenService, { clientId: 12345 });
      // The number is converted to string and used in the URL
      expect(client['client'].defaults.baseURL).toContain('12345');
    });
  });
});
