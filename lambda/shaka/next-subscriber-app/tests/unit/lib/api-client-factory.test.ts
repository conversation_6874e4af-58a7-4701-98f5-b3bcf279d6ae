import { describe, it, expect, vi, beforeEach, Mock } from 'vitest';
import { createApiClient } from '@/lib/api-client-factory';
import { AxiosClient } from '@/lib/axios-client';
import { TokenService } from '@/auth/tokens/token-service';

vi.mock('@/lib/axios-client');
vi.mock('@/auth/tokens/token-service');

describe('createApiClient', () => {
  const mockTokenService = new TokenService();
  const mockRefreshToken = vi.fn().mockResolvedValue(undefined);

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic Functionality', () => {
    it('should create an AxiosClient instance with default configuration', () => {
      createApiClient({});

      expect(AxiosClient).toHaveBeenCalledTimes(1);
      expect(AxiosClient).toHaveBeenCalledWith(
        expect.any(TokenService),
        { headers: undefined, timeout: undefined },
        undefined
      );
    });

    it('should use provided TokenService instance', () => {
      createApiClient({ tokenService: mockTokenService });

      expect(AxiosClient).toHaveBeenCalledWith(
        mockTokenService,
        expect.any(Object),
        undefined
      );
    });

    it('should apply custom timeout and headers', () => {
      // Arrange
      const customTimeout = 5000;
      const customHeaders = { 'X-Custom-Header': 'test' };

      createApiClient({
        timeout: customTimeout,
        headers: customHeaders
      });

      expect(AxiosClient).toHaveBeenCalledWith(
        expect.any(TokenService),
        {
          timeout: customTimeout,
          headers: customHeaders
        },
        undefined
      );
    });

    it('should pass through the refresh token function', () => {
      createApiClient({ refreshTokenFn: mockRefreshToken });

      expect(AxiosClient).toHaveBeenCalledWith(
        expect.any(TokenService),
        expect.any(Object),
        mockRefreshToken
      );
    });
  });

  describe('Edge Cases', () => {
    it('should handle null and undefined values in options', () => {
      createApiClient({
        tokenService: undefined,
        timeout: undefined,
        headers: undefined,
        refreshTokenFn: undefined
      });

      expect(AxiosClient).toHaveBeenCalledWith(
        expect.any(TokenService), // Should create default TokenService
        { headers: undefined, timeout: undefined },
        undefined
      );
    });

    it('should merge default and custom headers', () => {
      const defaultHeaders = { 'Content-Type': 'application/json' };
      const customHeaders = { 'X-Custom-Header': 'test' };

      createApiClient({
        headers: { ...defaultHeaders, ...customHeaders }
      });

      expect(AxiosClient).toHaveBeenCalledWith(
        expect.any(TokenService),
        {
          headers: {
            ...defaultHeaders,
            ...customHeaders
          },
          timeout: undefined
        },
        undefined
      );
    });

    it('should handle empty header objects', () => {
      createApiClient({ headers: {} });

      expect(AxiosClient).toHaveBeenCalledWith(
        expect.any(TokenService),
        { headers: {}, timeout: undefined },
        undefined
      );
    });

    it('should handle case-insensitive header keys', () => {
      createApiClient({
        headers: { 'content-type': 'application/json', 'X-API-KEY': '123' }
      });

      const headers = (AxiosClient as unknown as Mock).mock.calls[0][1].headers;
      expect(headers['content-type']).toBe('application/json');
      expect(headers['X-API-KEY']).toBe('123');
    });
  });

  describe('Type Safety', () => {
    it('should enforce type safety for options', () => {
      // This is a compile-time test - the TypeScript compiler will fail if types are incorrect
      const validOptions = {
        tokenService: mockTokenService,
        timeout: 5000,
        headers: { 'Content-Type': 'application/json' },
        refreshTokenFn: mockRefreshToken
      };

      const invalidOptions = { timeout: 'not a number' };

      // Just to use the variables
      expect(validOptions).toBeDefined();
      expect(invalidOptions).toBeDefined();
    });
  });

  describe('Integration', () => {
    it('should create a functional AxiosClient instance', async () => {
      // Arrange
      const mockRequest = vi.fn().mockResolvedValue({ data: 'success' });
      (AxiosClient as unknown as Mock).mockImplementation((): any => ({
        request: mockRequest,
        interceptors: { request: { use: vi.fn() } }
      }));

      // Create the client and cast to any to access the mocked request method
      const client = createApiClient({}) as any;

      // Call the request method and await the result
      const response = await client.request({ url: '/test' });

      // Assert the request was made with the correct arguments
      expect(mockRequest).toHaveBeenCalledWith({ url: '/test' });
      expect(response.data).toBe('success');
    });
  });

  describe('Error Handling', () => {
    it('should handle TokenService instantiation failure', () => {
      // Arrange
      const originalConsoleError = console.error;
      console.error = vi.fn();

      vi.spyOn(
        TokenService.prototype as any,
        'constructor'
      ).mockImplementationOnce(() => {
        throw new Error('TokenService failed');
      });

      expect(() => createApiClient({})).toThrow('TokenService failed');

      // Cleanup
      console.error = originalConsoleError;
    });
  });
});
