import {
  describe,
  it,
  expect,
  beforeEach,
  afterEach,
  vi,
  beforeAll,
  afterAll
} from 'vitest';
import { clearMockStorage } from './__mocks__/storage';
import {
  createTestTokens,
  createExpiredTestTokens,
  createMalformedTokens,
  createLargeTokens
} from './__fixtures__/tokens';
import { TokenService } from '@/auth/tokens/token-service';
import { LocalKey } from '@/hooks/useLocalStorage';

describe('TokenService', () => {
  let tokenService: TokenService;
  let originalWindow: typeof globalThis.window;
  let originalLocalStorage: Storage | undefined;

  // Save original globals before tests
  beforeAll(() => {
    originalWindow = { ...globalThis.window };
    originalLocalStorage = globalThis.localStorage;
  });

  // Restore original globals after all tests
  afterAll(() => {
    Object.defineProperty(globalThis, 'window', {
      value: originalWindow,
      configurable: true,
      writable: true
    });
    Object.defineProperty(globalThis, 'localStorage', {
      value: originalLocalStorage,
      configurable: true,
      writable: true
    });
  });

  // Reset storage and create new instance before each test
  beforeEach(() => {
    clearMockStorage();
    tokenService = new TokenService();
  });

  // Clean up after each test
  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Initialization', () => {
    it('should initialize with default options', () => {
      expect(tokenService).toBeInstanceOf(TokenService);
      // @ts-expect-error - Testing private property
      expect(tokenService.storageKey).toBe(LocalKey.AUTH_TOKENS);
    });

    it('should initialize with custom storage key and expiry buffer', () => {
      const customService = new TokenService({
        storageKey: 'custom_key',
        expiryBufferMinutes: 10
      });
      // @ts-expect-error - Testing private property
      expect(customService.storageKey).toBe('custom_key');
      // @ts-expect-error - Testing private property
      expect(customService.expiryBufferMs).toBe(10 * 60 * 1000);
    });

    it('should handle server-side rendering (window is undefined)', () => {
      // Temporarily remove window
      // @ts-expect-error - Testing SSR scenario
      delete globalThis.window;

      expect(() => {
        const service = new TokenService();
        service.setTokens(createTestTokens());
        expect(service.getTokens()).toBeNull();
      }).not.toThrow();

      // Restore window
      globalThis.window = originalWindow as typeof globalThis.window;
    });
  });

  describe('Token Management', () => {
    it('should store and retrieve tokens correctly', () => {
      const tokens = createTestTokens();
      tokenService.setTokens(tokens);
      const stored = tokenService.getTokens();
      expect(stored).toBeTruthy();
      expect(stored?.AccessToken).toBe(tokens.AccessToken);
      expect(stored?.RefreshToken).toBe(tokens.RefreshToken);
      expect(stored?.IdToken).toBe(tokens.IdToken);
      expect(stored?.ExpiresIn).toBe(tokens.ExpiresIn);
      expect(typeof stored?.ObtainedAt).toBe('number');
    });

    it('should clear tokens', () => {
      tokenService.setTokens(createTestTokens());
      tokenService.clearTokens();
      expect(tokenService.getTokens()).toBeNull();
    });

    it('should handle malformed token data', () => {
      // Mock console.error to suppress expected error logs
      const originalError = console.error;
      console.error = vi.fn();

      localStorage.setItem(LocalKey.AUTH_TOKENS, createMalformedTokens());
      expect(tokenService.getTokens()).toBeNull();
      // Should not throw when clearing non-existent tokens
      expect(() => tokenService.clearTokens()).not.toThrow();

      // Verify console.error was called with our error
      expect(console.error).toHaveBeenCalledWith(
        'Failed to parse auth tokens',
        expect.any(Error)
      );

      // Restore console.error
      console.error = originalError;
    });

    it('should handle large token values', () => {
      const largeTokens = createLargeTokens();
      expect(() => {
        tokenService.setTokens(largeTokens);
      }).not.toThrow();
      const retrieved = tokenService.getTokens();
      expect(retrieved).toBeDefined();
      expect(retrieved?.AccessToken.length).toBe(5 * 1024 * 1024);
    });
  });

  describe('Token Validation', () => {
    it('should detect valid tokens', () => {
      const tokens = createTestTokens();
      tokenService.setTokens(tokens);
      expect(tokenService.isTokenExpired()).toBe(false);
    });

    it('should detect expired tokens', () => {
      const tokens = createExpiredTestTokens();
      tokenService.setTokens(tokens);
      expect(tokenService.isTokenExpired()).toBe(true);
    });

    it('should respect expiry buffer', () => {
      const customService = new TokenService({ expiryBufferMinutes: 10 });
      const tokens = createTestTokens({
        ExpiresIn: Date.now() + 5 * 60 * 1000 // 5 minutes from now
      });
      customService.setTokens(tokens);
      expect(customService.isTokenExpired()).toBe(true);
    });

    it('should handle missing tokens', () => {
      expect(tokenService.isTokenExpired()).toBe(true);
      expect(tokenService.getAccessToken()).toBeNull();
      expect(tokenService.getIdToken()).toBeNull();
      expect(tokenService.getRefreshToken()).toBeNull();
    });
  });

  describe('Token Getters', () => {
    it('should return access token when valid', () => {
      const tokens = createTestTokens();
      tokenService.setTokens(tokens);
      expect(tokenService.getAccessToken()).toBe(tokens.AccessToken);
    });

    it('should return null for access token when expired', () => {
      const tokens = createExpiredTestTokens();
      tokenService.setTokens(tokens);
      expect(tokenService.getAccessToken()).toBeNull();
    });

    it('should return ID token', () => {
      const tokens = createTestTokens();
      tokenService.setTokens(tokens);
      expect(tokenService.getIdToken()).toBe(tokens.IdToken);
    });

    it('should return refresh token', () => {
      const tokens = createTestTokens();
      tokenService.setTokens(tokens);
      expect(tokenService.getRefreshToken()).toBe(tokens.RefreshToken);
    });
  });

  describe('Multiple Instances', () => {
    it('should work with multiple instances with different storage keys', () => {
      const service1 = new TokenService({ storageKey: 'service1' });
      const service2 = new TokenService({ storageKey: 'service2' });

      const tokens1 = createTestTokens();
      const tokens2 = createTestTokens({ AccessToken: 'different-token' });

      service1.setTokens(tokens1);
      service2.setTokens(tokens2);

      expect(service1.getTokens()).toEqual(tokens1);
      expect(service2.getTokens()).toEqual(tokens2);

      service1.clearTokens();
      expect(service1.getTokens()).toBeNull();
      expect(service2.getTokens()).toEqual(tokens2);
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty string as storage key', () => {
      const service = new TokenService({ storageKey: '' });
      const tokens = createTestTokens();
      service.setTokens(tokens);
      expect(service.getTokens()).toEqual(tokens);
    });

    it('should handle special characters in storage key', () => {
      const specialKey = 'auth:tokens@v1';
      const service = new TokenService({ storageKey: specialKey });
      const tokens = createTestTokens();
      service.setTokens(tokens);
      expect(service.getTokens()).toEqual(tokens);
      expect(localStorage.getItem(specialKey)).toBeTruthy();
    });

    it('should handle concurrent access', async () => {
      const tokens1 = createTestTokens();
      const tokens2 = createTestTokens({ AccessToken: 'concurrent-token' });

      // Simulate concurrent access
      const promises = [
        Promise.resolve().then(() => tokenService.setTokens(tokens1)),
        Promise.resolve().then(() => tokenService.setTokens(tokens2))
      ];

      await Promise.all(promises);

      // The last write should win
      expect(tokenService.getTokens()?.AccessToken).toBe(tokens2.AccessToken);
    });
  });
});
