import { AuthTokens } from '@/auth/tokens/token-service';

export const createTestTokens = (
  overrides: Partial<AuthTokens> = {}
): AuthTokens => ({
  AccessToken: 'test-access-token',
  RefreshToken: 'test-refresh-token',
  IdToken: 'test-id-token',
  ExpiresIn: Date.now() + 3600 * 1000,
  ObtainedAt: Date.now(),
  ...overrides
});

export const createExpiredTestTokens = (): AuthTokens => ({
  AccessToken: 'expired-access-token',
  RefreshToken: 'expired-refresh-token',
  IdToken: 'expired-id-token',
  ExpiresIn: Date.now() - 1000,
  ObtainedAt: Date.now()
});

export const createMalformedTokens = (): string => 'invalid-json';

export const createLargeTokens = (): AuthTokens => {
  const largeString = 'x'.repeat(5 * 1024 * 1024); // 5MB
  return {
    AccessToken: largeString,
    RefreshToken: largeString,
    IdToken: largeString,
    ExpiresIn: Date.now() + 3600 * 1000,
    ObtainedAt: Date.now()
  };
};
