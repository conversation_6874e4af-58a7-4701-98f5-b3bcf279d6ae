import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { getClientConfig } from '@/client-config/client-config';
import type { ClientConfig } from '@/client-config/client-config';

const originalEnv = process.env;

const resetEnv = () => {
  process.env = { ...originalEnv };
  // Clear all NEXT_PUBLIC_* vars
  Object.keys(process.env).forEach((key) => {
    if (key.startsWith('NEXT_PUBLIC_')) {
      delete process.env[key];
    }
  });
};

describe('getClientConfig', () => {
  beforeEach(() => {
    resetEnv();
    vi.resetModules();
  });

  afterEach(() => {
    resetEnv();
  });

  describe('default configuration', () => {
    it('should return default values when no environment variables are set', () => {
      const config = getClientConfig();

      expect(config.clientId).toBe('test-client');
      expect(config.name).toBe('Test Client');
      expect(config.stripePublicKey).toBe(process.env.NEXT_PUBLIC_STRIPE_PK);
      expect(config.analytics.provider).toBe('none');
      expect(config.analytics.trackingId).toBeUndefined();
      expect(config.features).toEqual({});
      expect(config.theme.logoUrl).toBe('/logo.svg');
      expect(config.theme.faviconUrl).toBe('/favicon.ico');
    });
  });

  describe('environment variable overrides', () => {
    it('should override default values with environment variables', () => {
      process.env.NEXT_PUBLIC_CLIENT_ID = 'test-env-client';
      process.env.NEXT_PUBLIC_CLIENT_NAME = 'Test Env Client';
      process.env.NEXT_PUBLIC_STRIPE_PUBLIC_KEY = 'pk_test_123';
      process.env.NEXT_PUBLIC_ANALYTICS_PROVIDER = 'google';
      process.env.NEXT_PUBLIC_ANALYTICS_TRACKING_ID = 'UA-123456';
      process.env.NEXT_PUBLIC_FEATURES = JSON.stringify({ newFeature: true });
      process.env.NEXT_PUBLIC_THEME_LOGO_URL = '/custom-logo.png';
      process.env.NEXT_PUBLIC_THEME_FAVICON_URL = '/custom-favicon.ico';

      const config = getClientConfig();

      expect(config.clientId).toBe('test-env-client');
      expect(config.name).toBe('Test Env Client');
      expect(config.stripePublicKey).toBe('pk_test_123');
      expect(config.analytics.provider).toBe('google');
      expect(config.analytics.trackingId).toBe('UA-123456');
      expect(config.features).toEqual({ newFeature: true });
      expect(config.theme.logoUrl).toBe('/custom-logo.png');
      expect(config.theme.faviconUrl).toBe('/custom-favicon.ico');
    });

    it('should handle empty string environment variables', () => {
      process.env.NEXT_PUBLIC_CLIENT_ID = '';
      process.env.NEXT_PUBLIC_CLIENT_NAME = '';

      const config = getClientConfig();

      expect(config.clientId).toBe('test-client'); // Should fall back to default
      expect(config.name).toBe('Test Client'); // Should fall back to default
    });

    it('should handle whitespace in environment variables', () => {
      process.env.NEXT_PUBLIC_CLIENT_NAME = '  Test Client  ';

      const config = getClientConfig();

      expect(config.name).toBe('  Test Client  '); // Preserves whitespace
    });
  });

  describe('type safety', () => {
    it('should return a valid ClientConfig object', () => {
      const config = getClientConfig();

      const typedConfig: ClientConfig = config;

      expect(typedConfig).toBeDefined();
    });

    it('should handle non-string environment variables', () => {
      // @ts-expect-error test case
      process.env.NEXT_PUBLIC_CLIENT_ID = 12345;

      const config = getClientConfig();

      // Should transform to string
      expect(config.clientId).toBe('test-client');
    });
  });

  describe('edge cases', () => {
    it('should handle very long environment variable values', () => {
      const longString = 'a'.repeat(10000);
      process.env.NEXT_PUBLIC_CLIENT_NAME = longString;

      const config = getClientConfig();

      expect(config.name).toBe(longString);
    });

    it('should handle special characters in values', () => {
      const specialString = 'Test & Test <script>alert(1)</script>';
      process.env.NEXT_PUBLIC_CLIENT_NAME = specialString;

      const config = getClientConfig();

      expect(config.name).toBe(specialString);
    });

    it('should handle undefined environment variables', () => {
      process.env.NEXT_PUBLIC_CLIENT_NAME = undefined;

      const config = getClientConfig();

      // Should fall back to default
      expect(config.name).toBe('Test Client');
    });
  });
});
