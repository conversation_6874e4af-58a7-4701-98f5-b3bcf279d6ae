'use client';

import React, {
  createContext,
  useEffect,
  useRef,
  useId,
  useState,
  ReactNode,
  KeyboardEvent as ReactKeyboardEvent,
  use
} from 'react';
import ReactDOM from 'react-dom';

interface ModalContextValue {
  open: boolean;
  setModalOpen: (open: boolean) => void;
  modalTitleId?: string;
  modalDescriptionId?: string;
  setModalTitleId: (id: string) => void;
  setModalDescriptionId: (id: string) => void;
  blockCloseModal: boolean | undefined;
}

const focusableSelectors = [
  'a[href]',
  'button:not([disabled])',
  'textarea:not([disabled])',
  'input:not([type="hidden"]):not([disabled])',
  'select:not([disabled])',
  '[tabindex]:not([tabindex="-1"])'
];

const ModalContext = createContext<ModalContextValue | undefined>(undefined);
export function useModalContext() {
  const modalContext = use(ModalContext);
  if (!modalContext)
    throw new Error('Modal compound components must be used within <Modal>');
  return modalContext;
}

interface ModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  blockCloseModal?: boolean;
  children: ReactNode;
}

export function Modal({
  open,
  onOpenChange,
  blockCloseModal,
  children
}: ModalProps) {
  const [modalTitleId, setModalTitleId] = useState<string>();
  const [modalDescriptionId, setModalDescriptionId] = useState<string>();

  useEffect(() => {
    if (!open) return;
    const originalOverflow = document.body.style.overflow;
    // document.body.style.overflow = 'hidden'; // prevent bg scroll if needed
    return () => {
      document.body.style.overflow = originalOverflow;
    };
  }, [open]);

  return (
    <ModalContext.Provider
      value={{
        open,
        setModalOpen: onOpenChange,
        modalTitleId,
        modalDescriptionId,
        setModalTitleId,
        setModalDescriptionId,
        blockCloseModal
      }}
    >
      {open ? <ModalPortal>{children}</ModalPortal> : null}
    </ModalContext.Provider>
  );
}

function ModalPortal({ children }: { children: ReactNode }) {
  const [mounted, setMounted] = useState(false);
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return ReactDOM.createPortal(children, document.body);
}

interface ModalOverlayProps {
  color?: string;
  opacity?: number;
}

Modal.Overlay = function ModalOverlay({
  color = 'black',
  opacity = 0.5
}: ModalOverlayProps) {
  return (
    <div
      aria-hidden="true"
      style={{
        position: 'fixed',
        inset: 0,
        background: color,
        opacity,
        zIndex: 1000
      }}
    />
  );
};

interface ModalContentProps {
  className?: string;
  children: ReactNode;
  onKeyDown?: (e: ReactKeyboardEvent<HTMLDivElement>) => void;
  overflow?: 'auto' | 'visible' | 'hidden';
  maxHeight?: string;
}

Modal.Content = function ModalContent({
  className,
  children,
  onKeyDown,
  overflow = 'auto',
  maxHeight = '90vh'
}: ModalContentProps) {
  const {
    open,
    setModalOpen,
    modalTitleId,
    modalDescriptionId,
    blockCloseModal
  } = useModalContext();

  const contentRef = useRef<HTMLDivElement>(null);
  const firstFocusableRef = useRef<HTMLElement | null>(null);
  const lastFocusableRef = useRef<HTMLElement | null>(null);

  useEffect(() => {
    if (!open || !contentRef.current) return;

    const nodes = contentRef.current.querySelectorAll<HTMLElement>(
      focusableSelectors.join(',')
    );

    if (nodes.length) {
      firstFocusableRef.current = nodes[0];
      lastFocusableRef.current = nodes[nodes.length - 1];
      nodes[0].focus();
    } else {
      (contentRef.current as HTMLElement).focus();
    }

    function handleFocus(e: FocusEvent) {
      if (!contentRef.current) return;
      if (!contentRef.current.contains(e.target as Node)) {
        e.stopPropagation();
        if (firstFocusableRef.current) firstFocusableRef.current.focus();
      }
    }

    document.addEventListener('focus', handleFocus, true);

    return () => document.removeEventListener('focus', handleFocus, true);
  }, [open]);

  useEffect(() => {
    if (!open) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        if (!blockCloseModal) {
          setModalOpen(false);
        } else {
          e.preventDefault();
        }
      }

      if (e.key === 'Tab' && contentRef.current) {
        const nodes = contentRef.current.querySelectorAll<HTMLElement>(
          focusableSelectors.join(',')
        );

        if (!nodes.length) {
          e.preventDefault();
          return;
        }

        const firstFocusableNode = nodes[0];
        const lastFocusableNode = nodes[nodes.length - 1];

        if (e.shiftKey && document.activeElement === firstFocusableNode) {
          e.preventDefault();
          lastFocusableNode.focus();
        } else if (
          !e.shiftKey &&
          document.activeElement === lastFocusableNode
        ) {
          e.preventDefault();
          firstFocusableNode.focus();
        }
      }
    };
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [open, blockCloseModal, setModalOpen]);

  // Prevent scroll on modal content
  useEffect(() => {
    if (!open || !contentRef.current) return;
    contentRef.current.scrollTop = 0;
  }, [open]);

  return (
    <div
      ref={contentRef}
      role="dialog"
      aria-modal="true"
      aria-labelledby={modalTitleId}
      aria-describedby={modalDescriptionId}
      tabIndex={-1}
      className={`fixed top-1/2 left-1/2 z-[1001] m-0 -translate-x-1/2 -translate-y-1/2 rounded bg-white shadow-lg outline-none ${className || ''}`}
      style={{
        minWidth: 320,
        minHeight: 100,
        maxHeight: maxHeight,
        overflowY: overflow
      }}
      onClick={(e) => e.stopPropagation()}
      onKeyDown={onKeyDown}
    >
      {children}
    </div>
  );
};

Modal.Backdrop = function ModalBackdrop({ onClick }: { onClick?: () => void }) {
  const { blockCloseModal } = useModalContext();
  return (
    <div
      aria-hidden="true"
      className="fixed inset-0 z-[1000] bg-black opacity-50"
      onClick={() => {
        if (!blockCloseModal && onClick) onClick();
      }}
    />
  );
};

interface ModalTitleProps {
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
  children: ReactNode;
  className?: string;
}

Modal.Title = function ModalTitle({
  children,
  as = 'h2',
  className,
  ...rest
}: ModalTitleProps) {
  const { setModalTitleId } = useModalContext();
  const id = useId();

  useEffect(() => {
    setModalTitleId(id);
  }, [id, setModalTitleId]);

  const Tag = as;

  return (
    <Tag id={id} className={className} {...rest}>
      {children}
    </Tag>
  );
};

interface ModalDescriptionProps {
  as?: 'p' | 'span' | 'div';
  className?: string;
  children: ReactNode;
}

Modal.Description = function ModalDescription({
  children,
  as = 'div',
  className,
  ...rest
}: ModalDescriptionProps) {
  const { setModalDescriptionId } = useModalContext();

  const id = useId();

  useEffect(() => {
    setModalDescriptionId(id);
  }, [id, setModalDescriptionId]);

  const Tag = as;

  return (
    <Tag id={id} className={className} {...rest}>
      {children}
    </Tag>
  );
};

interface ModalCloseProps {
  className?: string;
}

Modal.Close = function ModalClose({
  children,
  className,
  ...rest
}: ModalCloseProps & React.ButtonHTMLAttributes<HTMLButtonElement>) {
  const { setModalOpen, blockCloseModal } = useModalContext();
  return (
    <button
      type="button"
      aria-label="Close"
      className={`cursor-pointer ${className}`}
      onClick={() => {
        if (!blockCloseModal) setModalOpen(false);
      }}
      {...rest}
    >
      {children || '×'}
    </button>
  );
};

export default Modal;
