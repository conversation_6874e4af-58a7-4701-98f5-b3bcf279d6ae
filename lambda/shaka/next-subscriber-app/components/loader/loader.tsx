import { LoadingSpinner } from '@/icons/icons';
import React from 'react';

interface LoaderProps {
  text?: string;
  textStyles?: string;
  className?: string;
}

export function Loader({ text, className, textStyles }: LoaderProps) {
  return (
    <div
      className={`flex h-screen w-full items-center justify-center ${className || ''} ${text ? 'flex-col gap-y-4' : 'flex-row'}`}
    >
      {text && (
        <p className={`text-xxs text-center ${textStyles || ''}`}>{text}</p>
      )}
      <LoadingSpinner size={75} />
    </div>
  );
}
