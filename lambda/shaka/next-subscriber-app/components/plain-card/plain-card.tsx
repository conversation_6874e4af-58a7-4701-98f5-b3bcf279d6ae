import React, { ReactNode } from 'react';

export type PlainCardProps = {
  children: ReactNode;
  className?: string;
  as?: React.ElementType;
  ariaLabel?: string;
};

export function PlainCard({
  children,
  className,
  ariaLabel,
  as = 'section'
}: PlainCardProps) {
  const Component = as;
  return (
    <Component
      aria-label={ariaLabel}
      className={`plain-card ${className || ''}`}
    >
      {children}
    </Component>
  );
}
