import React from 'react';
import { PlainCard } from '@/components/plain-card/plain-card';
import { Alert } from '@/components/alert/alert';

interface CannotFetchAlertProps extends React.PropsWithChildren {
  className?: string;
  message: string;
}

export function CannotFetchAlert({
  className,
  message,
  children
}: CannotFetchAlertProps) {
  return (
    <PlainCard className={`mx-auto max-w-4xl ${className}`}>
      {children}
      <Alert message={message} />
    </PlainCard>
  );
}
