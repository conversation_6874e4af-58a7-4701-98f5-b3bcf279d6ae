'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Loader } from '@/components/loader/loader';
import { useDeliveryRiderAuth } from '@/src/deliveroo/app/signup/context/DeliveryRiderAuth';

type RouteConfig = Record<string, { path: string; name: string }>;

type ExtractRoutePaths<T extends RouteConfig> = T[keyof T]['path'];

interface RequireDeliverooRiderAuthProps<T extends RouteConfig>
  extends React.PropsWithChildren {
  redirectTo?: ExtractRoutePaths<T>;
  loadingComponent?: React.ReactNode;
}

export function RequireDeliverooRiderAuth<T extends RouteConfig>({
  children,
  redirectTo = '/login',
  loadingComponent = <Loader />
}: RequireDeliverooRiderAuthProps<T>) {
  const { isAuthenticated, isInitialized } = useDeliveryRiderAuth();
  const router = useRouter();

  useEffect(() => {
    if (isInitialized && !isAuthenticated) {
      router.replace(redirectTo);
    }
  }, [isInitialized, isAuthenticated, router, redirectTo]);

  if (!isInitialized) return loadingComponent;

  if (!isAuthenticated) return null;

  return <>{children}</>;
}
