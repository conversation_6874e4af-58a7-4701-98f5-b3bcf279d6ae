'use client';
import { useAuth } from '@/auth/hooks/use-auth';
import { useRouter } from 'next/navigation';
import React, { useEffect } from 'react';
import { Loader } from '@/components/loader/loader';

type RouteConfig = Record<string, { path: string; name: string }>;

type ExtractRoutePaths<T extends RouteConfig> = T[keyof T]['path'];

interface RequireAuthProps<T extends RouteConfig>
  extends React.PropsWithChildren {
  redirectTo?: ExtractRoutePaths<T>;
  loadingComponent?: React.ReactNode;
}

export function RequireAuth<T extends RouteConfig>({
  children,
  redirectTo = '/signup/plan-selection',
  loadingComponent = <Loader />
}: RequireAuthProps<T>) {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.replace(redirectTo);
    }
  }, [isAuthenticated, isLoading, router, redirectTo]);

  if (isLoading) {
    return loadingComponent;
  }

  if (!isAuthenticated) return null;

  return <>{children}</>;
}

// export function RequireDeliverooRiderAuth(){
//
// }

// export function useDeliverooRiderAuth() { }
// grab emailed entered in signup and prepopulate register form. input should not be editable ( disable === email provided)
// remove DOB field + logic only from deliveroo
