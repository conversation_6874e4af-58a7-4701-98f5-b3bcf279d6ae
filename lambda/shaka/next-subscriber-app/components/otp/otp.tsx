'use client';

import React, { useRef, useEffect } from 'react';

interface OTPInputProps {
  length?: number;
  onChange: (otp: string) => void;
  className?: string;
  setInputError: (error: string[]) => void;
}

export function OTPInput({
  length = 4,
  onChange,
  className = '',
  setInputError
}: OTPInputProps) {
  const inputElementsRef = useRef<HTMLInputElement[]>([]);

  useEffect(() => {
    const [firstInput] = inputElementsRef.current;
    if (firstInput) firstInput.focus();
  }, []);

  const handleInputChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    currentIndex: number
  ) => {
    const typedValue = event.target.value;
    const isNumericInput = /^\d*$/.test(typedValue);

    if (!isNumericInput) {
      setInputError(['Please enter numbers only.']);
      return;
    }

    setInputError([]);
    const numericValue = typedValue.replace(/\D/g, '');

    if (numericValue.length === 1) {
      const currentInput = inputElementsRef.current[currentIndex];
      if (currentInput) {
        currentInput.value = numericValue;

        const nextInput = inputElementsRef.current[currentIndex + 1];
        if (nextInput) nextInput.focus();
      }
    }

    updateCombinedOTP();
  };

  const handleKeyDown = (event: React.KeyboardEvent, currentIndex: number) => {
    const currentInput = inputElementsRef.current[currentIndex];
    if (event.key === 'Backspace' && currentInput && !currentInput.value) {
      const previousInput = inputElementsRef.current[currentIndex - 1];
      if (previousInput) previousInput.focus();
    }
  };

  const handlePaste = (event: React.ClipboardEvent<HTMLDivElement>) => {
    event.preventDefault();

    const pastedData = event.clipboardData?.getData('text') || '';
    const hasNonDigit = /\D/.test(pastedData);

    if (hasNonDigit) {
      setInputError([
        'Oops! Looks like your pasted content has non-numeric characters.'
      ]);

      return;
    }

    setInputError([]);
    const digitsArray = pastedData.replace(/\D/g, '').split('');

    digitsArray.forEach((digit, index) => {
      const inputBox = inputElementsRef.current[index];
      if (inputBox) {
        inputBox.value = digit;
      }
    });

    const lastFilledInput =
      inputElementsRef.current[Math.min(digitsArray.length, length - 1)];
    if (lastFilledInput) lastFilledInput.focus();

    updateCombinedOTP();
  };

  const updateCombinedOTP = () => {
    const otp = inputElementsRef.current
      .map((input) => input?.value || '')
      .join('');

    if (onChange) onChange(otp);
  };

  return (
    <div className="grid gap-2" onPaste={handlePaste}>
      <div className="flex justify-center gap-2.5">
        {Array.from({ length }).map((_, index) => (
          <input
            name="otp"
            key={index}
            ref={(input) => {
              if (input) inputElementsRef.current[index] = input;
            }}
            type="text"
            inputMode="numeric"
            maxLength={1}
            autoComplete="one-time-code"
            className={`text-center font-bold transition-all ${className}`}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              handleInputChange(e, index)
            }
            onKeyDown={(e: React.KeyboardEvent) => handleKeyDown(e, index)}
          />
        ))}
      </div>
    </div>
  );
}

export default OTPInput;
