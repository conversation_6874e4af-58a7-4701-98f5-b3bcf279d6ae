import React from 'react';
import Link from 'next/link';
import Button from '@/components/button/button';
import { createTestId } from '@/utils/helpers';
import { sendGTMEvent } from '@next/third-parties/google';

interface CtaButtonProps {
  text: string;
  href?: string | Record<string, string>;
  className?: string;
  variant: 'primary' | 'secondary';
  disabled?: boolean;
  isLoading?: boolean;
  openInNewWindow?: boolean;
  onClick?: () => void;
}

const variants = {
  secondary:
    'bg-secondary text-primary border-primary hover:bg-secondary-hover hover:text-primary border',
  primary:
    'bg-primary text-secondary border-primary hover:bg-primary-hover hover:text-secondary border'
};

export function NextStepLink({
  text,
  href,
  className,
  disabled,
  isLoading,
  onClick,
  variant,
  openInNewWindow = false
}: CtaButtonProps) {
  const isDisabled = disabled || isLoading;

  const handleClick = () => {
    sendGTMEvent({
      event: 'buttonClickedTest',
      label: 'Next Step',
      category: 'CTA Button',
      action: 'Click',
      value: 'Pawel'
    });
    if (onClick) onClick();
  };

  if (isDisabled) {
    return (
      <Button
        data-test-id={`next-step-link-${createTestId(text)}`}
        className={className}
        variant={variant}
        disabled={true}
      >
        {text}
      </Button>
    );
  }

  return (
    <Link
      {...(openInNewWindow && {
        target: '_blank',
        rel: 'noopener noreferrer'
      })}
      onClick={handleClick}
      data-test-id={`next-step-link-${createTestId(text)}`}
      prefetch
      className={`text-bold inline-block w-full cursor-pointer rounded-[2px] p-3 text-center font-semibold ${variants[variant] || ''} ${className || ''}`}
      href={href || '#'}
    >
      {text}
    </Link>
  );
}
