import React from 'react';
import { LoadingSpinner } from '@/icons/icons';

interface ButtonProps extends React.PropsWithChildren {
  className?: string;
  form?: string;
  isLoading?: boolean;
  disabled?: boolean;
  onClick?: React.MouseEventHandler<HTMLButtonElement>;
  variant?: 'primary' | 'secondary' | 'link';
  type?: 'submit' | 'reset' | 'button';
  ref?: React.RefObject<HTMLButtonElement | null>;
}

const variants = {
  base: 'cursor-pointer rounded-[2px] p-3',
  primary:
    'primary: bg-primary hover:bg-primary-hover text-bold text-center text-secondary font-bold disabled:opacity-50',
  secondary:
    'hover:bg-secondary-hover hover:text-primary border-2 bg-white font-bold',
  link: 'uswitchLink leading-0'
};

function Button({
  children,
  className = '',
  isLoading,
  disabled,
  onClick,
  variant = 'primary',
  form,
  type = 'submit',
  ref,
  ...props
}: ButtonProps) {
  return (
    <button
      ref={ref}
      type={type}
      onClick={onClick}
      form={form}
      className={`${variants.base} ${variants[variant]} ${className}`}
      disabled={disabled}
      {...props}
    >
      <div className="flex items-center justify-center gap-2">
        {isLoading && <LoadingSpinner />}
        {children}
      </div>
    </button>
  );
}

export default Button;
