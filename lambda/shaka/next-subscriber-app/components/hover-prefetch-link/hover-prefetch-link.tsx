'use client';

import React, { useState } from 'react';
import Link from 'next/link';

interface HoverPrefetchLinkProps extends React.PropsWithChildren {
  href: string;
  onClick?: () => void;
  className?: string;
}

export function HoverPrefetchLink(props: HoverPrefetchLinkProps) {
  const [active, setActive] = useState(false);
  return (
    <Link
      prefetch={active ? null : false}
      onMouseEnter={() => setActive(true)}
      {...props}
    >
      {props.children}
    </Link>
  );
}
