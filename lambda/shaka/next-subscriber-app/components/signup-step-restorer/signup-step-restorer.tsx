import { usePathname, useRouter } from 'next/navigation';
import { useEffect } from 'react';
import {
  getCurrentSignupStep,
  hasAlreadyRestoredSignupStep,
  isValidSignupPath,
  markSignupStepAsRestored,
  shouldRestoreToSavedPath
} from '@/utils/helpers';
import { ROUTES_CONFIG } from '@/src/deliveroo/routes/route-config';

export function SignupStepRestorer() {
  const router = useRouter();
  const currentPathname = usePathname();

  // big question. what happens when user get to the dashboard and coses a page. on return should be redirected to the dash but how ?
  // add hook call on view my account click as it was before
  useEffect(() => {
    try {
      if (typeof window === 'undefined') return;

      if (hasAlreadyRestoredSignupStep()) return;

      const savedSignupStep = getCurrentSignupStep<
        typeof ROUTES_CONFIG,
        'deliveroo'
      >();

      if (!savedSignupStep) return;

      if (!isValidSignupPath(savedSignupStep.stepId)) return;

      if (shouldRestoreToSavedPath(savedSignupStep.stepId, currentPathname)) {
        markSignupStepAsRestored();
        router.replace(savedSignupStep.stepId);
      }
    } catch {
      console.error('Failed to restore signup step');
    }
  }, [router, currentPathname]);

  return null;
}
