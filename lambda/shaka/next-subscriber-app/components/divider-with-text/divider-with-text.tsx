import React from 'react';

interface DeviderWithTextProps {
  text: string;
  className?: string;
}

export function DeviderWithText({
  text,
  className = ''
}: DeviderWithTextProps) {
  return (
    <div className={`relative flex items-center justify-center ${className}`}>
      <div className="absolute inset-0 flex items-center">
        <div className="w-full border-t border-gray-300"></div>
      </div>
      <div className="relative bg-white px-4">
        <span className="text-[16px]">{text}</span>
      </div>
    </div>
  );
}
