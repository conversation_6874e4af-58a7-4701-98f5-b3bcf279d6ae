import { useFocusError } from '@/hooks/useFocusError';
import React, { useState } from 'react';
import { Alert } from '@/components/alert/alert';
import { ChevronDown } from '@/icons/icons';
import { days, months, years } from '@/utils/constants';
import { Divider } from '@/components/divider/divider';
import { getDayMonthYearFromDate, trimWhiteSpace } from '@/utils/formatters';

export interface SignupFormBase {
  name: string;
  email: string;
  password: string;
}

export interface SignupFormData extends SignupFormBase {
  day: string;
  month: string;
  year: string;
}

export interface SignupPayload extends SignupFormBase {
  date_of_birth: string;
}

interface SignupFormContainerProps extends React.PropsWithChildren {
  formData: SignupFormData;
  clearErrors: () => void;
  errorMap: Record<string, string | null>;
  handleSubmit: (data: SignupFormData) => void;
}

interface FormFieldProps {
  id: string;
  label: string;
  type: string;
  placeholder: string;
  defaultValue?: string;
  error?: string | null;
  onChange: () => void;
  inputMode?: 'text' | 'email' | 'tel' | 'numeric';
  autoComplete?: string;
  pattern?: string;
  inputClassName?: string;
  maxLength?: number;
  disabled?: boolean;
}

function FormField({
  id,
  label,
  type,
  placeholder,
  defaultValue,
  error,
  onChange,
  inputMode = 'text',
  autoComplete,
  pattern,
  maxLength,
  inputClassName = '',
  disabled
}: FormFieldProps) {
  return (
    <div className="w-full">
      <label htmlFor={id} className="font-semibold">
        {label}
      </label>
      <input
        id={id}
        name={id}
        type={type}
        inputMode={inputMode}
        placeholder={placeholder}
        className={`bg-secondary border-border text-placeholder placeholder:text-default placeholder-placeholder my-2 w-full rounded-[2px] border p-3 ${error ? 'border-2 border-red-500' : ''} ${inputClassName}`}
        defaultValue={defaultValue}
        onChange={onChange}
        autoComplete={autoComplete}
        pattern={pattern}
        maxLength={maxLength}
        disabled={disabled}
      />
      {error && (
        <Alert variant="error" message={error} align="left" className="mb-4" />
      )}
    </div>
  );
}

const iconClasses = `
    pointer-events-none absolute top-1/2 right-3 h-5 w-5 -translate-y-1/2 transform
    transition-colors duration-200
  `;

const baseSelectClasses = `
    w-full cursor-pointer appearance-none rounded-[2px] border p-3 xl:p-3
    bg-white text-placeholder rounded-[2px] border border-border
  `;

interface DateOfBirthSelectProps {
  formData: SignupFormData;
  error?: boolean;
  className?: string;
}

// todo : refactor
// todo: mock api call
// todo: check what api expects ( dob )
function DateOfBirthSelect({
  formData,
  error = false,
  className = ''
}: DateOfBirthSelectProps) {
  const dateOfBirth = `${formData.year}-${formData.month}-${formData.day}`;
  const { year, month, day } = getDayMonthYearFromDate(dateOfBirth);

  const initialState = {
    day: day || '',
    month: month || '',
    year: year || ''
  };

  const [selectedDate, setSelectedDate] = useState(initialState);

  return (
    <div
      className={`${className}`}
      role="group"
      aria-labelledby="switchingDate"
    >
      <span className="font-semibold">Date of birth</span>
      <div className="mt-2 grid grid-cols-3 gap-3">
        {/* Day */}
        <div className="relative">
          <label htmlFor="day-select" className="sr-only">
            Day
          </label>
          <select
            className={`${baseSelectClasses} ${error ? 'border-2 border-red-500' : ''}`}
            id="day-select"
            name="day"
            aria-label="Day"
            value={selectedDate.day}
            onChange={(e) =>
              setSelectedDate({ ...selectedDate, day: e.target.value })
            }
            aria-describedby={error ? 'date-error' : undefined}
          >
            <option value="" disabled>
              Day
            </option>
            {days.map((day) => (
              <option key={day} value={day}>
                {parseInt(day, 10)}
              </option>
            ))}
          </select>
          <span aria-hidden="true">
            <ChevronDown className={iconClasses} />
          </span>
        </div>

        {/* Month */}
        <div className="relative">
          <label htmlFor="month-select" className="sr-only">
            Month
          </label>
          <select
            className={`${baseSelectClasses} ${error ? 'border-2 border-red-500' : ''}`}
            id="month-select"
            name="month"
            aria-label="Month"
            value={selectedDate.month}
            onChange={(e) =>
              setSelectedDate({
                ...selectedDate,
                month: e.target.value
              })
            }
            aria-describedby={error ? 'date-error' : undefined}
          >
            <option value="" disabled>
              Month
            </option>
            {months.map((month) => (
              <option key={month.value} value={month.value}>
                {month.name}
              </option>
            ))}
          </select>
          <span aria-hidden="true">
            <ChevronDown className={iconClasses} />
          </span>
        </div>

        {/* Year */}
        <div className="relative">
          <label htmlFor="year-select" className="sr-only">
            Year
          </label>
          <select
            className={`${baseSelectClasses} ${error ? 'border-2 border-red-500' : ''}`}
            id="year-select"
            name="year"
            aria-label="Year"
            value={selectedDate.year}
            onChange={(e) =>
              setSelectedDate({
                ...selectedDate,
                year: e.target.value
              })
            }
          >
            <option value="" disabled>
              Year
            </option>
            {years.map((year) => (
              <option key={year} value={year}>
                {year}
              </option>
            ))}
          </select>
          <span aria-hidden="true">
            <ChevronDown className={iconClasses} />
          </span>
        </div>
      </div>

      {error && (
        <Alert
          message="Please select a date"
          variant="error"
          className="mt-3"
          align="left"
        />
      )}
    </div>
  );
}

interface SignupFormProps {
  errors: Record<string, string | null>;
  onSubmit: (data: SignupFormData) => void;
  className?: string;
  children?: React.ReactNode;
  formId: string;
}

export function SignupForm({
  errors = {},
  onSubmit,
  className = '',
  formId,
  children
}: SignupFormProps) {
  useFocusError(errors);

  const handleFormSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const form = e.currentTarget;
    const formValues = new FormData(form);
    const trimmedData: Record<string, string> = {};

    formValues.forEach((value, key) => {
      trimmedData[key] =
        typeof value === 'string' ? trimWhiteSpace(value) : String(value);
    });

    onSubmit({
      month: trimmedData.month,
      year: trimmedData.year,
      day: trimmedData.day,
      name: trimmedData.name,
      email: trimmedData.email,
      password: trimmedData.password
    });
  };

  return (
    <form
      onSubmit={handleFormSubmit}
      className={`mt-6 grid gap-4 ${className || ''}`}
      id={formId}
    >
      {children}
    </form>
  );
}

export function SignupFormContainer({
  formData,
  errorMap,
  clearErrors,
  handleSubmit,
  children
}: SignupFormContainerProps) {
  const hasDOBErrors = Boolean(errorMap.day || errorMap.month || errorMap.year);
  return (
    <>
      <SignupForm
        errors={errorMap}
        onSubmit={handleSubmit}
        formId="signupForm"
        className="grid grid-cols-1 lg:grid-cols-2"
      >
        <FormField
          id="name"
          label="Name"
          type="text"
          placeholder="Enter your full name"
          defaultValue={formData.name}
          error={errorMap.name}
          onChange={clearErrors}
          autoComplete="given-name"
          maxLength={50}
        />
        <FormField
          id="email"
          label="Email"
          type="text"
          inputMode="email"
          placeholder="Enter your email address "
          defaultValue={formData.email}
          error={errorMap.email}
          onChange={clearErrors}
          autoComplete="email"
        />
        <FormField
          id="password"
          label="Password"
          type="password"
          placeholder="At least 8 characters"
          defaultValue={formData.password}
          error={errorMap.password}
          onChange={clearErrors}
          autoComplete="new-password"
        />
        {/*TO BE DELETED*/}
        <DateOfBirthSelect formData={formData} error={hasDOBErrors} />
      </SignupForm>
      <Divider className="my-2 mb-1" />
      {children}
    </>
  );
}
