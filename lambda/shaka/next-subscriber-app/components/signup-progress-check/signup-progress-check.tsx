'use client';
import { useAuth } from '@/auth/hooks/use-auth';
import { useRouter, usePathname } from 'next/navigation';
import React, { useEffect } from 'react';
import { ROUTES_CONFIG } from '@/src/deliveroo/routes/route-config';
import { useSignupProgress } from '@/context/signup-progress-context/signup-progress-tracked-routes';
import { Loader } from '@/components/loader/loader';

interface StepConfig {
  path: string;
  requiresAuth: boolean;
  requiresOtp: boolean;
  requiresPayment: boolean;
}

const STEP_CONFIGS: Record<string, StepConfig> = {
  [ROUTES_CONFIG.register.path]: {
    path: ROUTES_CONFIG.register.path,
    requiresAuth: false,
    requiresOtp: false,
    requiresPayment: false
  },
  [ROUTES_CONFIG.otp.path]: {
    path: ROUTES_CONFIG.otp.path,
    requiresAuth: true,
    requiresOtp: false,
    requiresPayment: false
  },
  [ROUTES_CONFIG.payment.path]: {
    path: ROUTES_CONFIG.payment.path,
    requiresAuth: true,
    requiresOtp: true,
    requiresPayment: false
  },
  [ROUTES_CONFIG['order-confirmation'].path]: {
    path: ROUTES_CONFIG['order-confirmation'].path,
    requiresAuth: true,
    requiresOtp: true,
    requiresPayment: true
  }
};

interface RequireStepAuthProps {
  children: React.ReactNode;
  loadingComponent?: React.ReactNode;
}

export function RequireStepAuth({
  children,
  loadingComponent = <Loader />
}: RequireStepAuthProps) {
  const { isAuthenticated, isLoading } = useAuth();
  const { progress } = useSignupProgress();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    if (isLoading) return;

    const currentStepConfig = STEP_CONFIGS[pathname];
    if (!currentStepConfig) return;

    if (currentStepConfig.requiresAuth && !isAuthenticated) {
      router.replace(ROUTES_CONFIG.register.path);
      return;
    }

    if (currentStepConfig.requiresOtp && !progress.isOtpVerified) {
      router.replace(STEP_CONFIGS[ROUTES_CONFIG.otp.path].path);
      return;
    }

    if (currentStepConfig.requiresPayment && !progress.isPaymentCompleted) {
      router.replace(ROUTES_CONFIG.payment.path);
      return;
    }
  }, [isLoading, isAuthenticated, progress, pathname, router]);

  if (isLoading) {
    return loadingComponent;
  }

  return <>{children}</>;
}
