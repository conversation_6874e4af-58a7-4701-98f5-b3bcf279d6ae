import React from 'react';
import { PlainCard } from '@/components/plain-card/plain-card';
import { useMediaQuery } from '@/hooks/useMediaQuery';

interface ResponsiveCardProps extends React.PropsWithChildren {
  className?: string;
}

export function ConditionalWrapper({
  children,
  className
}: ResponsiveCardProps) {
  const isMobileDevice = useMediaQuery(1024);

  if (isMobileDevice) {
    return <section className={className}>{children}</section>;
  }
  return <PlainCard className={className}>{children}</PlainCard>;
}

export function DesktopOnly({ children }: ResponsiveCardProps) {
  return <div className="hidden lg:block">{children}</div>;
}
