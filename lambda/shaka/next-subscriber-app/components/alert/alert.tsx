import React from 'react';
import {
  IconProps,
  InfoIcon,
  WarningIcon,
  ErrorIconFilled
} from '@/icons/icons';

interface AlertProps {
  message: string;
  variant?: 'error' | 'warning' | 'info' | 'success';
  icon?: React.ComponentType<IconProps> | null;
  className?: string;
  iconClassName?: string;
  textClassName?: string;
  showIcon?: boolean;
  align?: 'left' | 'center' | 'right';
  spacing?: 'tight' | 'normal' | 'loose';
}

const DefaultIcons = {
  error: ({ className, size = 22 }: IconProps) => (
    <ErrorIconFilled className={className} size={size} />
  ),
  warning: ({ className, size = 20 }: IconProps) => (
    <WarningIcon className={className} size={size} />
  ),
  info: ({ className, size = 24 }: IconProps) => (
    <InfoIcon className={className} size={size} />
  ),
  success: ({ className, size = 16 }: IconProps) => (
    <svg
      className={className}
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="currentColor"
    >
      <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" />
    </svg>
  )
};

const variantStyles = {
  error: {
    container: 'text-error',
    text: 'text-error',
    icon: 'text-error'
  },
  warning: {
    container: 'text-lemon',
    text: 'text-lemon',
    icon: 'text-lemon'
  },
  info: {
    container: 'text-blue-600',
    text: 'text-blue-600',
    icon: 'text-blue-600'
  },
  success: {
    container: 'text-success-border',
    text: 'text-success-border',
    icon: 'text-success-border'
  }
};

const alignmentStyles = {
  left: 'justify-start',
  center: 'justify-center',
  right: 'justify-end'
};

export function Alert({
  message,
  variant = 'error',
  icon,
  className = 'w-fit',
  iconClassName = '',
  textClassName = '',
  showIcon = true,
  align = 'center'
}: AlertProps) {
  const variantStyle = variantStyles[variant];
  const alignmentStyle = alignmentStyles[align];

  const IconComponent = icon !== null ? icon || DefaultIcons[variant] : null;

  const containerClasses = [
    'flex items-start gap-2',
    alignmentStyle,
    variantStyle.container,
    className
  ]
    .filter(Boolean)
    .join(' ');

  const textClasses = [
    'font-normal',
    'flex-1',
    align === 'center' ? 'text-center' : '',
    variantStyle.text,
    textClassName
  ]
    .filter(Boolean)
    .join(' ');

  const iconClasses = [variantStyle.icon, iconClassName]
    .filter(Boolean)
    .join(' ');

  return (
    <div className={containerClasses}>
      {showIcon && IconComponent && <IconComponent className={iconClasses} />}
      <strong role="alert" className={textClasses}>
        {message}
      </strong>
    </div>
  );
}
