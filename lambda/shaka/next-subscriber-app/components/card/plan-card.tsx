import React from 'react';
import { PlainCard } from '@/components/plain-card/plain-card';
import { BellIcon } from '@/icons/icons';
import Button from '@/components/button/button';
import { capitalize } from '@/utils/formatters';
import { SubscriptionStatusInfo } from '@/src/uswitch/utils/constants';

interface PlanSummary {
  children: React.ReactNode;
  className?: string;
}

export type PlanSummaryClasses = {
  card?: string;
  header?: string;
  mainLabel?: string;
  planType?: string;
  phoneNumber?: string;
  action?: string;
  body?: string;
  dataUsage?: string;
  dataRemainingTime?: string;
  lowDataWarning?: string;
  texts?: string;
  minutes?: string;
  status?: string;
  addNewPlanButton?: string;
  addNewPlanButtonIconFill?: string;
};

interface HeaderProps extends PlanSummary {
  className?: string;
}

function PlanSummaryComponent({ children, className = '' }: PlanSummary) {
  return (
    <PlainCard
      as="article"
      className={`plan-card-width-restriction grid min-h-[472px] grid-rows-[auto_1fr] overflow-hidden p-0 xl:min-h-[500px] ${className}`}
    >
      {children}
    </PlainCard>
  );
}

function Header({ children, className = '' }: HeaderProps) {
  return (
    <header className={`rounder-tr rounded-tl px-4 py-5 ${className}`}>
      {children}
    </header>
  );
}

interface MainLabelProps {
  className?: string;
}

function MainLabel({ className = '' }: MainLabelProps) {
  return <span className={`chip ${className}`}>Main plan</span>;
}

interface PlanTypeProps extends PlanSummary {
  className?: string;
}

function PlanType({ children, className = '' }: PlanTypeProps) {
  return (
    <h2 className={`mt-4 text-xs lg:text-base ${className}`}>{children}</h2>
  );
}

interface PhoneNumberProps {
  number: string;
  className?: string;
}

function PhoneNumber({ number, className = '' }: PhoneNumberProps) {
  return (
    <div
      className={`text-default absolute top-8 right-4 lg:static ${className}`}
    >
      {number}
    </div>
  );
}

interface ActionProps {
  action?: string | null;
  className?: string;
  onClick: () => void;
}

function Action({ action, className = '', onClick }: ActionProps) {
  if (!action) return null;
  return (
    <Button
      variant="primary"
      className={`text-xxxs mt-2 w-fit p-2! lg:mt-0 lg:w-full ${className}`}
      onClick={onClick}
    >
      {action}
    </Button>
  );
}

interface DataRemainingTimeProps {
  remainingDaysMessage: string | null;
  className?: string;
}

function DataRemainingTime({
  remainingDaysMessage,
  className = ''
}: DataRemainingTimeProps) {
  return (
    <p className={`mb-0 text-[12px] ${className}`}>{remainingDaysMessage}</p>
  );
}

interface BodyProps extends PlanSummary {
  className?: string;
}

function Body({ children, className = '' }: BodyProps) {
  return (
    <div
      className={`flex h-full flex-col gap-1 bg-white px-4 py-5 ${className}`}
    >
      {children}
    </div>
  );
}

interface DataUsageProps {
  label: string;
  amount?: number;
  className?: string;
}

function DataUsage({ label, amount, className = '' }: DataUsageProps) {
  return (
    <div className={`mb-1 ${className}`}>
      <strong className="mr-1 text-xs font-bold">{amount}GB</strong>
      <span className="text-default">{label}</span>
    </div>
  );
}

interface LowDataWarningProps extends PlanSummary {
  className?: string;
}

function LowDataWarning({ children, className = '' }: LowDataWarningProps) {
  return (
    <div
      className={`border-gray-subtle-tint bg-warning text-xxxs flex items-center gap-2 rounded border p-[6px] ${className}`}
    >
      <BellIcon />
      {children}
    </div>
  );
}

interface TextsProps {
  count: number;
  className?: string;
}

function Texts({ count, className = '' }: TextsProps) {
  return (
    <div className={`text-sm font-medium ${className}`}>
      <strong className="mr-1 text-xs font-bold">{count}</strong>
      <span className="text-default">texts sent</span>
    </div>
  );
}

interface MinutesProps {
  count: number;
  className?: string;
}

function Minutes({ count, className = '' }: MinutesProps) {
  return (
    <div className={`text-sm font-medium ${className}`}>
      <strong className="mr-1 text-xs font-bold">{count}</strong>
      <span className="text-default">minutes used</span>
    </div>
  );
}

interface StatusProps {
  status: SubscriptionStatusInfo;
  className?: string;
}

function Status({ status, className = '' }: StatusProps) {
  return (
    <div
      className={`text-primary-hover text-xxxs mt-auto flex items-center gap-1 ${className}`}
    >
      <span
        className={`bg-${status.color} inline-block h-2 w-2 rounded-full`}
      ></span>
      {capitalize(status.name)}
    </div>
  );
}

PlanSummaryComponent.Header = Header;
PlanSummaryComponent.MainLabel = MainLabel;
PlanSummaryComponent.PlanType = PlanType;
PlanSummaryComponent.PhoneNumber = PhoneNumber;
PlanSummaryComponent.Body = Body;
PlanSummaryComponent.DataUsage = DataUsage;
PlanSummaryComponent.LowDataWarning = LowDataWarning;
PlanSummaryComponent.Texts = Texts;
PlanSummaryComponent.Minutes = Minutes;
PlanSummaryComponent.Status = Status;
PlanSummaryComponent.Action = Action;
PlanSummaryComponent.DataRemainingTime = DataRemainingTime;

export const PlanSummary = PlanSummaryComponent;
