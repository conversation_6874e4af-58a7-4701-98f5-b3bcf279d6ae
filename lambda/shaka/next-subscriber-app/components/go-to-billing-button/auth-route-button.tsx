import React from 'react';
import { useAuth } from '@/auth/hooks/use-auth';
import { NextStepLink } from '@/components/cta-button/next-step-link';
import { Route } from '@/src/uswitch/routes/route-config';

interface AuthRouteButtonProps {
  authPath: Route;
  fallbackPath: Route;
  className?: string;
  text?: string;
  onClick?: () => void;
}

export function AuthRouteButton({
  authPath,
  fallbackPath,
  className,
  text = 'Go to billing',
  onClick
}: AuthRouteButtonProps) {
  const { isAuthenticated } = useAuth();
  const pathname = isAuthenticated ? authPath : fallbackPath;

  return (
    <NextStepLink
      variant="primary"
      href={pathname}
      className={`mt-4 p-3 ${className}`}
      text={text}
      onClick={onClick}
    />
  );
}
