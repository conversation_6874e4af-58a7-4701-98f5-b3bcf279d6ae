import { useFocusError } from '@/hooks/useFocusError';
import React from 'react';
import { getDesiredCodeChangeDate } from '@/src/uswitch/utils/helpers';
import { Alert } from '@/components/alert/alert';
import { PacDeliveryDatePicker } from '@/src/uswitch/app/signup/_components/pac-delivery-date-picker/pac-delivery-date-picker';
import { UK_PUBLIC_HOLIDAYS } from '@/utils/constants';
import { formatPortingCode, getFieldError } from '@/utils/helpers';
import { NumberPortingFormData } from '@/schema/schema';

interface PACFormDataWithDate extends NumberPortingFormData {
  desired_date: string;
}

interface PACFormProps {
  setErrors: React.Dispatch<React.SetStateAction<string[]>>;
  errors: string[];
  handleSubmit: (data: PACFormDataWithDate) => void;
  formData: NumberPortingFormData;
  className?: string;
  formId?: string;
}

function PacCodeField({ defaultValue, error, onChange }) {
  return (
    <div>
      <label htmlFor="pacCode" className="font-semibold">
        Your PAC code
      </label>
      <input
        id="pacCode"
        name="pacCode"
        type="text"
        placeholder="e.g PAC123456"
        className={`input my-2 ${error ? 'border-2 border-[var(--color-error)]' : ''}`}
        defaultValue={defaultValue}
        onChange={onChange}
      />
      {error && (
        <Alert variant="error" message={error} align="left" className="mb-4" />
      )}
    </div>
  );
}

function PhoneNumberField({ defaultValue, error, onChange }) {
  return (
    <div>
      <label htmlFor="incoming_phone_number" className="font-semibold">
        Your phone number
      </label>
      <input
        id="incoming_phone_number"
        name="incoming_phone_number"
        inputMode="numeric"
        type="tel"
        maxLength={13}
        onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
          e.target.value = e.target.value.replace(/[^0-9]/g, '');
        }}
        autoComplete="tel"
        placeholder="07123456789"
        className={`input my-2 ${error ? 'border-2 border-[var(--color-error)]' : ''}`}
        pattern="[0-9]*"
        defaultValue={defaultValue}
        onChange={onChange}
      />
      {error && (
        <Alert variant="error" message={error} align="left" className="mb-4" />
      )}
    </div>
  );
}

function PortingDateField({ formData, error }) {
  return (
    <div className="lg:col-span-2">
      <label htmlFor="switchingDate" className="font-semibold">
        Choose your porting date (optional)
      </label>
      <PacDeliveryDatePicker
        formData={formData}
        holidays={UK_PUBLIC_HOLIDAYS}
      />
      {error && (
        <Alert
          variant="error"
          message={error}
          align="left"
          className="mt-2 mb-4"
        />
      )}
    </div>
  );
}

export function NumberPortingForm({
  errors = {},
  onSubmit,
  className,
  formId = 'pacForm',
  children
}) {
  useFocusError(errors);

  const handleFormSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const form = e.currentTarget;
    const data = new FormData(form);

    const day = data.get('day') as string;
    const month = data.get('month') as string;
    const year = data.get('year') as string;
    const pacCode = data.get('pacCode') as string;
    const incoming_phone_number = data.get('incoming_phone_number') as string;

    onSubmit({
      pac_code: formatPortingCode(pacCode),
      incoming_phone_number,
      desired_date: getDesiredCodeChangeDate(day, month, year)
    });
  };

  return (
    <form
      onSubmit={handleFormSubmit}
      id={formId}
      className={`mt-4 grid gap-4 lg:grid-cols-2 ${className || ''}`}
      // autoComplete="off"
    >
      {children}
    </form>
  );
}

export function NumberPortingFormContainer({
  formData,
  errors,
  setErrors,
  handleSubmit,
  className,
  formId
}: PACFormProps) {
  const errorMap = {
    pacCode: getFieldError('pacCode', errors),
    incoming_phone_number: getFieldError('incoming_phone_number', errors),
    desired_date: getFieldError('desired_date', errors),
    network: getFieldError('network', errors)
  };

  const clearErrors = () => setErrors([]);

  return (
    <>
      <NumberPortingForm
        errors={errorMap}
        onSubmit={handleSubmit}
        className={className}
        formId={formId}
      >
        <PacCodeField
          defaultValue={formData.pac_code}
          error={errorMap.pacCode}
          onChange={clearErrors}
        />
        <PhoneNumberField
          defaultValue={formData.incoming_phone_number}
          error={errorMap.incoming_phone_number}
          onChange={clearErrors}
        />
        <PortingDateField formData={formData} error={errorMap.desired_date} />
      </NumberPortingForm>
      {errorMap.network && (
        <Alert variant="error" className="mt-3" message={errorMap.network} />
      )}
    </>
  );
}
