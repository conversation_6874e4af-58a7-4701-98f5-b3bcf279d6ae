'use client';

import { useState, useEffect, use } from 'react';
import { useWizard } from '@/context/wizard/wizard';
import React from 'react';
import { usePathname } from 'next/navigation';
import { getSignupPageName } from '@/src/uswitch/utils/helpers';

interface ProgressBarContextValue {
  currentStepNumber: number;
  minimumStepNumber: number;
  maximumStepNumber: number;
  progressPercentage: number;
  ariaLabel?: string;
}
const ProgressBarContext = React.createContext<
  ProgressBarContextValue | undefined
>(undefined);

interface ProgressBarProps extends React.PropsWithChildren {
  ariaLabel?: string;
  withLabel?: boolean;
  textColor?: string;
}

function ProgressBar({
  ariaLabel,
  children,
  withLabel,
  textColor = 'text-primary'
}: ProgressBarProps) {
  const { currentStepNumber, totalSteps } = useWizard();
  const stepNumber = currentStepNumber ?? 0;
  const maximumStepNumber = totalSteps ?? 1;
  const minimumStepNumber = 0;

  const completedStepsCount = stepNumber - minimumStepNumber;
  const totalStepsCount = maximumStepNumber - minimumStepNumber;
  const progress = completedStepsCount / totalStepsCount;
  const progressPercentage = totalStepsCount > 0 ? progress * 100 : 0;

  const progressBarContextValue = {
    currentStepNumber: stepNumber,
    minimumStepNumber,
    maximumStepNumber,
    progressPercentage,
    ariaLabel
  };

  const pathname = usePathname();
  const pageName = getSignupPageName(pathname);

  useEffect(() => {
    if (pageName) {
      document.title = `${pageName} - Step ${stepNumber} of ${maximumStepNumber}`;
    } else {
      document.title = `Sign up – Step ${stepNumber} of ${maximumStepNumber}`;
    }
  }, [stepNumber, maximumStepNumber, pageName]);

  const ariaProps = {
    'aria-valuenow': stepNumber,
    'aria-valuemin': minimumStepNumber,
    'aria-valuemax': maximumStepNumber
  };

  return (
    <ProgressBarContext.Provider value={progressBarContextValue}>
      <div
        className="w-full"
        role="progressbar"
        aria-label={ariaLabel}
        {...ariaProps}
      >
        <p
          className={
            withLabel
              ? `mb-2 font-[var(--font-weight-heading)] ${textColor}`
              : 'sr-only'
          }
          aria-atomic="true"
          aria-live="polite"
        >
          {`Step ${stepNumber} of ${maximumStepNumber}`}
        </p>
        <progress
          className="sr-only"
          value={stepNumber}
          max={maximumStepNumber}
          aria-label={ariaLabel}
        />
        {children}
      </div>
    </ProgressBarContext.Provider>
  );
}

interface ProgressBarTrackProps extends React.PropsWithChildren {
  className?: string;
}
function ProgressBarTrack({ className, children }: ProgressBarTrackProps) {
  return <div className={`bg-gray-subtle ${className || ''}`}>{children}</div>;
}

interface ProgressBarBarProps {
  className?: string;
  style?: React.CSSProperties;
  animate?: boolean;
  transitionDuration?: number;
}
function ProgressBarBar({
  className,
  style,
  animate = true,
  transitionDuration = 700
}: ProgressBarBarProps) {
  const progressBarContext = use(ProgressBarContext);
  if (!progressBarContext)
    throw new Error('ProgressBar.Bar must be used within ProgressBar');
  const { progressPercentage } = progressBarContext;

  const [animatedProgressBarWidth, setAnimatedProgressBarWidth] = useState(0);

  useEffect(() => {
    if (animate) {
      setAnimatedProgressBarWidth(0);
      const progressBarAnimationTimer = setTimeout(
        () => setAnimatedProgressBarWidth(progressPercentage),
        50
      );
      return () => clearTimeout(progressBarAnimationTimer);
    } else {
      setAnimatedProgressBarWidth(progressPercentage);
    }
  }, [progressPercentage, animate]);

  return (
    <div
      className={`h-1 transition-all ease-in-out ${className || ''}`}
      style={{
        ...style,
        width: animate
          ? `${animatedProgressBarWidth}%`
          : `${progressPercentage}%`,
        transition: animate ? `width ${transitionDuration}ms` : undefined
      }}
      aria-hidden="true"
      data-testid="progress-bar"
    />
  );
}

ProgressBar.Track = ProgressBarTrack;
ProgressBar.Bar = ProgressBarBar;

export { ProgressBar };
