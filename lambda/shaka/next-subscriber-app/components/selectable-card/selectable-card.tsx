import { constructAriaLabel } from '@/src/uswitch/utils/helpers';

interface SelectableCardProps {
  itemId: string | number;
  currentSelectedId?: string | number | null;
  selected?: boolean;
  onSelectionChange?: (itemId: string | number) => void;
  onChange?: (checked: boolean) => void;
  value?: string | number;
  price: number;
  originalPrice?: number | string;
  title?: string;
  description?: string;
  className?: string;
  selectedClassName?: string;
  pricePeriod?: string;
}

export function SelectableCard({
  itemId,
  currentSelectedId,
  selected,
  onSelectionChange,
  onChange,
  value,
  price,
  originalPrice = 1.23,
  title,
  description,
  className = 'border-border cursor-pointer rounded border px-4 py-2 hover:bg-[#E7E7E7] flex justify-center items-center flex-col min-h-[68px]',
  selectedClassName = 'outline-primary outline-[2px] active'
}: SelectableCardProps) {
  const isSelected =
    selected !== undefined ? selected : currentSelectedId === itemId;

  const isNoneAddon = price === 0;

  const handleCardClick = () => {
    if (onChange) {
      onChange(!isSelected);
    } else if (onSelectionChange) {
      onSelectionChange(itemId);
    }
  };

  const combinedClassName = [
    className,
    isSelected && selectedClassName,
    isNoneAddon && 'col-span-2 lg:col-span-1'
  ]
    .filter(Boolean)
    .join(' ');

  return (
    <button
      data-testid={`selectable-card-${price}`}
      type="button"
      onClick={handleCardClick}
      className={combinedClassName}
      aria-pressed={isSelected}
      aria-label={constructAriaLabel(
        isNoneAddon,
        price,
        title,
        description,
        value,
        originalPrice
      )}
    >
      <div aria-hidden="true">
        {title && <div className="font-bold">{title}</div>}
        {description && <div className="text-sm">{description}</div>}
        {price !== 0 && value && (
          <div className="text-[18px] font-bold">{value}GB data</div>
        )}
        {isNoneAddon ? (
          <strong className="py-2 text-[18px] lg:px-[23px]">None</strong>
        ) : (
          <strong className="text-xxxs font-normal">
            {originalPrice && <s className="opacity-60">£{originalPrice}</s>} £
            {price} a month
          </strong>
        )}
      </div>
    </button>
  );
}
