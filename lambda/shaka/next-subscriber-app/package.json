{"name": "next-subscriber-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:subscriber": "cp client-config/subscriber.env ./src/subscriber-app/.env.local && next dev ./src/subscriber-app --port 3001 --turbopack", "dev:uswitch": "cp client-config/uswitch.env ./src/uswitch/.env.local && next dev ./src/uswitch --port 3000 --turbopack", "dev:deliveroo": "cp client-config/deliveroo.env ./src/deliveroo/.env.local && next dev ./src/deliveroo --port 3002 --turbopack", "build": "next build", "build:subscriber": "cp client-config/subscriber.env .env.local && next build ./src/subscriber-app", "build:uswitch": "cp client-config/uswitch.env .env.local && next build ./src/uswitch", "build:deliveroo": "cp client-config/deliveroo.env .env.local && next build ./src/deliveroo", "build:clientA": "cp client-config/subscriber.env .env.local && node scripts/set-client.js clientA && next build ./src/subscriber-app", "build:clientB": "cp client-config/subscriber.env .env.local && node scripts/set-client.js clientB && next build ./src/subscriber-app", "ts-check": "tsc --noEmit", "ts-check:subscriber": "tsc --noEmit -p ./src/subscriber-app/tsconfig.json", "ts-check:uswitch": "tsc --noEmit -p ./src/uswitch/tsconfig.json", "start": "next start", "start:subscriber": "next start ./src/subscriber-app", "start:uswitch": "next start ./src/uswitch", "start:deliveroo": "next start ./src/deliveroo", "start:clientA": "next start ./src/subscriber-app", "start:clientB": "next start ./src/subscriber-app", "lint:uswitch": "next lint ./src/uswitch", "lint:subscriber": "next lint ./src/subscriber-app", "lint:deliveroo": "next lint ./src/subscriber-app", "lint": "pnpm lint:uswitch && pnpm lint:subscriber", "prettier": "prettier --write src/**/*.{ts,tsx} --config .prettierrc.json", "prettier:uswitch": "prettier --write src/uswitch/**/*.{ts,tsx} --config .prettierrc.json", "prettier:deliveroo": "prettier --write src/deliveroo/**/*.{ts,tsx} --config .prettierrc.json", "prettier:subscriber": "prettier --write src/subscriber-app/**/*.{ts,tsx} --config .prettierrc.json", "test": "vitest run", "test:watchMode": "vitest watch", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch", "e2e": "npx playwright test", "e2e:subscriber": "npx playwright test --project=subscriber-app", "e2e:global": "npx playwright test --project=global", "e2e:global:ui": "npx playwright test --project=global --ui", "e2e:uswitch": "npx playwright test --project=uswitch-app", "e2e:deliveroo": "npx playwright test --project=deliveroo-app", "e2e:ui": "npx playwright test --ui", "e2e:report": "npx playwright show-report", "msw:init": "msw init ./public --save"}, "dependencies": {"@next/third-parties": "^15.5.2", "@stripe/react-stripe-js": "^3.1.1", "@stripe/stripe-js": "^5.6.0", "@tanstack/react-query": "^5.80.10", "@tanstack/react-query-devtools": "^5.80.10", "axios": "^1.11.0", "dotenv": "^16.5.0", "jwt-decode": "^4.0.0", "next": "15.5.2", "postcss": "^8.5.3", "posthog-js": "^1.258.2", "posthog-node": "^5.6.0", "react": "^19.0.0", "react-dom": "^19.0.0", "zod": "^3.25.17"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@msw/playwright": "^0.4.2", "@next/eslint-plugin-next": "^15.3.3", "@playwright/test": "^1.53.1", "@tailwindcss/postcss": "^4.1.5", "@tanstack/eslint-plugin-query": "^5.78.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@vitejs/plugin-react": "^4.5.1", "@vitest/coverage-v8": "3.2.0", "@vitest/ui": "3.2.0", "eslint": "^9", "eslint-config-next": "15.3.1", "eslint-plugin-react-hooks": "^5.2.0", "jsdom": "^26.1.0", "msw": "^2.9.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.1.5", "typescript": "^5", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.0"}, "packageManager": "pnpm@10.8.0+sha512.0e82714d1b5b43c74610193cb20734897c1d00de89d0e18420aebc5977fa13d780a9cb05734624e81ebd81cc876cd464794850641c48b9544326b5622ca29971", "msw": {"workerDirectory": ["public", "src/uswitch/public", "src/subscriber-app/public"]}}