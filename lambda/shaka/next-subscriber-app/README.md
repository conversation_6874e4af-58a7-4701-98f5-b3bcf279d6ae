# Next.js 15 Monorepo with React 19

This is a Next.js 15 monorepo setup with React 19 that allows multiple Next.js applications to share code, components, and configuration.

## Directory Structure

```
next-subscriber-app/
├── package.json             # Root package.json for managing all apps
├── next.config.ts           # Shared Next.js configuration (TypeScript)
├── tsconfig.json            # TypeScript configuration
├── .env.local               # Local environment variables
├── .eslintrc.json           # ESLint configuration
├── .prettierrc.json         # Prettier configuration
├── global-styles.css        # Global CSS styles
├── auth/                    # Authentication logic and providers
├── client-config/           # Client environment configs (per app)
├── components/              # Shared UI components
├── context/                 # React contexts (providers, consumers)
├── hooks/                   # Shared React hooks
├── lib/                     # Shared libraries (analytics, feature flags, etc.)
├── mocks/                   # Mock data and handlers for testing
├── public/                  # Public assets (favicon, images, etc.)
├── services/                # Service layer (e.g., userService)
├── src/                     # Contains Next.js apps
│   ├── uswitch/             # uswitch app
│   │   └── app/             # Next.js App Router structure
│   └── subscriber-app/      # subscriber-app
│       └── app/             # Next.js App Router structure
├── tests/                   # High-level tests for shared/common logic
│   ├── api/                 # API layer tests
│   ├── integration/         # Integration tests
│   ├── unit/                # Unit tests
│   └── utils/               # Test utilities
├── utils/                   # Shared utility functions (constants, formatters, helpers)
```

## Getting Started

### Installation

This project uses pnpm as the package manager.

```bash
# Install dependencies
pnpm install
```

### Development

To run a specific app in development mode:

```bash
# Run the uswitch app
pnpm run dev:uswitch

# Run the subscriber app
pnpm run dev:subscriber
```

### Building for Production

To build a specific app for production:

```bash
# Build the uswitch app
pnpm run build:uswitch

# Build the subscriber app
pnpm run build:subscriber
```

### Running in Production

To start a specific app in production mode:

```bash
# Start the uswitch app
pnpm run start:uswitch

# Start the subscriber app
pnpm run start:subscriber
```

## Adding a New App

1. Create a new directory in `src/` with your app name
2. Add the Next.js app structure (app/ directory for App Router)
3. Update the `package.json` scripts to include your new app
4. Update the `next.config.ts` if needed

## Environment Variables

- App-specific environment variables should be defined in `src/[app-name]/.env.local`

# Environment & Client Config Flow

This document explains how environment variables and client configuration work in this monorepo, focusing on the API/config flow for the subscriber and uswitch apps. It also provides a checklist for adding a new client.

## Overview

- Each app (e.g., subscriber, uswitch) has its own environment file containing client-specific settings.
- At app start, the relevant environment file is copied to the correct location as `.env.local`.
- The `getClientConfig` function reads these environment variables and builds a config object used throughout the app, including for API calls.
- The configuration is fixed for the lifetime of the app process and does not change until the server is restarted with new environment variables.

## 1. Environment Variable Setup

All client-specific env files are stored in `client-config/` (e.g., `client-config/subscriber.env`, `client-config/uswitch.env`).

Each env file contains variables like:

```
NEXT_PUBLIC_CLIENT_ID=subscriber
NEXT_PUBLIC_CLIENT_NAME=Subscriber
etc.
```

## 2. Scripts in package.json

The scripts automate copying the correct env file and starting the app.

Example scripts:

```json
"dev:subscriber": "cp client-config/subscriber.env ./src/subscriber-app/.env.local && next dev ./src/subscriber-app --port 3000 --turbopack",
"dev:uswitch": "cp client-config/uswitch.env ./src/uswitch/.env.local && next dev ./src/uswitch --port 3001 --turbopack"
```

For builds:

```json
"build:subscriber": "cp client-config/subscriber.env .env.local && next build ./src/subscriber-app",
"build:uswitch": "cp client-config/uswitch.env .env.local && next build ./src/uswitch"
```

How it works:

1. The script copies the relevant env file to the correct app folder as `.env.local`.
2. Then it starts the Next.js dev/build process for that app.

## 3. How Environment Variables Are Loaded

- When the app starts, Next.js reads `.env.local` from the app directory.
- These variables are loaded into `process.env` and are available throughout the app.
- The values do not change until the app/server is restarted with a different `.env.local`.

## 4. The Role of client-config/client-config.ts

This file exports `getClientConfig`, which builds a `ClientConfig` object by reading from `process.env`.

Example:

```typescript
export function getClientConfig(): ClientConfig {
  return {
    clientId: process.env.NEXT_PUBLIC_CLIENT_ID || 'test-client',
    name: process.env.NEXT_PUBLIC_CLIENT_NAME || 'Test Client',
    apiBaseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || 'https://api.shaka.tel',
    // ...other config fields
  };
}
```

This function is the single source of truth for client configuration.

## 5. Checklist: Adding a New Client

1. **Create a new env file**:
    - Add `client-config/newclient.env` with all required variables. See `.env.local.example` at the root

2. **Add scripts to package.json**:
    - Add a `dev:newclient` script to copy the env file and start the app ( follow the pattern ).
    - Add a `build:newclient` script for production builds ( follow the pattern ).

3. **Update .gitignore if needed**:
    - Ensure new env files are ignored !

4. **Use getClientConfig in your API and config code**:
    - No further changes are required unless you add new config fields.

## 6. Key Points for Developers

- Always restart the server after changing env files.
- Only variables present at app start are available in `process.env`.
- All config logic should go through `getClientConfig` for consistency.
- Do not change config at runtime; it is static for the app's lifetime.

## Example: Adding a Client Called "acme"

1. Create `client-config/acme.env`:
```
NEXT_PUBLIC_CLIENT_ID=acme
NEXT_PUBLIC_CLIENT_NAME=Acme Portal
etc.
```

2. Add to `package.json`:
```json
"dev:acme": "cp client-config/acme.env ./src/acme-app/.env.local && next dev ./src/acme-app --port 3002 --turbopack",
"build:acme": "cp client-config/acme.env .env.local && next build ./src/acme-app"
```

3. Use `getClientConfig()` in your code as usual.

4. If you need to add new config fields, update both the env files and the `ClientConfig` interface in `client-config/client-config.ts`.

## Testing

This project uses high-level tests for shared and common logic. The test suite is organized as follows:

- **tests/unit/**: Unit tests for isolated logic and utilities.
- **tests/integration/**: Integration tests covering combined flows and interactions between modules.
- **tests/api/**: Tests for API layer and endpoint logic.
- **tests/utils/**: Test-specific helpers and mocks.

### Testing Tools

- **Vitest**: Main test runner and assertion library.
- **@testing-library/react**: React component testing utilities.
- **@testing-library/jest-dom**: Custom DOM matchers for assertions.
- **@testing-library/user-event**: Simulates user interactions.
- **jsdom**: Provides a DOM environment for tests.
- **msw**: Mock Service Worker for API mocking.

#### Running Tests

To run all tests:
```bash
pnpm test
```
To run tests in watch mode:
```bash
pnpm test:watch
```
To view test coverage:
```bash
pnpm test:coverage
```

Vitest UI is also available:
```bash
pnpm test:ui
```

## TODO

- [ ] Accessibility - linter ?
- [ ] update absolute paths - partially done !! ?
