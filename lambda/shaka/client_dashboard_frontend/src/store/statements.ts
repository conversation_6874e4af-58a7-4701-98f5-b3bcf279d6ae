import { create } from 'zustand';
import { StatementsByYears } from 'src/types/statements';

type State = { statements: StatementsByYears; isLoaded: boolean };

const useStatementsStore = create<State>(() => ({
  statements: {},
  isLoaded: false
}));

const selectorStatements = (state: State) => state.statements;
const selectorStatementsLoaded = (state: State) => state.isLoaded;

const setStatements = (statements: StatementsByYears) =>
  useStatementsStore.setState(() => ({ statements, isLoaded: true }));

export {
  setStatements,
  useStatementsStore,
  selectorStatements,
  selectorStatementsLoaded
};
