import { create } from 'zustand';
import { Perk } from 'src/types/perks';

type State = { perks: Perk[]; isLoaded: boolean };

const usePerksStore = create<State>(() => ({
  perks: [],
  isLoaded: false
}));

const selectorPerks = (state: State) => state.perks;
const selectorPerksLoaded = (state: State) => state.isLoaded;

const selectorPerkById = (id?: string) => (state: State) =>
  state.perks.find((perk) => perk.id?.toString() == id);

const setPerks = (perks: Perk[]) =>
  usePerksStore.setState(() => ({ perks, isLoaded: true }));

export {
  usePerksStore,
  selectorPerks,
  selectorPerkById,
  selectorPerksLoaded,
  setPerks
};
