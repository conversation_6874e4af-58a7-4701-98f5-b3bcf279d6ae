import { User } from 'src/types/users';
import { create } from 'zustand';

type State = { users: User[]; isLoaded: boolean };
type Action = {
  setUsers: (users: User[]) => void;
};

type UsersStore = State & Action;

export const useUsersStore = create<UsersStore>((set) => ({
  users: [],
  isLoaded: false,
  setUsers: (users: User[]) => set({ users, isLoaded: true })
}));

export const selectorUsers = (state: UsersStore) => state.users;
export const selectorUserById =
  (id?: number | string) => (state: UsersStore) => {
    return id && state.users.find((user) => user.id === Number(id));
  };
export const selectorUsersLoaded = (state: UsersStore) => state.isLoaded;
export const selectorSetUsers = (state: UsersStore) => state.setUsers;
