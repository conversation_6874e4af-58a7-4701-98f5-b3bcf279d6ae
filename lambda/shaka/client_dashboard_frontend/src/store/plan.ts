import { create } from 'zustand';
import { Plan } from 'src/types/plans';

type State = { plans: Plan[]; isLoaded: boolean };

export const usePlansStore = create<State>(() => ({
  plans: [],
  isLoaded: false
}));

export const selectorPlans = (state: State) => state.plans;
export const selectorPlanById = (id?: string) => (state: State) =>
  state.plans.find((plan) => plan.id.toString() == id);

export const selectorPlansLoaded = (state: State) => state.isLoaded;

export const setPlans = (plans: Plan[]) =>
  usePlansStore.setState(() => ({ plans, isLoaded: true }));
