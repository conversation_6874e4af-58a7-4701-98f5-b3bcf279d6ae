import { create } from 'zustand';
import { Beam, BeamStatus } from 'src/types/sms';

type State = { beams: Beam[]; isLoaded: boolean };
type Action = {
  setBeams: (beams: Beam[]) => void;
};

type BeamsStore = State & Action;

const useBeamsStore = create<BeamsStore>((set) => ({
  beams: [],
  isLoaded: false,
  setBeams: (beams: Beam[]) => set(() => ({ beams, isLoaded: true }))
}));

const selectorBeams = (state: BeamsStore) => state.beams;
const selectorBeamsLoaded = (state: BeamsStore) => state.isLoaded;

const selectorBeamsScheduled = (state: BeamsStore) =>
  state.beams.filter(
    (beam) => beam.send_on && beam.status === BeamStatus.TO_SEND
  );

const selectorBeamsPublished = (state: BeamsStore) =>
  state.beams.filter(
    (beam) =>
      beam.status === BeamStatus.SENDING ||
      beam.status === BeamStatus.SENT ||
      beam.status === BeamStatus.ERRORED ||
      (beam.status === BeamStatus.TO_SEND && !beam.send_on)
  );

const selectorBeamsById = (state: BeamsStore) =>
  state.beams.reduce(
    (acc, beam) => {
      acc[beam.id] = beam;
      return acc;
    },
    {} as Record<number, Beam>
  );

const setBeams = (beams: Beam[]) =>
  useBeamsStore.setState(() => ({ beams, isLoaded: true }));

export {
  useBeamsStore,
  setBeams,
  selectorBeams,
  selectorBeamsById,
  selectorBeamsLoaded,
  selectorBeamsScheduled,
  selectorBeamsPublished
};
