import { Browser<PERSON>outer } from 'react-router-dom';
import { AuthContextProvider } from 'src/contexts/AuthContext';
import { BrandingContextProvider } from './contexts/BrandingContext';
import { SpnContextProvider } from './contexts/SpnContext';
import Routes from './routes/Routes';

function App() {
  return (
    <BrowserRouter>
      <AuthContextProvider>
        <BrandingContextProvider>
          <SpnContextProvider>
            <Routes />
          </SpnContextProvider>
        </BrandingContextProvider>
      </AuthContextProvider>
    </BrowserRouter>
  );
}

export default App;
