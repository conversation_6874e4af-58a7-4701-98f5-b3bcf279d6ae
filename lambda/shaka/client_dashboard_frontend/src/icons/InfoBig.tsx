export function InfoBigIcon({ ...props }: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="37"
      height="37"
      viewBox="0 0 37 37"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M18.5 0C8.2991 0 0 8.2991 0 18.5C0 28.7009 8.2991 37 18.5 37C28.7009 37 37 28.7009 37 18.5C37 8.2991 28.7009 0 18.5 0ZM18.5 7.585C18.9757 7.585 19.4406 7.72605 19.8361 7.99032C20.2316 8.25458 20.5399 8.63019 20.7219 9.06965C20.904 9.5091 20.9516 9.99267 20.8588 10.4592C20.766 10.9257 20.5369 11.3542 20.2006 11.6906C19.8642 12.0269 19.4357 12.256 18.9692 12.3488C18.5027 12.4416 18.0191 12.394 17.5796 12.2119C17.1402 12.0299 16.7646 11.7216 16.5003 11.3261C16.2361 10.9306 16.095 10.4657 16.095 9.99C16.095 9.35215 16.3484 8.74043 16.7994 8.28941C17.2504 7.83838 17.8622 7.585 18.5 7.585ZM22.94 28.49H14.8C14.4075 28.49 14.031 28.3341 13.7535 28.0565C13.4759 27.779 13.32 27.4025 13.32 27.01C13.32 26.6175 13.4759 26.241 13.7535 25.9635C14.031 25.6859 14.4075 25.53 14.8 25.53H17.39V17.39H15.91C15.5175 17.39 15.141 17.2341 14.8635 16.9565C14.5859 16.679 14.43 16.3025 14.43 15.91C14.43 15.5175 14.5859 15.141 14.8635 14.8635C15.141 14.5859 15.5175 14.43 15.91 14.43H18.87C19.2625 14.43 19.639 14.5859 19.9165 14.8635C20.1941 15.141 20.35 15.5175 20.35 15.91V25.53H22.94C23.3325 25.53 23.709 25.6859 23.9865 25.9635C24.2641 26.241 24.42 26.6175 24.42 27.01C24.42 27.4025 24.2641 27.779 23.9865 28.0565C23.709 28.3341 23.3325 28.49 22.94 28.49Z"
        fill="white"
      />
    </svg>
  );
}
