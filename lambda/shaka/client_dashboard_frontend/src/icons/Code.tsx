type Props = {
  fill?: string;
};

export default function CodeIcon({ fill = '#9E9EA8' }: Props) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
    >
      <path
        d="M17.1425 14.2172L17.1311 14.21C17.0604 14.1615 17.0001 14.0994 16.9538 14.0272C16.9074 13.955 16.876 13.8743 16.8614 13.7898C16.8468 13.7053 16.8492 13.6187 16.8685 13.5352C16.8879 13.4516 16.9238 13.3728 16.974 13.3033C17.9515 11.6046 18.2751 9.60744 17.8841 7.68697C17.4931 5.76651 16.4143 4.05489 14.8504 2.87362C13.2866 1.69236 11.3452 1.12273 9.39098 1.27175C7.43678 1.42076 5.60423 2.27816 4.23754 3.68289C2.87085 5.08762 2.06408 6.94302 1.96876 8.90057C1.87344 10.8581 2.49614 12.7831 3.71988 14.314C4.94363 15.8449 6.68421 16.8762 8.61469 17.2144C10.5452 17.5526 12.5327 17.1743 14.2039 16.1505C14.4895 15.9749 14.8608 16.0506 15.0564 16.3219L15.0592 16.3233C15.1061 16.3883 15.1394 16.4622 15.157 16.5404C15.1746 16.6186 15.1761 16.6996 15.1616 16.7785C15.1471 16.8573 15.1167 16.9324 15.0724 16.9992C15.028 17.066 14.9706 17.1232 14.9036 17.1672L14.7837 17.2457C12.8434 18.4122 10.5437 18.8293 8.3174 18.4185C6.09108 18.0077 4.09167 16.7974 2.69547 15.0153C1.29927 13.2332 0.602563 11.0022 0.736477 8.74229C0.87039 6.48235 1.82569 4.34929 3.42258 2.74455C5.01947 1.13981 7.14782 0.174069 9.40707 0.0290758C11.6663 -0.115917 13.9007 0.569842 15.6896 1.95729C17.4785 3.34473 18.6986 5.33818 19.1203 7.56246C19.542 9.78674 19.1362 12.0884 17.9793 14.0344C17.9383 14.1035 17.8835 14.1634 17.8184 14.2105C17.7533 14.2575 17.6791 14.2906 17.6007 14.3077C17.5222 14.3249 17.441 14.3257 17.3622 14.3101C17.2834 14.2945 17.2086 14.2629 17.1425 14.2172ZM6.64756 6.43232C6.78749 6.29382 7.13018 6.24384 7.29725 6.43232C7.50429 6.65079 7.43718 7.00776 7.29725 7.15769L5.13685 9.25669L7.24441 11.2529C7.38435 11.3914 7.40291 11.7869 7.24441 11.944C7.05308 12.1339 6.66184 12.1039 6.5319 11.974L4.06879 9.51942C4.00191 9.45202 3.96438 9.36092 3.96438 9.26597C3.96438 9.17102 4.00191 9.07992 4.06879 9.01252L6.64756 6.43375V6.43232ZM13.3529 6.43232L15.9317 9.01109C15.9986 9.07849 16.0361 9.16959 16.0361 9.26454C16.0361 9.35949 15.9986 9.45059 15.9317 9.51799L13.47 11.9754C13.3401 12.1039 12.9702 12.1853 12.7561 11.9754C12.5547 11.7755 12.6176 11.3642 12.7561 11.2257L14.8722 9.25954L12.6875 7.1377C12.5476 6.98777 12.5019 6.61795 12.6875 6.43375C12.8689 6.25527 13.213 6.29525 13.3529 6.43375V6.43232ZM10.7056 5.91828C10.7756 5.73266 11.1226 5.61557 11.3439 5.69839C11.5552 5.77692 11.6794 6.06964 11.6223 6.25527L9.28058 12.695C9.21204 12.8807 8.87792 12.9549 8.69086 12.8849C8.49239 12.8164 8.27678 12.5423 8.34817 12.3581L10.7042 5.91828H10.7056Z"
        fill={fill}
      />
    </svg>
  );
}
