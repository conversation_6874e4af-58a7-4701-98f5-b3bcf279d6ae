import {
  ReactNode,
  createContext,
  useContext,
  useEffect,
  useState
} from 'react';
import { fetchSpn } from 'src/api/spn';
import { useAsync } from 'src/api/useApi';
import useClientData from 'src/hooks/useClient';
import { AuthContext } from './AuthContext';

type Props = {
  children: ReactNode;
};

type Spn = {
  id: number;
  spn: string;
};

export const SpnContext = createContext<{
  spn: string | null;
  setSpn: React.Dispatch<React.SetStateAction<string | null>>;
  isLoading: boolean;
}>({
  spn: '',
  setSpn: () => {},
  isLoading: true
});

export const SpnContextProvider = ({ children }: Props) => {
  const { isLoggedIn } = useContext(AuthContext);
  const [spn, setSpn] = useState<string | null>(null);
  const { clientData } = useClientData();
  const { run: doFetchSpn } = useAsync(fetchSpn);

  useEffect(() => {
    if (!isLoggedIn) return;

    if (clientData?.id && !spn) {
      doFetchSpn(clientData.id)
        .then((res: Spn) => {
          setSpn(res.spn);
        })
        .catch(() => {
          setSpn(null);
        });
    }
  }, [clientData, fetchSpn, isLoggedIn]);

  return (
    <SpnContext.Provider
      value={{
        spn,
        setSpn,
        isLoading: Boolean(!spn)
      }}
    >
      {children}
    </SpnContext.Provider>
  );
};
