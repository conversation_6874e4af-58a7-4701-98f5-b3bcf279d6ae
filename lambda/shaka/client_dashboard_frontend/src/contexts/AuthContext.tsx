import { ReactNode, createContext, useEffect, useState } from 'react';
import { jwtDecode } from 'jwt-decode';
import { useLocation, useNavigate } from 'react-router-dom';
import { ROUTES } from 'src/config/navigation';
import { LoginData } from 'src/types/auth';
import {
  deleteAuthSessionKeys,
  setAuthSessionKeys
} from 'src/localStorage/auth';

export const AuthContext = createContext<any>({});

type Props = {
  children: ReactNode;
};

type User = {
  email: string;
};

export const AuthContextProvider = ({ children }: Props) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [user, setUser] = useState<User | null>(null);
  const [isLoggedIn, setIsLoggedIn] = useState(
    Boolean(localStorage.getItem('id_token'))
  );

  const parseToken = (token: string) => {
    const userData = jwtDecode(token);

    if ('email' in userData && 'cognito:username' in userData) {
      setUser({
        email: userData.email as string
      });

      return true;
    }

    return false;
  };

  const setAuthSession = (data: LoginData) => {
    setAuthSessionKeys(data);

    setIsLoggedIn(true);
  };

  const deleteAuthSession = () => {
    deleteAuthSessionKeys();

    setUser(null);
    setIsLoggedIn(false);
  };

  const logoutUser = () => {
    deleteAuthSession();

    // Redirect to login page if not in public login pages
    if (!location.pathname.includes(ROUTES.LOGIN)) {
      navigate(ROUTES.LOGIN, { replace: true });
    }
  };

  const loginUser = ({ access_token, id_token, refresh_token }: LoginData) => {
    const isValidToken = parseToken(id_token);

    if (!isValidToken) {
      logoutUser();
      return;
    }

    setAuthSession({ access_token, id_token, refresh_token });
  };

  useEffect(() => {
    if (!isLoggedIn) {
      logoutUser();
      return;
    }

    parseToken(localStorage.getItem('id_token') as string);
  }, []);

  return (
    <AuthContext.Provider
      value={{
        isLoggedIn,
        parseToken,
        loginUser,
        logoutUser,
        user
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};
