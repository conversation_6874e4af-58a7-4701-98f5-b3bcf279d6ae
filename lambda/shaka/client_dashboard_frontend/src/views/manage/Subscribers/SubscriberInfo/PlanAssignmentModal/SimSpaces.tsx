export default function SimSpaces({
  backSide,
  barcode,
  p1,
  p2,
  p3
}: {
  backSide?: boolean;
  barcode?: string;
  p1: string;
  p2: string;
  p3: string;
}) {
  return (
    <svg viewBox="0 0 391 498" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M56.6442 173.578L56.6442 154.448C58.2636 149.971 60.0326 148.245 64.8408 146.637L216.25 146.639"
        stroke="#4f4e62"
        strokeWidth="10"
      />
      <path
        d="M56.644 265.324L56.6452 284.69C58.2649 289.222 60.0339 290.969 64.8423 292.597L216.252 292.605"
        stroke="#4f4e62"
        strokeWidth="10"
      />
      <path
        d="M220.523 146.622H285.312C289.784 148.13 291.514 149.793 293.141 154.327L293.141 272.619L273.535 292.637H220.523"
        stroke="#4f4e62"
        strokeWidth="10"
      />
      <g filter="url(#filter0_i_0_5)">
        <path
          d="M77.5 175C77.5 169.753 81.7533 165.5 87 165.5H207C212.247 165.5 216.5 169.753 216.5 175V251.754C216.5 254.284 215.491 256.709 213.696 258.492L199.36 272.739C197.58 274.507 195.173 275.5 192.664 275.5H87C81.7533 275.5 77.5 271.247 77.5 266V175Z"
          stroke="black"
          strokeOpacity="0.25"
        />
      </g>
      <g filter="url(#filter1_i_0_5)">
        <path
          d="M87 177C87 171.477 91.4772 167 97 167H189C194.523 167 199 171.477 199 177V230.042C199 232.681 197.957 235.213 196.098 237.086L187.207 246.044C185.329 247.936 182.774 249 180.109 249H97C91.4771 249 87 244.523 87 239V177Z"
          stroke="black"
          strokeOpacity="0.25"
        />
      </g>
      {!backSide ? (
        <>
          <path
            d="M169.653 203.624C169.652 212.898 169.656 218.949 169.665 221.775C169.669 222.856 169.671 223.871 169.671 224.821C169.67 227.033 168.971 229.022 167.573 230.789C166.649 231.957 165.678 232.903 164.659 233.626C162.755 234.977 160.623 235.647 158.262 235.636C141.354 235.56 122.948 235.547 103.044 235.598C102.926 235.598 102.464 235.565 101.66 235.499C99.6517 235.334 98.1167 234.569 96.3571 233.31C94.614 232.062 93.2938 230.437 92.3964 228.435C91.7562 227.012 91.6767 225.471 91.6866 223.696C91.7025 220.308 91.7111 213.613 91.7125 203.61C91.7151 193.606 91.7085 186.91 91.6926 183.521C91.6846 181.749 91.7642 180.208 92.4044 178.785C93.3031 176.782 94.624 175.157 96.3671 173.91C98.1287 172.652 99.6636 171.888 101.672 171.723C102.476 171.658 102.938 171.626 103.056 171.626C122.96 171.684 141.366 171.678 158.274 171.608C160.635 171.597 162.767 172.268 164.669 173.62C165.688 174.343 166.66 175.289 167.584 176.457C168.979 178.225 169.678 180.215 169.679 182.427C169.679 183.376 169.677 184.392 169.673 185.473C169.662 188.299 169.656 194.35 169.653 203.624ZM167.373 182.962C167.373 180.544 166.423 178.225 164.734 176.516C163.045 174.806 160.753 173.845 158.364 173.845H103.04C100.65 173.845 98.359 174.806 96.6695 176.516C94.98 178.225 94.0308 180.544 94.0308 182.962V224.225C94.0308 226.643 94.98 228.962 96.6695 230.672C98.359 232.382 100.65 233.342 103.04 233.342H158.364C160.753 233.342 163.045 232.382 164.734 230.672C166.423 228.962 167.373 226.643 167.373 224.225V182.962Z"
            fill="white"
          />
          <path
            d="M167.373 182.963V224.227C167.373 226.644 166.424 228.964 164.734 230.673C163.045 232.383 160.753 233.343 158.364 233.343H103.04C100.651 233.343 98.3592 232.383 96.6697 230.673C94.9802 228.964 94.031 226.644 94.031 224.227V182.963C94.031 180.545 94.9802 178.227 96.6697 176.517C98.3592 174.807 100.651 173.847 103.04 173.847H158.364C160.753 173.847 163.045 174.807 164.734 176.517C166.424 178.227 167.373 180.545 167.373 182.963ZM163.099 184.634C163.096 182.53 162.266 180.513 160.793 179.027C159.319 177.541 157.324 176.709 155.244 176.713L105.97 176.8C103.89 176.803 101.897 177.643 100.429 179.134C98.9611 180.624 98.1384 182.644 98.142 184.749L98.2073 222.564C98.2109 224.668 99.0406 226.685 100.514 228.171C101.987 229.657 103.983 230.489 106.063 230.486L155.336 230.399C157.416 230.395 159.409 229.555 160.877 228.065C162.346 226.574 163.168 224.554 163.165 222.449L163.099 184.634Z"
            fill="#FFDE59"
          />
          <path
            d="M163.164 222.447C163.168 224.552 162.345 226.572 160.877 228.063C159.409 229.553 157.416 230.393 155.336 230.397L106.063 230.484C103.983 230.487 101.987 229.655 100.514 228.169C99.0404 226.683 98.2107 224.666 98.2071 222.562L98.1418 184.747C98.1382 182.643 98.9609 180.623 100.429 179.132C101.897 177.641 103.89 176.801 105.97 176.798L155.244 176.711C157.323 176.707 159.319 177.54 160.793 179.025C162.266 180.511 163.095 182.528 163.099 184.632L163.164 222.447ZM146.52 188.277C147.257 187.802 147.852 187.857 148.87 187.867C153.733 187.911 157.308 187.965 160.708 187.867C160.747 187.866 160.783 187.85 160.81 187.822C160.837 187.794 160.852 187.757 160.851 187.718C160.844 187.333 160.846 186.311 160.855 184.654C160.864 182.824 160.151 181.351 158.716 180.235C158.089 179.749 157.474 179.431 156.87 179.279C155.791 179.01 154.608 178.881 153.319 178.893C147.233 178.951 140.15 178.973 132.073 178.959C131.97 178.959 131.871 179.001 131.799 179.074C131.726 179.148 131.685 179.248 131.685 179.352V193.919C131.685 193.964 131.702 194.006 131.733 194.038C131.764 194.07 131.806 194.088 131.85 194.088C134.167 194.131 136.398 194.138 138.543 194.108C139.117 194.1 139.537 193.987 139.962 193.645C141.215 192.635 142.788 191.309 144.68 189.668C145.302 189.13 145.915 188.666 146.52 188.277ZM114.679 188.231C116.779 189.933 119.004 191.745 121.356 193.668C121.736 193.977 122.282 194.124 122.847 194.13C124.842 194.155 126.942 194.132 129.148 194.064C129.233 194.061 129.313 194.026 129.372 193.964C129.431 193.903 129.464 193.821 129.464 193.736L129.44 179.307C129.44 179.221 129.406 179.138 129.346 179.075C129.286 179.013 129.204 178.977 129.118 178.973C127.97 178.932 127.158 178.91 126.68 178.909C121.133 178.889 115.108 178.903 108.605 178.951C106.884 178.966 105.794 179.013 105.334 179.092C103.903 179.342 102.729 179.967 101.813 180.969C100.837 182.038 100.515 183.301 100.467 184.861C100.434 185.903 100.419 186.816 100.423 187.599C100.424 187.676 100.454 187.75 100.508 187.804C100.561 187.858 100.634 187.889 100.709 187.889C108.55 187.873 112.846 187.868 113.598 187.875C113.963 187.877 114.381 187.985 114.679 188.231ZM114.902 191.346C114.176 190.722 113.542 190.136 112.607 190.134C108.718 190.129 104.76 190.14 100.733 190.167C100.662 190.168 100.595 190.197 100.545 190.248C100.495 190.299 100.467 190.368 100.467 190.44L100.495 202.197C100.495 202.421 100.606 202.533 100.829 202.533L120.121 202.486C120.305 202.486 120.398 202.393 120.399 202.207C120.417 200.333 120.419 198.405 120.407 196.424C120.405 195.973 120.18 195.572 119.733 195.221C117.155 193.191 115.545 191.9 114.902 191.346ZM141.342 202.474H160.642C160.689 202.474 160.734 202.456 160.767 202.422C160.801 202.388 160.819 202.343 160.819 202.295V190.442C160.819 190.402 160.812 190.363 160.797 190.326C160.782 190.29 160.76 190.256 160.732 190.228C160.704 190.2 160.671 190.178 160.635 190.163C160.599 190.148 160.56 190.14 160.521 190.14C155.867 190.157 151.862 190.164 148.506 190.163C148.114 190.163 147.73 190.241 147.428 190.434C146.462 191.05 145.446 192.078 144.603 192.742C143.228 193.823 142.194 194.694 141.501 195.356C141.162 195.679 140.989 195.976 140.982 196.247C140.969 196.908 140.948 198.841 140.92 202.044C140.918 202.331 141.058 202.474 141.342 202.474ZM138.771 196.674C138.771 196.573 138.732 196.477 138.662 196.406C138.592 196.335 138.497 196.295 138.398 196.295H122.962C122.863 196.295 122.768 196.335 122.698 196.406C122.628 196.477 122.589 196.573 122.589 196.674V210.422C122.589 210.522 122.628 210.619 122.698 210.69C122.768 210.76 122.863 210.8 122.962 210.8H138.398C138.497 210.8 138.592 210.76 138.662 210.69C138.732 210.619 138.771 210.522 138.771 210.422V196.674ZM114.93 215.848C115.574 215.292 117.187 213.992 119.769 211.949C120.216 211.595 120.441 211.192 120.443 210.74C120.45 208.752 120.443 206.818 120.421 204.937C120.419 204.751 120.325 204.658 120.141 204.658H100.781C100.558 204.658 100.447 204.77 100.447 204.996V216.792C100.447 216.864 100.475 216.934 100.526 216.985C100.576 217.036 100.644 217.065 100.715 217.066C104.756 217.083 108.727 217.085 112.629 217.07C113.568 217.066 114.202 216.476 114.93 215.848ZM160.668 217.048C160.708 217.047 160.747 217.031 160.775 217.002C160.803 216.974 160.819 216.935 160.819 216.895V204.867C160.819 204.809 160.797 204.754 160.756 204.713C160.715 204.672 160.66 204.649 160.603 204.649H141.282C141.198 204.649 141.116 204.684 141.056 204.744C140.996 204.805 140.962 204.887 140.962 204.973V211.024C140.962 211.143 140.988 211.261 141.037 211.369C141.087 211.477 141.159 211.573 141.248 211.649C143.376 213.467 145.392 215.145 147.295 216.683C147.553 216.891 147.953 217.076 148.249 217.078C152.518 217.117 156.657 217.107 160.668 217.048ZM114.721 218.953C114.423 219.199 114.005 219.307 113.637 219.312C112.884 219.319 108.58 219.322 100.723 219.319C100.647 219.319 100.575 219.35 100.521 219.405C100.467 219.46 100.437 219.534 100.437 219.611C100.434 220.396 100.45 221.31 100.485 222.354C100.536 223.917 100.861 225.183 101.841 226.251C102.761 227.253 103.938 227.878 105.372 228.126C105.833 228.206 106.926 228.25 108.651 228.261C115.167 228.299 121.205 228.303 126.764 228.273C127.241 228.27 128.055 228.248 129.206 228.205C129.292 228.202 129.374 228.165 129.434 228.102C129.494 228.039 129.528 227.956 129.528 227.869V213.41C129.528 213.324 129.494 213.242 129.435 213.181C129.376 213.12 129.295 213.084 129.21 213.082C127 213.018 124.896 212.999 122.897 213.026C122.33 213.034 121.783 213.181 121.402 213.492C119.049 215.423 116.822 217.243 114.721 218.953ZM147.784 219.329C147.152 219.333 146.692 219.171 146.223 218.768C144.15 216.988 142.065 215.252 139.966 213.561C139.545 213.222 139.21 213.05 138.962 213.048C136.501 213.013 134.166 213.025 131.957 213.084C131.885 213.086 131.817 213.117 131.767 213.169C131.717 213.221 131.689 213.291 131.689 213.364V227.881C131.689 228.083 131.788 228.185 131.987 228.185C134.883 228.187 140.414 228.182 148.579 228.171C148.591 228.171 149.645 228.187 151.741 228.221C152.825 228.238 153.973 228.223 155.187 228.176C157.18 228.099 158.752 227.289 159.903 225.748C160.482 224.971 160.789 224.004 160.823 222.847C160.852 221.823 160.866 220.755 160.865 219.642C160.865 219.556 160.832 219.474 160.772 219.414C160.713 219.353 160.631 219.318 160.547 219.318C157.063 219.288 152.809 219.292 147.784 219.329Z"
            fill="white"
          />

          <path
            d="M146.52 188.277C145.915 188.666 145.302 189.129 144.68 189.667C142.787 191.309 141.215 192.635 139.962 193.645C139.537 193.987 139.117 194.099 138.543 194.108C136.398 194.137 134.167 194.13 131.85 194.087C131.806 194.087 131.764 194.069 131.733 194.037C131.702 194.006 131.685 193.963 131.685 193.918V179.351C131.685 179.247 131.726 179.147 131.798 179.074C131.871 179 131.97 178.959 132.073 178.959C140.15 178.972 147.232 178.95 153.319 178.892C154.608 178.88 155.791 179.009 156.87 179.279C157.474 179.43 158.089 179.749 158.716 180.234C160.151 181.35 160.864 182.823 160.855 184.653C160.846 186.311 160.844 187.332 160.851 187.717C160.851 187.756 160.837 187.793 160.81 187.821C160.783 187.849 160.746 187.865 160.708 187.866C157.308 187.965 153.733 187.91 148.87 187.866C147.852 187.856 147.257 187.802 146.52 188.277Z"
            fill="#FFDE59"
          />
          <path
            d="M114.679 188.231C114.381 187.986 113.963 187.877 113.598 187.875C112.846 187.869 108.55 187.873 100.71 187.889C100.634 187.889 100.561 187.859 100.508 187.805C100.454 187.75 100.424 187.677 100.423 187.6C100.419 186.816 100.434 185.903 100.467 184.861C100.515 183.302 100.837 182.038 101.813 180.97C102.729 179.968 103.903 179.342 105.334 179.093C105.794 179.013 106.884 178.967 108.605 178.952C115.108 178.903 121.133 178.889 126.681 178.91C127.158 178.911 127.97 178.932 129.118 178.974C129.204 178.977 129.286 179.014 129.346 179.076C129.406 179.138 129.44 179.221 129.44 179.308L129.464 193.736C129.464 193.822 129.431 193.904 129.372 193.965C129.313 194.026 129.233 194.062 129.148 194.064C126.942 194.133 124.842 194.155 122.847 194.131C122.282 194.125 121.736 193.978 121.356 193.668C119.004 191.746 116.779 189.934 114.679 188.231Z"
            fill="#FFDE59"
          />
          <path
            d="M114.902 191.347C115.545 191.901 117.155 193.193 119.733 195.222C120.18 195.574 120.405 195.975 120.407 196.425C120.419 198.407 120.417 200.334 120.399 202.208C120.398 202.394 120.305 202.488 120.121 202.488L100.829 202.534C100.606 202.534 100.495 202.422 100.495 202.198L100.467 190.441C100.467 190.37 100.495 190.301 100.545 190.249C100.594 190.198 100.662 190.169 100.733 190.168C104.76 190.141 108.718 190.13 112.607 190.136C113.542 190.138 114.176 190.723 114.902 191.347Z"
            fill="#FFDE59"
          />
          <path
            d="M160.642 202.474H141.342C141.058 202.474 140.918 202.331 140.92 202.044C140.948 198.841 140.969 196.908 140.982 196.247C140.989 195.976 141.162 195.679 141.501 195.356C142.194 194.694 143.228 193.823 144.603 192.742C145.446 192.078 146.462 191.05 147.428 190.434C147.73 190.241 148.114 190.162 148.506 190.162C151.862 190.164 155.867 190.156 160.521 190.14C160.56 190.14 160.599 190.148 160.635 190.163C160.671 190.178 160.704 190.2 160.732 190.228C160.76 190.256 160.782 190.289 160.796 190.326C160.812 190.363 160.819 190.402 160.819 190.442V202.295C160.819 202.343 160.801 202.388 160.767 202.422C160.734 202.455 160.689 202.474 160.642 202.474Z"
            fill="#FFDE59"
          />
          <path
            d="M138.771 196.674V210.422C138.771 210.523 138.732 210.619 138.662 210.69C138.591 210.761 138.497 210.801 138.398 210.801H122.962C122.863 210.801 122.768 210.761 122.698 210.69C122.628 210.619 122.589 210.523 122.589 210.422V196.674C122.589 196.573 122.628 196.477 122.698 196.406C122.768 196.335 122.863 196.296 122.962 196.296H138.398C138.497 196.296 138.591 196.335 138.662 196.406C138.732 196.477 138.771 196.573 138.771 196.674Z"
            fill="#FFDE59"
          />
          <path
            d="M114.93 215.85C114.202 216.478 113.568 217.067 112.629 217.071C108.727 217.086 104.756 217.085 100.715 217.067C100.644 217.067 100.576 217.038 100.525 216.987C100.475 216.935 100.447 216.866 100.447 216.794V204.997C100.447 204.772 100.558 204.659 100.781 204.659H120.141C120.325 204.659 120.419 204.752 120.421 204.939C120.442 206.819 120.45 208.754 120.443 210.741C120.44 211.193 120.216 211.597 119.769 211.951C117.187 213.994 115.574 215.293 114.93 215.85Z"
            fill="#FFDE59"
          />
          <path
            d="M160.668 217.048C156.657 217.107 152.518 217.117 148.249 217.078C147.953 217.076 147.553 216.891 147.295 216.684C145.391 215.146 143.376 213.467 141.248 211.65C141.159 211.573 141.087 211.477 141.037 211.369C140.988 211.261 140.962 211.143 140.962 211.024V204.974C140.962 204.888 140.996 204.805 141.056 204.745C141.116 204.684 141.198 204.65 141.282 204.65H160.603C160.66 204.65 160.715 204.673 160.756 204.713C160.796 204.754 160.819 204.809 160.819 204.867V216.895C160.819 216.935 160.803 216.974 160.775 217.003C160.747 217.031 160.708 217.048 160.668 217.048Z"
            fill="#FFDE59"
          />
          <path
            d="M113.637 219.311C114.005 219.307 114.423 219.198 114.721 218.953C116.822 217.242 119.049 215.422 121.401 213.492C121.783 213.18 122.33 213.033 122.897 213.025C124.896 212.998 127 213.017 129.209 213.081C129.295 213.084 129.375 213.119 129.435 213.18C129.494 213.242 129.528 213.324 129.528 213.409V227.868C129.528 227.955 129.494 228.039 129.434 228.102C129.374 228.164 129.292 228.201 129.205 228.204C128.055 228.247 127.241 228.27 126.764 228.273C121.205 228.302 115.167 228.298 108.651 228.26C106.926 228.25 105.833 228.205 105.372 228.125C103.938 227.877 102.761 227.253 101.841 226.25C100.86 225.182 100.536 223.916 100.485 222.353C100.45 221.309 100.434 220.395 100.437 219.611C100.437 219.533 100.467 219.459 100.521 219.404C100.575 219.35 100.647 219.319 100.723 219.319C108.58 219.322 112.884 219.319 113.637 219.311Z"
            fill="#FFDE59"
          />
          <path
            d="M146.223 218.768C146.692 219.171 147.152 219.333 147.784 219.33C152.809 219.292 157.064 219.288 160.547 219.318C160.632 219.318 160.713 219.353 160.772 219.414C160.832 219.474 160.865 219.556 160.865 219.642C160.866 220.755 160.852 221.823 160.823 222.847C160.789 224.004 160.482 224.971 159.903 225.748C158.752 227.289 157.18 228.099 155.187 228.177C153.974 228.224 152.825 228.238 151.741 228.221C149.645 228.187 148.591 228.171 148.58 228.171C140.414 228.183 134.883 228.187 131.987 228.185C131.788 228.185 131.689 228.084 131.689 227.881V213.364C131.689 213.291 131.717 213.221 131.767 213.169C131.817 213.117 131.886 213.086 131.957 213.084C134.166 213.025 136.501 213.013 138.962 213.048C139.21 213.051 139.545 213.222 139.966 213.561C142.065 215.253 144.15 216.988 146.223 218.768Z"
            fill="#FFDE59"
          />
        </>
      ) : (
        <>
          <g>
            <text
              x="-140"
              y="185"
              dominantBaseline="middle"
              textAnchor="start"
              fontSize="14"
              fill="currentColor"
              transform="scale(-1,1)"
              letterSpacing="1.5"
              fontWeight={400}
            >
              {p1}
            </text>
            <text
              x="-140"
              y="200"
              dominantBaseline="middle"
              textAnchor="start"
              fontSize="14"
              fill="currentColor"
              transform="scale(-1,1)"
              letterSpacing="1.5"
              fontWeight={400}
            >
              {p2}
            </text>
            <text
              x="-140"
              y="215"
              dominantBaseline="middle"
              textAnchor="start"
              fontSize="14"
              fill="currentColor"
              transform="scale(-1,1)"
              letterSpacing="1.5"
              fontWeight={400}
            >
              {p3}
            </text>
            <text
              x="-140"
              y="230"
              dominantBaseline="middle"
              textAnchor="start"
              fontSize="14"
              fill="currentColor"
              transform="scale(-1,1)"
              letterSpacing="2"
              fontWeight={400}
            >
              ****
            </text>
          </g>
          <g transform="translate(320,140) scale(-1,1) rotate(90)">
            {barcode && (
              <image href={barcode} height="32" width="308" x="0" y="0" />
            )}
            <text
              fill="currentColor"
              {...(barcode ? { x: '20', y: '60' } : { x: '0', y: '10' })}
              fontSize="24"
              letterSpacing="2"
              fontWeight={400}
            >
              {p1}
              {p2}
              {p3}****
            </text>
          </g>
        </>
      )}
    </svg>
  );
}
