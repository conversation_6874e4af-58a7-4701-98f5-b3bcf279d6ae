import { useContext, useEffect, useState } from 'react';
import SimSpaces from './SimSpaces';
import { BrandingContext } from 'src/contexts/BrandingContext';
import { getFiveCharactersFromIndex as getFiveChars } from 'src/helper';
import { twMerge } from 'tailwind-merge';
import './style.css';
import clsx from 'clsx';

const CardSide = ({
  isBack,
  serial,
  barcode
}: {
  isBack?: boolean;
  serial: string;
  barcode?: string;
}) => {
  const { branding } = useContext(BrandingContext);
  const serialLength = serial.length;
  const [p1, p2, p3] = [
    getFiveChars(serial, serialLength - 19),
    getFiveChars(serial, serialLength - 14),
    getFiveChars(serial, serialLength - 9)
  ];

  return (
    <div
      className={clsx(
        'absolute top-0 bottom-0 right-0 left-0',
        isBack ? 'side-back' : 'side-front'
      )}
    >
      <div className="bg-white max-w-[390px] w-[20vw] h-[26vw] max-h-[500px] relative rounded-[32px] overflow-hidden">
        <div
          className={
            isBack
              ? 'absolute top-[2%] left-1/2 -translate-x-1/2 w-[5vw] h-[5vw] rounded-lg overflow-hidden'
              : 'w-full h-full'
          }
        >
          <img
            src={branding?.logo}
            alt="logo"
            className="w-full h-full object-cover"
          />
        </div>
        <div
          className={twMerge(
            'absolute inset-0 text-gray-800',
            isBack && 'scale-x-[-1]'
          )}
        >
          <SimSpaces
            p1={p1}
            p2={p2}
            p3={p3}
            barcode={barcode}
            backSide={isBack}
          />
        </div>
      </div>
    </div>
  );
};

export default function SimCard({
  backSide,
  barcode,
  serialNumber = ''
}: {
  backSide?: boolean;
  barcode?: string;
  serialNumber?: string;
}) {
  const [isBackAnimation, setIsBackAnimation] = useState(false);

  useEffect(() => {
    if (backSide) {
      setIsBackAnimation(true);
    }
  }, [backSide]);

  return (
    <div
      className={clsx(
        'card  w-[20vw] h-[26vw]',
        backSide && ' rotated',
        isBackAnimation && !backSide && ' rotated-back'
      )}
    >
      <div className="card-content text-center relative transition-transform duration-1000 text-white font-bold w-full h-full">
        <div
          className={twMerge('bg-black/10 rounded-[32px] absolute -inset-5')}
        />

        <CardSide serial={serialNumber} />
        <CardSide isBack serial={serialNumber} barcode={barcode} />
      </div>
    </div>
  );
}
