import React, { useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';
import Card from 'src/components/Card';
import Typography, {
  TypographyColor,
  TypographyShade,
  TypographySize
} from 'src/components/Typography';
import PlanCard from './PlanCard';
import {
  convertToGb,
  convertToMins,
  currencyRounded,
  getDateDurationFromNow,
  getInitials
} from 'src/helper';
import { useAsync } from 'src/api/useApi';
import { fetchSubscriber } from 'src/api/subscriber';
import { DateTime } from 'luxon';
import { SubscriberPlan, SubscriberStatus } from 'src/types/subscribers';
import usePlansOffering from 'src/hooks/usePlansOffering';
import {
  selectorPlanComponentOfferingById,
  usePlansOfferingStore
} from 'src/store/plansOfferings';
import InfoBlock from 'src/components/InfoBlock';
import InfoCard from 'src/components/InfoCard';
import Button, { ButtonColor } from 'src/components/Button';
import PlanAssignmentModal from './PlanAssignmentModal';
import Status, { StatusColor } from 'src/components/Status';

const PLACEHOLDER = 'Unknown';
const statusColor: Record<string, StatusColor> = {
  [SubscriberStatus.Active]: StatusColor.Success,
  [SubscriberStatus.Pending]: StatusColor.Default,
  [SubscriberStatus.Inactive]: StatusColor.Error,
  [SubscriberStatus.NeedsSim]: StatusColor.Warning
};

const SubscriberInfo: React.FC = () => {
  const { subscriberId = '' } = useParams();
  const { isLoading: isPlansLoading } = usePlansOffering();
  const {
    data: subscriber,
    isLoading: isSubscriberLoading,
    run: runFetchSubscriber
  } = useAsync(() => fetchSubscriber(subscriberId), {
    fetchOnMount: true
  });

  const [isPlanAssignmentModalOpen, setIsPlanAssignmentModalOpen] =
    useState(false);

  const {
    name,
    status = 'Pending',
    total_profit,
    email,
    start_date,
    address,
    latest_number_display,
    latest_plan = {},
    data_usage,
    voice_usage,
    sms_usage,
    date_of_birth,
    latest_sim_type,
    perk_points,
    total_points_earned,
    perk_redemption_amount,
    send_marketing
  } = subscriber || {};
  const emptyUsage = !(data_usage || voice_usage || sms_usage);

  const {
    name: latestPlanName,
    price: latestPlanPrice,
    data_component_offering,
    voice_component_offering,
    sms_component_offering
  } = latest_plan as SubscriberPlan;

  const plansById = usePlansOfferingStore(selectorPlanComponentOfferingById);
  const simType = latest_sim_type;

  const { dataDescription, smsDescription, voiceDescription } = useMemo(() => {
    return {
      dataDescription:
        data_component_offering &&
        plansById[data_component_offering]?.plan_component.max_limit,
      smsDescription:
        sms_component_offering &&
        plansById[sms_component_offering]?.plan_component.max_limit,
      voiceDescription:
        voice_component_offering &&
        plansById[voice_component_offering]?.plan_component.max_limit
    };
  }, [plansById, latest_plan]);

  const addressFormatted = useMemo(() => {
    return (
      address
        ?.split('\n')
        .filter((el) => Boolean(el))
        .join(', ') || PLACEHOLDER
    );
  }, [address]);

  const firstLineData = [
    {
      title: 'Total Profit',
      value: currencyRounded(Number(total_profit) || 0)
    },
    {
      title: 'total duration',
      value: getDateDurationFromNow(start_date || '')
    },
    { title: 'ACTIVE SUBSCRIPTIONS', value: '1' }
  ];
  const usageData = [
    {
      title: 'Monthly Data',
      value: convertToGb(data_usage || 0)
    },
    {
      title: 'Monthly minutes',
      value: convertToMins(voice_usage)
    },
    { title: 'Monthly Texts', value: sms_usage?.toString() || '0' }
  ];
  const subscriptionData = [
    {
      title: 'Number',
      value: latest_number_display || (
        <div className="flex flex-col gap-2">
          <span>Not assigned</span>
          {status === SubscriberStatus.NeedsSim && (
            <Button
              onClick={() => setIsPlanAssignmentModalOpen(true)}
              color={ButtonColor.Pink}
            >
              Assign
            </Button>
          )}
        </div>
      )
    },
    {
      title: 'Started',
      value: start_date ? DateTime.fromISO(start_date).toLocaleString() : ''
    }
  ];

  const perksData = [
    { title: 'Current balance', value: perk_points || 0 },
    { title: 'Lifetime points', value: total_points_earned || 0 },
    { title: 'Redemptions', value: perk_redemption_amount || 0 }
  ];

  const getSubscriber = () => {
    runFetchSubscriber();
  };

  return (
    <Card classes="space-y-8" isLoading={isPlansLoading || isSubscriberLoading}>
      <div className="flex justify-between">
        <Typography size={TypographySize.Title}>{name}</Typography>
        <div className="flex gap-14 items-center">
          <Status
            status="Marketing"
            color={send_marketing ? StatusColor.Success : StatusColor.Error}
          />

          <span className="h-full w-[1px] bg-gradient-divider" />

          <Status
            status={status}
            color={statusColor[status] || StatusColor.Default}
          />
        </div>
      </div>
      <InfoCard classes="grid grid-cols-4 gap-5 h-[280px]" gradientSize="big">
        <div className="h-full bg-gradient-subscriber flex justify-center items-center text-8xl rounded-2xl uppercase text-black/60">
          {getInitials(name, email)}
        </div>
        {firstLineData.map((data) => (
          <InfoBlock
            {...data}
            key={data.title}
            titleColor={TypographyColor.Success}
            bold
          />
        ))}
      </InfoCard>

      <InfoCard classes="grid grid-cols-4 gap-5 h-[280px]" gradientSize="small">
        <div className="h-full flex justify-center items-center">
          <Typography size={TypographySize.Title}>Subscriptions</Typography>
        </div>
        <div className="col-span-3 grid grid-cols-7 gap-5 h-full">
          <PlanCard
            title={latestPlanName}
            price={latestPlanPrice}
            data={dataDescription || 0}
            sms={smsDescription || 0}
            voice={voiceDescription || 0}
            simType={simType === 'esim' ? 'eSIM' : 'SIM'}
            terminated={status === SubscriberStatus.Inactive}
          />

          {subscriptionData.map((data) => (
            <div className="col-span-2" key={data.title}>
              <InfoBlock {...data} fontSize={TypographySize.Title} />
            </div>
          ))}
        </div>
      </InfoCard>

      {!emptyUsage && (
        <InfoCard classes="grid grid-cols-4 gap-5 h-[280px]">
          <div className="h-full flex justify-center items-center">
            <Typography size={TypographySize.Title}>Avg. usage</Typography>
          </div>
          {usageData.map((data) => (
            <InfoBlock
              {...data}
              key={data.title}
              fontSize={TypographySize.CardTitle}
              bold
            />
          ))}
        </InfoCard>
      )}

      <InfoCard classes="grid grid-cols-4 gap-5 h-[280px]">
        <div className="h-full flex justify-center items-center">
          <Typography size={TypographySize.Title}>Perks</Typography>
        </div>
        {perksData.map((data) => (
          <InfoBlock
            {...data}
            key={data.title}
            fontSize={TypographySize.CardTitle}
            bold
          />
        ))}
      </InfoCard>

      <div>
        <div className="mt-20">
          <Typography size={TypographySize.Title} as="p">
            Subscriber details
          </Typography>
        </div>
      </div>

      <div className="grid grid-cols-3 gap-8">
        <InfoCard size="small">
          <Typography shade={TypographyShade.Light}>Name</Typography>
          <Typography>{name}</Typography>
        </InfoCard>
        <InfoCard size="small">
          <Typography shade={TypographyShade.Light}>Date of Birth</Typography>
          <Typography>
            {date_of_birth
              ? DateTime.fromISO(date_of_birth).toLocaleString()
              : PLACEHOLDER}
          </Typography>
        </InfoCard>
        <InfoCard size="small">
          <Typography shade={TypographyShade.Light}>Email</Typography>
          <Typography wrap>{email}</Typography>
        </InfoCard>
        <InfoCard size="small">
          <Typography shade={TypographyShade.Light}>Country</Typography>
          <Typography>United Kingdom</Typography>
        </InfoCard>

        <InfoCard classes="col-span-2" size="small">
          <Typography shade={TypographyShade.Light}>Address</Typography>
          <Typography>{addressFormatted}</Typography>
        </InfoCard>
      </div>

      {isPlanAssignmentModalOpen && subscriber && (
        <PlanAssignmentModal
          isOpen
          title="Assign SIM (Physical)"
          onClose={() => setIsPlanAssignmentModalOpen(false)}
          onSubmit={getSubscriber}
          subscriber={subscriber}
        />
      )}
    </Card>
  );
};

export default SubscriberInfo;
