import { currencyRounded } from 'src/helper';
import Chip from './Chip';
import Typography, {
  TypographyShade,
  TypographySize
} from 'src/components/Typography';

export const getDataText = (limit?: number) => {
  return limit === 0 ? 'Unlimited data' : `${limit || 0}GB data`;
};

export const getSmsText = (limit?: number) => {
  return limit === 0 ? 'Unlimited SMS' : `${limit || 0} SMS`;
};

export const getVoiceText = (limit?: number) => {
  return limit === 0 ? 'Unlimited calls' : `${limit || 0} mins`;
};

type Props = {
  title: string;
  price: string | null;
  data: number;
  sms: number;
  voice: number;
  simType: 'SIM' | 'eSIM';
  terminated?: boolean;
};

const PlanCard = (props: Props) => {
  const { title, price, data, sms, voice, simType, terminated } = props;

  return (
    <div className="col-span-3 bg-gradient-to-r from-[#2E215C] to-[#5A7AB2] rounded-3xl flex flex-col size-full font-bold py-6 px-4 relative">
      {terminated && (
        <div className="absolute inset-0 bg-black/80 text-white text-2xl uppercase flex justify-center items-center rounded-3xl font-medium">
          Terminated
        </div>
      )}
      <div className="grow flex justify-center flex-col">
        <Typography
          size={TypographySize.Title}
          shade={TypographyShade.Light}
          bold
        >
          {simType}
        </Typography>
        <div className="flex justify-between my-2 items-center">
          <Typography size={TypographySize.Title} uppercase bold truncate>
            {title}
          </Typography>
          <Typography size={TypographySize.Title} bold>
            {currencyRounded(price ? parseInt(price) : 0)}
          </Typography>
        </div>
      </div>
      <div className="flex flex-wrap gap-2">
        <Chip>{getDataText(data)}</Chip>
        <Chip>{getSmsText(sms)}</Chip>
        <Chip>{getVoiceText(voice)}</Chip>
        <Chip>Roam Like At Home</Chip>
        <Chip>5G</Chip>
      </div>
    </div>
  );
};

export default PlanCard;
