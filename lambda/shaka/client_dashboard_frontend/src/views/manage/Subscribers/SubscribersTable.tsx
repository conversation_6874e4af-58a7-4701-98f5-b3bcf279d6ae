import { createColumnHel<PERSON>, Row, SortingState } from '@tanstack/react-table';
import { currencyFormat, getDateDurationFromNow } from 'src/helper';
import { SUBSCRIBER_COLUMNS } from './constants';
import { Subscriber, SubscriberStatus } from 'src/types/subscribers';
import clsx from 'clsx';
import { useNavigate, useSearchParams } from 'react-router-dom';
import ArrowRightSquareIcon from 'src/icons/ArrowRightSquare';
import TableAsync from 'src/components/TableAsync';
import { useCallback, useEffect, useState } from 'react';
import { getSubscriberInfoRoute } from 'src/config/navigation';
import { fetchSubscribersPaged } from 'src/api/subscriber';
import { useAsync } from 'src/api/useApi';
import {
  selectorSubscribers,
  selectorSubscribersCount,
  selectorSubscribersOrdering,
  setSubscribers,
  setSubscribersCount,
  setSubscribersOrdering,
  useSubscribersStore
} from 'src/store/subscribers';

const ROWS_PER_PAGE = 20;
const FIRST_REQUEST_PAGE_COUNT = 10;
const REQUEST_PAGE_COUNT = 5;

const columnsWithSorting = ['start_date', 'revenue_generated'];
const orderingFields: { [key: string]: string } = {
  start_date: 'start_date',
  revenue_generated: '_total_revenue'
};

const style = {
  [SubscriberStatus.Active]: { text: 'text-success', bg: 'bg-success' },
  [SubscriberStatus.Pending]: { text: 'text-gray-100', bg: 'bg-gray-100' },
  [SubscriberStatus.Inactive]: { text: 'text-error', bg: 'bg-error' },
  [SubscriberStatus.Warning]: { text: 'text-warning', bg: 'bg-warning' },
  [SubscriberStatus.NeedsSim]: { text: 'text-warning', bg: 'bg-warning' }
};

const columnHelper = createColumnHelper<Subscriber>();
const columns = [
  'name',
  'revenue_generated',
  'status',
  'group',
  'start_date',
  'action'
].map((key) =>
  columnHelper.accessor(key as keyof Subscriber, {
    cell: (info) => {
      const status = info.getValue() as keyof typeof style;
      const classes = style[status] || style['Pending'];

      switch (key) {
        case 'start_date':
          return getDateDurationFromNow(info.getValue().toString());
        case 'revenue_generated':
          return currencyFormat(parseFloat(info.getValue() as string));
        case 'name':
          return <div className="text-left">{info.getValue()}</div>;
        case 'status':
          return (
            <div className="flex gap-4 items-center justify-center">
              <span
                className={clsx('block w-2 h-2 rounded-full', classes.bg)}
              />
              <span className={classes.text}>{status}</span>
            </div>
          );
        case 'action':
          return (
            <div className="flex justify-center">
              <ArrowRightSquareIcon className="w-7 h-7" />
            </div>
          );
        default:
          return info.getValue();
      }
    },
    enableSorting: columnsWithSorting.includes(key),
    header: () => SUBSCRIBER_COLUMNS[key as keyof Subscriber & 'info']
  })
);

export default function SubscribersTable() {
  const [searchParams, setSearchParams] = useSearchParams();
  const urlPage = searchParams.get('page')
    ? parseInt(searchParams.get('page')!) - 1
    : 0;

  const { run: runFetchSubscribers } = useAsync(fetchSubscribersPaged);

  const navigate = useNavigate();

  const subscriberStoreData = useSubscribersStore(selectorSubscribers);
  const subscribersCount = useSubscribersStore(selectorSubscribersCount);
  const prevSortData = useSubscribersStore(selectorSubscribersOrdering);

  const [pageIndex, setPageIndex] = useState(urlPage);
  const [nextPageIndex, setNextPageIndex] = useState(urlPage);
  const [sorting, setSorting] = useState<SortingState>(prevSortData);

  const pageCount = Math.ceil(subscribersCount / ROWS_PER_PAGE);

  const getNewRows = (newPage: number) => {
    setNextPageIndex(newPage);
    setSearchParams({ page: (newPage + 1).toString() });
  };

  const goToSubscriberInfo = useCallback(
    (row: Row<Subscriber>) => {
      navigate(getSubscriberInfoRoute(row?.original.id));
    },
    [navigate]
  );

  const fetchAndSetSubscribers = async ({
    requestPageIndex,
    requestPageSize,
    numberOfRequestedPages = REQUEST_PAGE_COUNT,
    nextIndex,
    clearSubscribers
  }: {
    requestPageIndex: number;
    requestPageSize: number;
    numberOfRequestedPages?: number;
    nextIndex?: number;
    ordering?: string;
    clearSubscribers?: boolean;
  }) => {
    const withSorting = sorting.length;

    // reverse sorting for date because we use the difference from now
    const isDescending =
      sorting[0]?.id === 'start_date' ? !sorting[0]?.desc : sorting[0]?.desc;

    const fetchedSubscribers = await runFetchSubscribers({
      page: requestPageIndex,
      pageSize: requestPageSize,
      ...(withSorting && {
        ordering: `${isDescending ? '-' : ''}${orderingFields[sorting[0].id]}`
      })
    });

    const fetchedSubscribersCount = fetchedSubscribers.count;
    const numberOfSubscriberPages = Math.ceil(
      fetchedSubscribersCount / ROWS_PER_PAGE
    );
    const rowStartIndex = (requestPageIndex - 1) * requestPageSize;
    const tablePageIndex = Math.ceil(rowStartIndex / ROWS_PER_PAGE);
    const pageLimitIndex = tablePageIndex + numberOfRequestedPages;

    const updatedSubscribers: typeof subscriberStoreData = {
      ...(!clearSubscribers && { ...subscriberStoreData })
    };

    for (let i = tablePageIndex; i < pageLimitIndex; i++) {
      const pagedSubscribers = fetchedSubscribers.results.slice(
        (i - tablePageIndex) * ROWS_PER_PAGE,
        (i - tablePageIndex) * ROWS_PER_PAGE + ROWS_PER_PAGE
      );

      if (i < numberOfSubscriberPages && pagedSubscribers.length) {
        updatedSubscribers[i] = pagedSubscribers;
      }
    }

    if (typeof nextIndex === 'number') {
      setPageIndex(nextIndex);
    }
    setSubscribers(updatedSubscribers);
    setSubscribersCount(fetchedSubscribersCount);
  };

  useEffect(() => {
    if (subscriberStoreData[nextPageIndex]?.length) {
      setPageIndex(nextPageIndex);

      const hasNextPage =
        nextPageIndex + 1 < pageCount
          ? Boolean(subscriberStoreData[nextPageIndex + 1]?.length)
          : true;

      // prefetch next page if we are at the end of the current list
      if (!hasNextPage) {
        const requestPageSize = REQUEST_PAGE_COUNT * ROWS_PER_PAGE;
        const requestPage = Math.ceil(
          (ROWS_PER_PAGE * (nextPageIndex + 2)) / requestPageSize
        );

        fetchAndSetSubscribers({
          requestPageIndex: requestPage,
          requestPageSize
        });

        return;
      }

      return;
    }

    const numberOfPages = nextPageIndex
      ? REQUEST_PAGE_COUNT
      : FIRST_REQUEST_PAGE_COUNT;
    const requestPageSize = numberOfPages * ROWS_PER_PAGE;
    const requestPageIndex = nextPageIndex
      ? Math.ceil((ROWS_PER_PAGE * (nextPageIndex + 1)) / requestPageSize)
      : 1;

    fetchAndSetSubscribers({
      requestPageIndex,
      requestPageSize,
      numberOfRequestedPages: numberOfPages,
      nextIndex: nextPageIndex
    });
  }, [nextPageIndex]);

  useEffect(() => {
    if (JSON.stringify(prevSortData) !== JSON.stringify(sorting)) {
      getNewRows(0);
      fetchAndSetSubscribers({
        nextIndex: 0,
        requestPageIndex: 1,
        requestPageSize: ROWS_PER_PAGE * FIRST_REQUEST_PAGE_COUNT,
        numberOfRequestedPages: FIRST_REQUEST_PAGE_COUNT,
        clearSubscribers: true
      });
      setSubscribersOrdering(sorting);
    }
  }, [sorting]);

  return (
    <TableAsync
      columns={columns}
      data={subscriberStoreData[pageIndex] || []}
      pageCount={pageCount}
      onPageChange={getNewRows}
      pageIndex={pageIndex}
      onRowClick={goToSubscriberInfo}
      sorting={sorting}
      onSortingChange={setSorting}
      withRowClick
      withPagination
      withSorting
    />
  );
}
