import { Revenue } from 'src/assets/revenue-chart';
import Chart, { ChartData } from './Chart';
import { useMemo } from 'react';
import Typography, {
  TypographyColor,
  TypographyShade,
  TypographySize
} from 'src/components/Typography';
import ArrowDownSmallIcon from 'src/icons/ArrowDownSmall';
import clsx from 'clsx';
import { currencyRounded } from 'src/helper';
import { formatPercent } from 'src/helper/numbers';

interface Props {
  chartData: {
    data: ChartData[];
  }[];
}

const ChartBlock = ({ chartData }: Props) => {
  const isRealChart = chartData.length && chartData[0].data.length > 1;

  const { profitCurrentMonth, profitDiffInPercents, isProfit } = useMemo(() => {
    if (!isRealChart) {
      return {};
    }

    const profitCurrentMonth =
      chartData[0].data[chartData[0].data.length - 1].value || 0;

    const profitLastMonth =
      chartData[0].data[chartData[0].data.length - 2].value || 0;

    const isProfit = profitCurrentMonth > profitLastMonth;

    const diff = profitCurrentMonth - profitLastMonth;

    const profitDiffInPercents = formatPercent(
      diff ? (diff * 100) / (profitLastMonth || 1) : 0
    );

    return { profitCurrentMonth, profitDiffInPercents, isProfit };
  }, [isRealChart, chartData]);

  if (!isRealChart) {
    return <Revenue className="h-full w-full" />;
  }

  return (
    <div className="flex flex-col h-full">
      <div className="flex justify-between items-center flex-wrap">
        <div className="inline-flex flex-col items-center">
          <Typography
            color={TypographyColor.Accent}
            size={TypographySize.Title}
          >
            Profit
          </Typography>
          <div className="rotate-180">
            <ArrowDownSmallIcon />
          </div>
        </div>
        <div className="rounded-full border border-blue-600 text-center py-1 px-2 text-blue-600">
          <Typography
            size={TypographySize.Caption}
            color={TypographyColor.Inherit}
          >
            Month
          </Typography>
        </div>

        <div className="w-full flex gap-12 items-center mt-4">
          <div className="text-6xl">
            <Typography size={TypographySize.Inherit} bold>
              {profitCurrentMonth && currencyRounded(profitCurrentMonth)}
            </Typography>
          </div>
          <div>
            <div
              className={clsx(
                isProfit ? 'bg-success' : 'bg-error',
                'text-black/70 flex gap-3 items-center justify-center px-2 rounded-md'
              )}
            >
              <div className={clsx(!isProfit && 'rotate-180')}>
                <ArrowDownSmallIcon />
              </div>
              <Typography color={TypographyColor.Inherit} bold>
                {isProfit ? '+' : ''}
                {profitDiffInPercents}
              </Typography>
            </div>
            <div className="text-center">
              <Typography
                size={TypographySize.Caption}
                shade={TypographyShade.Medium}
              >
                vs last month
              </Typography>
            </div>
          </div>
        </div>
      </div>
      <div className="grow">
        <Chart charts={chartData} />
      </div>
    </div>
  );
};

export default ChartBlock;
