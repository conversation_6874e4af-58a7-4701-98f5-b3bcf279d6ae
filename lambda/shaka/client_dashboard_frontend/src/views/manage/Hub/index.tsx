import Card from 'src/components/Card';
import Typography, {
  TypographyShade,
  TypographySize
} from 'src/components/Typography';
import { ROUTES } from 'src/config/navigation';
import { ActionsBg } from 'src/assets/actions-bg';
import { useContext, useMemo } from 'react';
import { SpnContext } from 'src/contexts/SpnContext';
import Link, { LinkColor } from 'src/components/Link';
import ActionLink from './ActionLink';
import { defaultMetrics } from './helpers';
import { useAsync } from 'src/api/useApi';
import { fetchMetrics } from 'src/api/metrics';
import MetricsCard from './MetricsCard';
import ChartBlock from './ChartBlock';
import CsvDownloader from 'react-csv-downloader';
import { CsvKey, SUBSCRIBER_COLUMNS_CSV } from '../Subscribers/constants';
import { DateTime } from 'luxon';
import { fetchSubscribers } from 'src/api/subscriber';

import Loader from 'src/components/Loader';
import { csvFormatSubscribers } from 'src/helper/csvFormat';
import useClientData from 'src/hooks/useClient';

export default function Dashboard() {
  const { run: runFetchSubscribers } = useAsync(fetchSubscribers);

  const { spn } = useContext(SpnContext);
  const { clientData } = useClientData();

  const { data, isLoading } = useAsync(() => fetchMetrics('monthly'), {
    fetchOnMount: true
  });

  const fileName = `${
    clientData.name
  }-profit-summary-${DateTime.now().toLocaleString()}.csv`;

  const metricsData = useMemo(() => {
    if (!data) return [];

    const metrics = data.metrics || defaultMetrics;

    return Object.keys(metrics).reduce(
      (result, key) => {
        if (key !== 'profit-per-period') {
          const values = Object.values(metrics[key as keyof typeof metrics]);

          result.push({
            key,
            data: values[values.length - 1]
          });
        }
        return result;
      },
      [] as { key: string; data: any }[]
    );
  }, [data]);

  const chartData = useMemo(() => {
    if (!data) {
      return [];
    }

    const profitData = Object.values(data.metrics['profit-per-period']);

    const dataFilteredByValues = profitData.filter(
      ({ values }) => values.find(({ primary }) => primary)?.value !== null
    );

    return [
      {
        data: dataFilteredByValues.map(({ date, values }) => ({
          date,
          value: values.find(({ primary }) => primary)?.value
        }))
      }
    ];
  }, [data]);

  const handleSummaryExport = async () => {
    const subscribers = await runFetchSubscribers();

    return csvFormatSubscribers(subscribers);
  };

  return (
    <>
      <div className="flex justify-between">
        <div className="flex flex-col">
          <Typography size={TypographySize.CardTitle}>Welcome back!</Typography>

          <Typography
            shade={TypographyShade.Medium}
            size={TypographySize.BodyM}
          >
            You currently have{' '}
            <Typography size={TypographySize.BodyM} bold>
              {clientData.active_subscriber_count || 0} active
            </Typography>{' '}
            subscribers on <span>{spn}</span> network.
          </Typography>
        </div>
      </div>

      <div className="grid grid-cols-2 xl:grid-cols-4 gap-8 mt-28 mb-20 desktop:gap-16">
        <div className="col-span-2 h-[350px] xl:h-auto w-full">
          {isLoading ? <Loader /> : <ChartBlock chartData={chartData} />}
        </div>
        <div className="flex flex-col items-baseline gap-6 bg-[url('/hub/insights.png')] bg-full text-white p-6 desktop:px-6 desktop:py-8">
          <div className="tracking-[4px] desktop:text-xl">Insights</div>
          <div className="grow text-2xl desktop:text-[26px] desktop:leading-9">
            Make sure you collect as much feedback from your customers as
            possible during the test phase to ensure smooth commercial launch
          </div>
          <Link to={ROUTES.PLANS} color={LinkColor.Light}>
            GO TO PLANS
          </Link>
        </div>
        <div className="relative rounded-3xl overflow-hidden pt-6 p-4 desktop:p-8">
          <ActionsBg className="absolute inset-0 z-[-1]" />
          <div className="tracking-[4px] desktop:text-xl mb-4">Actions</div>
          <div>
            <ul className="space-y-2">
              <li>
                <ActionLink to={ROUTES.PLANS_ADD}>Create a new plan</ActionLink>
              </li>
              <li>
                <ActionLink to={ROUTES.PLANS}>Edit existing plans</ActionLink>
              </li>
              <li>
                <ActionLink to={ROUTES.SUBSCRIBERS}>
                  View active subscribers
                </ActionLink>
              </li>
              <li>
                <CsvDownloader
                  filename={fileName}
                  columns={Object.keys(SUBSCRIBER_COLUMNS_CSV).map((key) => ({
                    id: key,
                    displayName: SUBSCRIBER_COLUMNS_CSV[key as CsvKey]
                  }))}
                  datas={handleSummaryExport}
                >
                  <ActionLink to={ROUTES.DASHBOARD}>
                    Export profit summary
                  </ActionLink>
                </CsvDownloader>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <Card title="Profit">
        <div className="grid grid-cols-2 xl:grid-cols-4 gap-10">
          {metricsData.map(({ data, key }) => (
            <MetricsCard data={data} key={key} />
          ))}
        </div>
      </Card>
    </>
  );
}
