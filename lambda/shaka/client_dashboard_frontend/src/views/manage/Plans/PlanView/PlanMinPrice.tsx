import { useMemo } from 'react';
import { useFormContext } from 'react-hook-form';
import Typography, { TypographyColor } from 'src/components/Typography';
import { currencyFormat } from 'src/helper';
import {
  selectorBundledPlanOfferingsById,
  selectorPlanComponentOfferingById,
  usePlansOfferingStore
} from 'src/store/plansOfferings';

export default function PlanMinPrice() {
  const planComponentOfferingById = usePlansOfferingStore(
    selectorPlanComponentOfferingById
  );
  const bundledPlansById = usePlansOfferingStore(
    selectorBundledPlanOfferingsById
  );
  const { watch } = useFormContext();
  const dataOfferingId = watch('dataOfferingId');
  const smsOfferingId = watch('smsOfferingId');
  const voiceOfferingId = watch('voiceOfferingId');
  const bundleId = watch('bundleId');

  const minPrice = useMemo(() => {
    if (bundleId) {
      return bundledPlansById[bundleId].price;
    }

    return [dataOfferingId, smsOfferingId, voiceOfferingId].reduce(
      (acc, offeringId) => {
        const offeringPrice = planComponentOfferingById[offeringId]?.price || 0;

        return acc + offeringPrice;
      },
      0
    );
  }, [dataOfferingId, smsOfferingId, voiceOfferingId, bundleId]);

  return <Typography color={TypographyColor.Accent}>(Recommended min price: {currencyFormat(minPrice)})</Typography>;
}
