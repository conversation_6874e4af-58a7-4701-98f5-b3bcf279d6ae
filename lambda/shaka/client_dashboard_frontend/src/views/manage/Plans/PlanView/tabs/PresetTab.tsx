import { useFormContext } from 'react-hook-form';
import {
  selectorBundledPlanOfferingsById,
  selectorBundledPlanOfferings,
  usePlansOfferingStore
} from 'src/store/plansOfferings';
import LabeledInputWrapper from 'src/components/form/WrapperWithLabel';
import { DimensionData } from '../DimensionData';
import { dimensions } from '../constants';
import { Inputs } from '../types';
import { inputClass } from 'src/components/sharedStyles';

interface Props {
  onBundledPlanChange: (newFormData: Partial<Inputs>) => void;
}

export default function PresetTab({ onBundledPlanChange }: Props) {
  const { register, watch, setValue } = useFormContext();
  const bundleId = watch('bundleId');

  const bundledPlanOfferings = usePlansOfferingStore(
    selectorBundledPlanOfferings
  );

  const bundledPlansById = usePlansOfferingStore(
    selectorBundledPlanOfferingsById
  );

  const handleBundleOptionChange = (event: any) => {
    const bundleId = event.target.value || null;

    if (!bundleId) {
      onBundledPlanChange({
        bundleId
      });
      return;
    }

    const bundle = bundledPlansById[bundleId];
    const newData = {
      voiceOfferingId: bundle.voice.id,
      smsOfferingId: bundle.sms.id,
      dataOfferingId: bundle.data.id,
      bundleId: bundle.id
    };

    onBundledPlanChange(newData);
  };

  const handleLimitChange = (limitField: string) => (value: number) => {
    setValue(limitField, value);
  };

  return (
    <div className="flex flex-col gap-1">
      <LabeledInputWrapper label="Preset name" className="mb-10">
        <div className="select w-2/5">
          <select
            {...register('bundleId')}
            onChange={handleBundleOptionChange}
            className={inputClass}
          >
            <option className="bg-grey-select" value="">
              --Please choose an option--
            </option>
            {bundledPlanOfferings.map(({ description, id }) => (
              <option className="bg-grey-select" key={description} value={id}>
                {description}
              </option>
            ))}
          </select>
        </div>
      </LabeledInputWrapper>

      {Boolean(bundleId) &&
        dimensions.map(({ label, type, extension, limitField }) => (
          <LabeledInputWrapper key={type} label={label} className="gap-1">
            <DimensionData
              type={type}
              offering={bundledPlansById[bundleId][type]}
              extension={extension}
              onChange={handleLimitChange(limitField)}
              disabled
            />
          </LabeledInputWrapper>
        ))}
    </div>
  );
}
