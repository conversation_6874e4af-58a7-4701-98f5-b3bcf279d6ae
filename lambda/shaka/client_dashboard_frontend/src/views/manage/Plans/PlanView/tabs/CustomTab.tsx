import LabeledInputWrapper from 'src/components/form/WrapperWithLabel';
import { dimensions } from '../constants';
import { useFormContext } from 'react-hook-form';
import {
  selectorDataOfferingsWithLimit,
  selectorOfferingsLimits,
  selectorPlanComponentOfferingById,
  selectorSmsOfferingsWithLimit,
  selectorVoiceOfferingsWithLimit,
  usePlansOfferingStore
} from 'src/store/plansOfferings';
import { DimensionData } from '../DimensionData';
import { Inputs } from '../types';
import { PlanDimension } from 'src/types/plans';
import { inputClass } from 'src/components/sharedStyles';
import { useEffect } from 'react';

interface Props {
  onDimensionChange: (
    formField: keyof Inputs,
    limitField: keyof Inputs
  ) => (event: React.ChangeEvent<HTMLSelectElement>) => void;
}

export default function CustomTab({ onDimensionChange }: Props) {
  const {
    register,
    watch,
    setValue,
    formState: { errors }
  } = useFormContext();
  const formData: Record<string, number> = {
    dataOfferingId: watch('dataOfferingId'),
    voiceOfferingId: watch('voiceOfferingId'),
    smsOfferingId: watch('smsOfferingId')
  };

  const customLimit = [
    watch('dataLimit'),
    watch('voiceLimit'),
    watch('smsLimit')
  ];

  const dataOfferingsOptions = usePlansOfferingStore(
    selectorDataOfferingsWithLimit
  );
  const dataLimit = usePlansOfferingStore(
    selectorOfferingsLimits(PlanDimension.Data)
  );
  const smsOfferingsOptions = usePlansOfferingStore(
    selectorSmsOfferingsWithLimit
  );
  const smsLimit = usePlansOfferingStore(
    selectorOfferingsLimits(PlanDimension.Sms)
  );
  const voiceOfferingsOptions = usePlansOfferingStore(
    selectorVoiceOfferingsWithLimit
  );
  const voiceLimit = usePlansOfferingStore(
    selectorOfferingsLimits(PlanDimension.Voice)
  );
  const planComponentOfferingById = usePlansOfferingStore(
    selectorPlanComponentOfferingById
  );

  const dimensionsOfferings = [
    dataOfferingsOptions,
    voiceOfferingsOptions,
    smsOfferingsOptions
  ];

  const dimensionsLimits = [dataLimit, voiceLimit, smsLimit];

  const handleLimitChange =
    (limitField: string, formField: string) =>
    (limit: number, dimensionId: number) => {
      setValue(formField, dimensionId);
      setValue(limitField, limit);
    };

  useEffect(() => {
    // temp fix for scrolling dropdowns into view when error
    if (Object.keys(errors).includes('price')) return;
    const firstError = Object.keys(errors)[0];
    //@ts-expect-error ref includes this method
    errors[firstError]?.ref?.scrollIntoView();
    //@ts-expect-error ref includes this method
    errors[firstError]?.ref?.focus();
  }, [errors]);

  return dimensions.map(
    ({ label, formField, type, extension, limitField }, i) => (
      <div className="flex flex-col gap-1 mb-10" key={formField}>
        <LabeledInputWrapper
          label={label}
          error={errors[formField]?.message?.toString() as string}
        >
          <div className="select w-2/5">
            <select
              {...register(formField)}
              onChange={onDimensionChange(formField, limitField)}
              className={inputClass}
            >
              <option value="" className="bg-grey-select">
                --Please choose an option--
              </option>
              {dimensionsOfferings[i].map(({ value, label }) => (
                <option key={value} value={value} className="bg-grey-select">
                  {label}
                </option>
              ))}
            </select>
          </div>
        </LabeledInputWrapper>

        <DimensionData
          type={type}
          customLimit={customLimit[i]}
          dimensionLimits={dimensionsLimits[i]}
          offering={planComponentOfferingById[formData[formField]]}
          onChange={handleLimitChange(limitField, formField)}
          extension={extension}
          defaultState={!planComponentOfferingById[formData[formField]]}
        />
      </div>
    )
  );
}
