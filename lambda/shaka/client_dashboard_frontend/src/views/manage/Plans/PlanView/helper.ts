import {
  Plan,
  PlanComponentOffering,
  PlanDimension,
  PlanStatus
} from 'src/types/plans';
import { Inputs } from './types';
import { DIMENSION_UNLIMITED } from 'src/config/constants';
import { DimensionsLimit } from 'src/store/plansOfferings';

const convertTimeString = (inputTimeString: string) => {
  const inputDate = new Date(inputTimeString);
  const outputDateString = inputDate.toISOString().slice(0, 16); // Extracts the format "YYYY-MM-DDTHH:mm"

  return outputDateString;
};

export const convertToFormData = (plan: Plan) => {
  const {
    name,
    price,
    status,
    bundle_id,
    data_component_offering,
    sms_component_offering,
    voice_component_offering,
    custom_voice_limit,
    custom_sms_limit,
    custom_data_limit,
    plan_key,
    implementation_datetime,
    points_per_month
  } = plan;

  return {
    name,
    status,
    price: Number(price),
    bundleId: bundle_id,
    dataOfferingId: data_component_offering,
    voiceOfferingId: voice_component_offering,
    smsOfferingId: sms_component_offering,
    dataLimit: custom_data_limit || undefined,
    voiceLimit: custom_voice_limit || undefined,
    smsLimit: custom_sms_limit || undefined,
    planKey: plan_key,
    implementationDate: convertTimeString(implementation_datetime),
    pointsPerMonth: points_per_month || 0,
    pointsAccrual: points_per_month ? 'yes' : 'no'
  };
};

export const convertToPayload = (data: Inputs & { provider: number }) => {
  const isBundle = Boolean(data.bundleId);

  const commonData = {
    name: data.name,
    status: data.status,
    price: data.price.toString(),
    custom_data_limit: data.dataLimit || null,
    custom_voice_limit: data.voiceLimit || null,
    custom_sms_limit: data.smsLimit || null,
    plan_key: data.planKey,
    implementation_datetime:
      data.status === PlanStatus.Inactive
        ? data.implementationDate
        : new Date().toISOString(),
    provider: data.provider,
    points_per_month:
      data.pointsAccrual === 'yes' ? Number(data.pointsPerMonth) : 0
  };

  if (isBundle) {
    return {
      ...commonData,
      bundle_id: Number(data.bundleId)
    };
  }

  return {
    ...commonData,
    data_component_offering: data.dataOfferingId,
    voice_component_offering: data.voiceOfferingId,
    sms_component_offering: data.smsOfferingId
  };
};

export const getPlanMinPrice = (
  data: PlanComponentOffering,
  sms: PlanComponentOffering,
  voice: PlanComponentOffering
): Record<PlanDimension | 'total', number> => {
  const dataPrice = data?.price || 0;
  const voicePrice = sms?.price || 0;
  const smsPrice = voice?.price || 0;

  return {
    data: dataPrice,
    sms: smsPrice,
    voice: voicePrice,
    total: dataPrice + voicePrice + smsPrice
  };
};

const validateForUnlimited = (value: number, maxValue: number) =>
  value === DIMENSION_UNLIMITED ? maxValue : value;

export const findItemWithClosestLimit = (
  dimensionsLimits: DimensionsLimit[],
  targetValue: number,
  maxValue: number
) => {
  return dimensionsLimits.reduce(
    (closest, current) => {
      const currentLimit = validateForUnlimited(current.limit, maxValue);
      const currentValue = validateForUnlimited(targetValue, maxValue);

      if (!closest) return current;
      const closestValue = validateForUnlimited(closest.limit, maxValue);

      const diffCurrent = Math.abs(currentLimit - currentValue);
      const diffClosest = Math.abs(closestValue - currentValue);

      return diffClosest < diffCurrent ? closest : current;
    },
    null as DimensionsLimit | null
  );
};
