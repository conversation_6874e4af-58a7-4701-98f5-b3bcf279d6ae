export type Plan = {
  name: string;
  price: number;
  data: string;
  'voice+sms': string;
  created_date: string;
  revenue_per_subscriber: number;
  total_plan_revenue: number;
  active_subscribers: number;
};

export const PLAN_COLUMNS: Record<string, string> = {
  name: 'Name',
  active_subscribers: 'Active Subscribers',
  revenue_per_subscriber: 'Revenue Per Subscriber',
  total_plan_revenue: 'Total Plan Revenue',
  created_date: 'Created'
};

export enum PlanGenerator {
  Preset = 'Preset',
  Custom = 'Custom'
}

export const planGeneratorOption = [
  {
    value: PlanGenerator.Preset,
    label: PlanGenerator.Preset
  },
  {
    value: PlanGenerator.Custom,
    label: PlanGenerator.Custom
  }
];

export const options = [
  {
    value: 'unlimited',
    label: 'Unlimited'
  },
  {
    value: 'limited',
    label: 'Limited'
  }
];
