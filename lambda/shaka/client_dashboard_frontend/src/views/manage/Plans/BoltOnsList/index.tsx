import Card from 'src/components/Card';
import InfoCard from 'src/components/InfoCard';
import Typography, { TypographyColor } from 'src/components/Typography';
import { currencyRounded } from 'src/helper';
import BoltOnItem from './BoltOnItem';
import useBoltonList from 'src/hooks/useBoltonList';
import InfoBlock from './InfoBlock';
import Link, { LinkColor } from 'src/components/Link';
import { ROUTES } from 'src/config/navigation';
import AddIcon from 'src/icons/Add';
import { formatPercent } from 'src/helper/numbers';

export default function BoltOnsList() {
  const {
    isLoading: isDataLoading,
    boltOnsDetails,
    sortedBoltOnsByStatus,
    amountOfActiveBoltOns
  } = useBoltonList();

  const details = [
    {
      title: 'Bolt-on purchases',
      value: boltOnsDetails?.purchases
    },
    {
      title: 'Bolt-on revenue',
      value: currencyRounded(Number(boltOnsDetails?.revenue) || 0)
    },
    {
      title: 'Bolt-on perk redemptions',
      value: boltOnsDetails?.perk_redemptions
    },
    {
      title: 'Bolt-on uptake',
      value: formatPercent(boltOnsDetails?.uptake)
    }
  ];

  return (
    <Card
      simple
      subtitle={`${amountOfActiveBoltOns} active bolt-on${
        amountOfActiveBoltOns !== 1 ? 's' : ''
      }`}
      isLoading={isDataLoading}
      actions={
        <Link
          to={ROUTES.BOLT_ON_ADD}
          color={LinkColor.GrayGradient}
          icon={<AddIcon />}
        >
          Add new bolt on
        </Link>
      }
    >
      {sortedBoltOnsByStatus.length === 0 ? (
        <div className="flex items-center justify-center h-64">
          <Typography>No existing bolt-ons</Typography>
        </div>
      ) : (
        <>
          <InfoCard
            classes="grid grid-cols-4 gap-5 h-[280px] mb-7"
            gradientSize="big"
          >
            {details.map(({ title, value }, index) => (
              <InfoBlock
                key={title}
                title={title}
                value={value ?? ''}
                valueColor={index !== 3 ? TypographyColor.Success : undefined}
              />
            ))}
          </InfoCard>

          <div className="grid grid-cols-4 gap-x-8 gap-y-6">
            {sortedBoltOnsByStatus.map((boltOn) => (
              <BoltOnItem key={boltOn.id} {...boltOn} />
            ))}
          </div>
        </>
      )}
    </Card>
  );
}
