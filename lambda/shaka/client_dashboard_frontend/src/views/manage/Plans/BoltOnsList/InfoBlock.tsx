import InfoCard from 'src/components/InfoCard';
import Typography, {
  TypographyColor,
  TypographySize
} from 'src/components/Typography';
// import InfoCard from './InfoCard';

interface InfoBlockProps {
  title: string;
  value: string | number;
  fontSize?: TypographySize;
  valueColor?: TypographyColor;
  // bold?: boolean;
}

const InfoBlock = ({
  title,
  value,
  fontSize = TypographySize.CardTitle,
  valueColor = TypographyColor.Primary,
}: InfoBlockProps) => {
  return (
    <InfoCard classes="h-full">
      <div className="text-center">
        <Typography size={fontSize} color={valueColor} bold>
          {value}
        </Typography>
      </div>
      <div className="absolute bottom-5 left-0 w-full text-center px-8">
        <Typography size={TypographySize.BodyS}>
          {title}
        </Typography>
      </div>
    </InfoCard>
  );
};

export default InfoBlock;
