import * as yup from 'yup';
import { Inputs } from './types';

export const schema = yup
  .object({
    name: yup.string().required('Name is required'),
    cost: yup
      .number()
      .typeError('Cost is required')
      .required('Cost is required')
      .min(0)
      .when('min_cost', ([min_cost], schema) => {
        return min_cost
          ? schema.min(
              Number(min_cost),
              `Cost must be greater than or equal to minimum price`
            )
          : schema;
      }),
    availability_date: yup
      .string()
      .when(['availability'], ([availability], schema) => {
        return availability === 'inactive'
          ? schema.required('Date is required')
          : schema.optional();
      }),
    status: yup.string().optional(),
    availability: yup.string().optional(),
    min_cost: yup.string().optional(),
    offering_id: yup.number().when('edit_page', ([edit_page], schema) => {
      return !edit_page
        ? schema.required('Bolt on base is required')
        : schema.optional();
    })
  })
  .required() as unknown as yup.ObjectSchema<Inputs>;
