import Card from 'src/components/Card';
import { convertToGb, convertToMins, formatNumber } from 'src/helper';
import Typography, {
  TypographyColor,
  TypographySize
} from 'src/components/Typography';
import { fetchNetworkStatistic } from 'src/api/network';
import { DateTime } from 'luxon';
import { useAsync } from 'src/api/useApi';

function Stats({ label, content }: { label: string; content: string }) {
  return (
    <div className="font-manrope border-l-[3px] border-neutral-100 px-2.5">
      <Typography>{label}</Typography>
      <Typography color={TypographyColor.Success} as="p">
        {content}
      </Typography>
    </div>
  );
}

export default function NetworkStatistics() {
  const { data: list = [], isLoading } = useAsync(fetchNetworkStatistic, {
    fetchOnMount: true
  });

  return (
    <Card title="Network Statistics" isLoading={isLoading} fullHeight>
      <div className="flex flex-col gap-14 mt-4">
        {list.map((item) => (
          <div key={item.date} className="grid grid-cols-4 gap-4 items-center">
            <Typography
              color={TypographyColor.Accent}
              size={TypographySize.Title}
            >
              {DateTime.fromISO(item.date).toFormat('LLLL yyyy')}
            </Typography>

            <div className="col-span-1 pr-10">
              <Stats
                label="Data"
                content={convertToGb(item.data)}
              />
            </div>

            <div className="col-span-1">
              <Stats
                label="Voice"
                content={`${convertToMins(item.voice)} minutes`}
              />
            </div>

            <div className="col-span-1">
              <Stats
                label="SMS"
                content={`${formatNumber(item.sms, {
                  notation: 'compact'
                })}`}
              />
            </div>
          </div>
        ))}
      </div>
    </Card>
  );
}
