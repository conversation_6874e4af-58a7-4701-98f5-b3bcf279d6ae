import Card from 'src/components/Card';
import { fetchMonthStatements } from 'src/api/statements';
import { DateTime } from 'luxon';
import StatementsTable from 'src/components/StatementsTable';
import Typography, {
  TypographyColor,
  TypographySize
} from 'src/components/Typography';
import {
  selectorStatements,
  selectorStatementsLoaded,
  setStatements,
  useStatementsStore
} from 'src/store/statements';
import { useAsync } from 'src/api/useApi';

const { year, month } = DateTime.now().toObject();

const MonetizeDashboard = () => {
  const statements = useStatementsStore(selectorStatements);
  const statementsLoaded = useStatementsStore(selectorStatementsLoaded);

  const { isLoading: isLoadingData } = useAsync(fetchMonthStatements, {
    fetchOnMount: !statementsLoaded,
    setToStore: setStatements
  });

  const isLoading = !statementsLoaded && isLoadingData;
  const currentMonthStatements = statementsLoaded
    ? statements[year][month.toString()]
    : {};

  return (
    <Card title="Monetize" isLoading={isLoading} fullHeight>
      <Typography color={TypographyColor.Accent} size={TypographySize.Title}>
        {DateTime.now().toFormat('LLLL yyyy')}
      </Typography>
      <div className="flex flex-col gap-8">
        <StatementsTable
          data={Object.values(currentMonthStatements)}
          isPredicted
        />
      </div>
    </Card>
  );
};

export default MonetizeDashboard;
