import { Eligibility } from 'src/types/perks';

export const eligibilityTypeOptions: { value: Eligibility; label: string }[] = [
  { value: 'tenure', label: 'Tenure' },
  { value: 'total_spend', label: 'Total spend' },
  { value: 'total_points_earned', label: 'Total points earned' },
  { value: 'airdrop', label: 'Airdrop' },
  { value: 'no_free', label: 'No free claim eligibility' }
];

export const yesNoOptions = [
  { value: 'yes', label: 'Yes' },
  { value: 'no', label: 'No' }
];

export const yesNoOptionsReverse = yesNoOptions.reverse();

export const perkTypeOptions = [
  { value: 'bolt_on', label: 'Bolt-on' },
  { value: 'discount', label: 'Discount' },
  { value: 'voucher', label: 'Voucher' },
  { value: 'physical', label: 'Physical  (coming soon)' }
];

export const availabilityOptions = [
  { value: 'immediately', label: 'Immediately' },
  { value: 'specific', label: 'Specific date' }
];

export const statusOptions = [
  { value: 'enabled', label: 'Enabled' },
  { value: 'disabled', label: 'Disabled' }
];
