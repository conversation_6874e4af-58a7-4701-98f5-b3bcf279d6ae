import Button, { ButtonColor } from 'src/components/Button';
import ErrorText from 'src/components/ErrorText';
import Typography, {
  TypographyShade,
  TypographySize
} from 'src/components/Typography';
import useImageDrop from 'src/hooks/useImageDrop';
import ImageIcon from 'src/icons/Image';

interface Props {
  src: string;
  onChange: (src: string) => void;
}

export default function ImageDrop({ src, onChange }: Props) {
  const { isDropped, metadata, getRootProps, getInputProps, error } =
    useImageDrop(onChange);

  return (
    <div
      {...getRootProps()}
      className="relative w-full rounded-xl bg-white/5 cursor-pointer h-[230px]"
    >
      <div className="w-full h-full flex items-center p-5 max-md:flex-col max-md:text-center gap-4">
        <div className="w-1/3 max-w-[190px] h-[190px] rounded-xl overflow-hidden grow">
          {src ? (
            <img
              src={src}
              className="w-full h-full object-cover object-center"
            />
          ) : (
            <span className="h-full flex items-center justify-center bg-white/5 ">
              <ImageIcon />
            </span>
          )}
        </div>

        <input {...getInputProps()} />

        <div className="flex flex-col gap-4 grow items-center max-w-[50%]">
          {isDropped ? (
            <>
              <>
                <Typography bold truncate>
                  {metadata.name}
                </Typography>
                <Typography
                  shade={TypographyShade.Light}
                  as="p"
                  size={TypographySize.Caption}
                >
                  {metadata.width}x{metadata.height} pixels
                </Typography>
              </>
              <Button type="button" color={ButtonColor.Light}>
                Change image
              </Button>
            </>
          ) : (
            <>
              <div className="text-center">
                {error ? (
                  <div className="max-w-[220px]">
                    <ErrorText>{error}</ErrorText>
                  </div>
                ) : (
                  <Typography shade={TypographyShade.Dark}>
                    Drag and drop new image or
                  </Typography>
                )}
              </div>
              <Button type="button" color={ButtonColor.Light}>
                Choose image
              </Button>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
