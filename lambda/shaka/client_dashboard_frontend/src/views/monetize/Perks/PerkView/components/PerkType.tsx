import { Controller, useFormContext } from 'react-hook-form';
import { Inputs } from '../types';
import InputWrapper from 'src/components/form/WrapperWithLabel';
import RadioButtonGroup from 'src/components/form/RadioButtonGroup';
import { perkTypeOptions } from '../constants';
import Voucher from './Voucher';
import Discount from './Discount';
import BoltOn from './BoltOn';

export default function PerkType() {
  const { control, watch } = useFormContext<Inputs>();

  const watchType = watch('type');

  return (
    <div>
      <InputWrapper label="Perk type">
        <Controller
          name="type"
          control={control}
          render={({ field: { value, onChange } }) => (
            <div className="w-min">
              <RadioButtonGroup
                options={perkTypeOptions}
                horizontal
                value={value}
                onChange={onChange}
                disabledOptions={['physical']}
              />
            </div>
          )}
        />
      </InputWrapper>

      {watchType === 'bolt_on' && <BoltOn />}
      {watchType === 'discount' && <Discount />}
      {watchType === 'voucher' && <Voucher />}
    </div>
  );
}
