import { useFormContext } from 'react-hook-form';
import InputWrapper from 'src/components/form/WrapperWithLabel';
import { Inputs } from '../types';
import Input from 'src/components/form/Input';
import Textarea from 'src/components/form/Textarea';
import Calendar from 'src/components/form/Calendar';

export default function Voucher() {
  const { control, register } = useFormContext<Inputs>();

  return (
    <div className="w-1/2 space-y-10">
      <InputWrapper label="Name">
        <Input
          {...register('voucher_details.merchant_name')}
          placeholder="Type in the name of voucher merchant"
        />
      </InputWrapper>
      <InputWrapper label="Code">
        <Input
          {...register('voucher_details.code')}
          placeholder="Type in the voucher code"
        />
      </InputWrapper>
      <InputWrapper label="URL" optional>
        <Input
          {...register('voucher_details.url')}
          placeholder="URL to redeem"
        />
      </InputWrapper>
      <InputWrapper label="Expiry date" optional>
        <Calendar {...register('voucher_details.expiry_date')} />
      </InputWrapper>
      <InputWrapper label="Instructions to redeem" optional>
        <Textarea
          name="voucher_details.instructions"
          control={control}
          placeholder="Add instructions for subscribers how to redeem the voucher if needed"
        />
      </InputWrapper>
    </div>
  );
}
