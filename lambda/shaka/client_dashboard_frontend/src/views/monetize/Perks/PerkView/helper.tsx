import {
  BoltOnDetails,
  DiscountDetails,
  Perk,
  VoucherDetails
} from 'src/types/perks';
import { Inputs, PerkStatus } from './types';
import { PerkPayload } from 'src/api/perks';

export const convertToPayload = (data: Inputs): PerkPayload => {
  const {
    redemption,
    name,
    description,
    eligibility_type,
    status,
    eligibility,
    availability_date,
    cost,
    pointsCost,
    use_points,
    image,
    availability,
    type,
    featured
  } = data;
  const discountDetails =
    type === 'discount' ? data.discount_details : ({} as DiscountDetails);
  const boltOnDetails =
    type === 'bolt_on' ? data.bolt_on_details : ({} as BoltOnDetails);
  const voucherDetails =
    type === 'voucher' ? data.voucher_details : ({} as VoucherDetails);

  const redeemable = use_points === 'yes';

  const eligibilityThreshold =
    eligibility_type === 'no_free' || eligibility_type === 'airdrop'
      ? '0'
      : eligibility.amount;

  // @ts-expect-error bolt_on_details type
  return {
    name,
    perk_image: image,
    description: description || '',
    eligibility_type: eligibility_type,
    enabled: status === 'enabled',
    eligibility_threshold: eligibilityThreshold,
    allow_multiple_redemptions: eligibility.multiple_redemptions === 'yes',
    redemption_limit: redemption === 'limited' ? data.redemption_limit || 0 : 0,
    availability_date:
      availability === 'inactive'
        ? availability_date!
        : new Date().toISOString(),
    target_group: eligibility.target_group || '',
    cost_base: cost || 0,
    elective_redemption_cost: redeemable ? pointsCost?.toString() || '' : '',
    electively_redeemable: redeemable,
    ...(type === 'discount' && {
      discount_details: {
        plan_discount: {
          discount_type: discountDetails.discount_type,
          discount_amount:
            discountDetails?.discount_type === 'flat'
              ? discountDetails.discount_amount
              : null,
          discount_percentage:
            discountDetails.discount_type === 'percentage'
              ? discountDetails.discount_percentage
              : null,
          discount_duration_months: discountDetails.discount_duration_months
        }
      }
    }),
    ...(type === 'bolt_on' && {
      bolt_on_details: {
        bolt_on: boltOnDetails.bolt_on[0]
      }
    }),
    ...(type === 'voucher' && {
      voucher_details: voucherDetails
    }),

    featured
  };
};

export const convertToForm = (perk: Perk): Inputs => {
  const { discount_details, bolt_on_details, voucher_details } = perk;
  const { plan_discount } = discount_details || {};
  const redeemable = perk.electively_redeemable;

  const getDetails = () => {
    if (voucher_details) {
      return {
        type: 'voucher',
        voucher_details: {
          ...voucher_details,
          expiry_date: voucher_details.expiry_date?.slice(0, 16) || ''
        }
      };
    }
    if (bolt_on_details) {
      return { type: 'bolt_on', bolt_on_details };
    }

    return { type: 'discount', discount_details: plan_discount };
  };

  // @ts-expect-error bolt_on_details type
  return {
    name: perk.name,
    image: perk.perk_image || '',
    description: perk.description,
    eligibility_type: perk.eligibility_type,
    status: perk.enabled ? 'enabled' : 'disabled',
    eligibility: {
      amount: perk.eligibility_threshold?.replace(/\.00/g, ''),
      multiple_redemptions: perk.allow_multiple_redemptions ? 'yes' : 'no'
    },

    availability: perk.availability_date
      ? PerkStatus.Inactive
      : PerkStatus.Active,
    availability_date: perk.availability_date?.slice(0, 16) || '',
    redemption: perk.redemption_limit ? 'limited' : 'unlimited',
    redemption_limit: perk.redemption_limit || 0,
    cost: perk.cost_base ? perk.cost_base : 0,
    use_points: redeemable ? 'yes' : 'no',
    pointsCost: redeemable
      ? perk.elective_redemption_cost?.replace(/\.00/g, '')
      : '',
    featured: perk.featured,

    ...getDetails()
  };
};

export const fieldsTitles = {
  eligibility_threshold: 'Tenure amount',
  name: 'Name',
  'voucher_details.code': 'Voucher code'
};
