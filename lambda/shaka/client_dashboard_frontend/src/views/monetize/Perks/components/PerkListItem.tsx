import clsx from 'clsx';
import Link, { LinkColor } from 'src/components/Link';
import Typography, {
  TypographyColor,
  TypographySize
} from 'src/components/Typography';
import { getPerksEditRoute, getPerksViewRoute } from 'src/config/navigation';
import ImageIcon from 'src/icons/Image';
import { Perk } from 'src/types/perks';

export default function PerkListItem({
  name,
  id,
  perk_image,
  redemptions_amount = 0,
  inactive,
  featured
}: Perk & { inactive?: boolean }) {
  return (
    <div
      className={clsx(
        'flex flex-col bg-white/5 rounded-3xl p-4 space-y-4 relative overflow-hidden',
        inactive && 'pointer-events-none'
      )}
    >
      {inactive && (
        <>
          <div className="absolute top-0 left-0 w-full h-full bg-black/60" />
          <div className="absolute inset-0 flex justify-center items-center z-10 pointer-events-auto w-2/3 m-auto">
            <Link
              to={getPerksEditRoute(id!)}
              color={LinkColor.Default}
              fullWidth
            >
              Activate
            </Link>
          </div>
        </>
      )}
      <div className="grow">
        <Typography size={TypographySize.Subtitle} as="h4" bold>
          {name}
        </Typography>
      </div>
      <div className="relative rounded-xl overflow-hidden h-[204px]">
        {perk_image ? (
          <img
            src={perk_image}
            className="w-full h-full object-cover object-center"
          />
        ) : (
          <span className="h-full flex items-center justify-center bg-white/5 ">
            <ImageIcon />
          </span>
        )}
        {featured && (
          <span className="absolute top-2 right-2 bg-black/30 rounded-xl text-white px-2.5 py-1 font-semibold backdrop-blur-md">
            Featured
          </span>
        )}
      </div>
      <div className="flex justify-between items-center">
        <Typography>
          Claimed{' '}
          <Typography color={TypographyColor.Success}>
            {redemptions_amount}x
          </Typography>
        </Typography>
        <Link to={getPerksViewRoute(id!)} color={LinkColor.DarkAction}>
          View
        </Link>
      </div>
    </div>
  );
}
