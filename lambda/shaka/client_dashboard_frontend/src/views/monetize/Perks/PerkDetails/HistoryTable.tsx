import { createColumnHelper } from '@tanstack/react-table';
import Table from 'src/components/Table';
import { currencyRounded } from 'src/helper';
import { DateTime } from 'luxon';

type Columns = {
  [key: string]: string | number | boolean;
};

export const COLUMNS = {
  date: 'Month',
  redemptions: 'REDEMPTIONS',
  cost: 'Cost',
};

type Props = {
  data: {
    date: string;
    redemptions: number;
    cost: number;
  }[];
};

const columnHelper = createColumnHelper<Columns>();
const columns = [
  { accessorKey: 'date' },
  { accessorKey: 'redemptions', size: 70 },
  { accessorKey: 'cost', size: 70 },
].map(({ accessorKey: key, size }) =>
  columnHelper.accessor(key, {
    size: size || undefined,
    cell: (info) => {
      switch (key) {
        case 'date':
          return (
            <div className="text-left">
              {DateTime.fromISO(info.getValue().toString()).toFormat(
                'MMMM yyyy'
              )}
            </div>
          );
        case 'cost':
          return currencyRounded(parseFloat(info.getValue() as string));
        case 'redemptions':
          return info.getValue();
        // case 'quantity':
        //   return info.row.original.is_unlimited ? '∞' : info.getValue();
        default:
          return info.getValue();
      }
    },

    header: () => {
      if (key === 'date') {
        return <div className="text-left">{COLUMNS[key]}</div>;
      }

      return (COLUMNS as Columns)[key];
    }
  })
);

export default function HistoryTable({ data }: Props) {
  return <Table columns={columns} data={data} />;
}
