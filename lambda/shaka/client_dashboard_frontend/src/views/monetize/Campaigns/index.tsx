import Card from 'src/components/Card';
import Link, { LinkColor } from 'src/components/Link';
import Typography from 'src/components/Typography';
import { ROUTES } from 'src/config/navigation';
import AddIcon from 'src/icons/Add';
import CampaignListItem from './CampaignListItem';
import useCampaigns from 'src/hooks/useCampaigns';

export default function Campaigns() {
  const { data: campaigns = [], isLoading } = useCampaigns({
    loadOnMount: true
  });

  return (
    <Card
      title="Campaigns"
      subtitle="All campaigns"
      isLoading={isLoading}
      actions={
        campaigns.length !== 0 && (
          <Link
            to={ROUTES.CAMPAIGNS_ADD}
            color={LinkColor.GrayGradient}
            icon={<AddIcon />}
          >
            Add new campaign
          </Link>
        )
      }
    >
      {campaigns.length === 0 ? (
        <div className="flex flex-col items-center justify-center gap-6 h-64">
          <Typography>No existing campaigns</Typography>
          <Link to={ROUTES.CAMPAIGNS_ADD} className="ml-2">
            Create a campaign
          </Link>
        </div>
      ) : (
        <div className="space-y-7">
          {campaigns.map((campaign) => (
            <CampaignListItem key={campaign.id} campaign={campaign} />
          ))}
        </div>
      )}
    </Card>
  );
}
