import { Controller, useForm } from 'react-hook-form';
import Button, { ButtonColor } from 'src/components/Button';
import Card from 'src/components/Card';
import Dropdown from 'src/components/form/Dropdown';
import Input from 'src/components/form/Input';
import RadioButtonGroup from 'src/components/form/RadioButtonGroup';
import LabeledInputWrapper from 'src/components/form/WrapperWithLabel';
import Typography, {
  TypographyColor,
  TypographyShade,
  TypographySize
} from 'src/components/Typography';
import usePlanList from 'src/hooks/usePlanList';
import usePlansOffering from 'src/hooks/usePlansOffering';
import { selectorPlans, usePlansStore } from 'src/store/plan';
import CampaignPlanCard from './CampaignPlanCard';
import {
  campaignTargetOptions,
  campaignTypeOptions,
  defaultPlanProps,
  monthOptions
} from '../contants';
import Calendar from 'src/components/form/Calendar';
import { DateTime } from 'luxon';
import Link from 'src/components/Link';
import { ROUTES } from 'src/config/navigation';
import { Inputs } from './types';
import { Eligibility } from 'src/types/campaign';
import { saveCampaign, updateCampaign } from 'src/api/campaign';
import { convertToFormData, getCampaignDuration, getPayload } from './helpers';
import { InfoBigIcon } from 'src/icons/InfoBig';
import { useNavigate, useParams } from 'react-router-dom';
import { useEffect } from 'react';
import useCampaigns from 'src/hooks/useCampaigns';
import { schema } from './schema';
import { yupResolver } from '@hookform/resolvers/yup';
import { useAsync } from 'src/api/useApi';
import ErrorText from 'src/components/ErrorText';

export default function CampaignView() {
  const { campaignId } = useParams();
  const navigate = useNavigate();

  const plans = usePlansStore(selectorPlans);
  const { isLoading: isPlansLoading } = usePlanList();
  const { isLoading: isOfferingLoading } = usePlansOffering();
  const { dataById, isLoading: isCampaignsLoading } = useCampaigns();
  const { run: doSaveCampaign, error: saveError } = useAsync(saveCampaign);
  const { run: doUpdateCampaign, error: updateError } =
    useAsync(updateCampaign);

  const isEdit = Boolean(campaignId);
  const title = isEdit ? 'Edit campaign' : 'Create a new campaign';
  const responseError = saveError || updateError;

  const {
    handleSubmit,
    register,
    control,
    watch,
    setValue,
    reset,
    formState: { errors }
  } = useForm<Inputs>({
    defaultValues: {
      eligibility: Eligibility.NEW_SUBSCRIBERS,
      activeDurationMonths: 1,
      campaignType: 'discount',
      startDate: DateTime.now().toISO().slice(0, 16),
      endDate: '',
      selectedPlans: [],
      planDiscounts: {}
    },
    resolver: yupResolver(schema)
  });

  useEffect(() => {
    if (campaignId && !isCampaignsLoading) {
      const campaign = dataById[parseInt(campaignId)];

      reset(convertToFormData(campaign));
    }
  }, [isCampaignsLoading, campaignId]);

  const watchEligibility = watch('eligibility');
  const watchSelectedPlans = watch('selectedPlans');
  const startDate = watch('startDate');
  const endDate = watch('endDate');

  const onSubmit = (data: Inputs) => {
    if (isEdit) {
      doUpdateCampaign(campaignId!, getPayload(data)).then(() => {
        navigate(ROUTES.CAMPAIGNS);
      });

      return;
    }

    doSaveCampaign(getPayload(data)).then(() => {
      navigate(ROUTES.CAMPAIGNS);
    });
  };

  const handlePlanSelection = (planId: number) => {
    const planDiscounts = watch('planDiscounts');

    if (!planDiscounts[planId]) {
      setValue('planDiscounts', {
        ...planDiscounts,
        [planId]: defaultPlanProps
      });
    }
  };

  const selectAllPlans = () => {
    setValue(
      'selectedPlans',
      plans.map((plan) => plan.id)
    );

    plans.forEach((plan) => {
      handlePlanSelection(plan.id);
    });
  };

  const campaignDuration = getCampaignDuration(startDate, endDate);

  return (
    <Card
      title="Campaigns"
      subtitle={title}
      isLoading={isPlansLoading || isOfferingLoading}
    >
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="space-y-10">
          <LabeledInputWrapper
            label="Title"
            className="w-2/5"
            error={errors.title?.message}
          >
            <Input
              placeholder="Type in the title of your campaign"
              {...register('title')}
            />
          </LabeledInputWrapper>

          <LabeledInputWrapper label="Campaign target">
            <div className="flex gap-10">
              <div className="flex flex-col gap-2 w-2/5">
                <Typography
                  size={TypographySize.Caption}
                  shade={TypographyShade.Dark}
                >
                  Existing vs new customers:
                </Typography>

                <Controller
                  name="eligibility"
                  control={control}
                  render={({ field: { value, onChange } }) => (
                    <RadioButtonGroup
                      horizontal
                      value={value}
                      shape="square"
                      options={campaignTargetOptions}
                      onChange={onChange}
                    />
                  )}
                />
              </div>
              {watchEligibility === Eligibility.SUBSCRIBER_ACTIVITY && (
                <div className="flex flex-col gap-2 w-2/5">
                  <Typography
                    size={TypographySize.Caption}
                    shade={TypographyShade.Dark}
                  >
                    Subscriber eligibility (for existing customers):
                  </Typography>
                  <div className="bg-grey-area rounded-xl p-5 max-w-[275px]">
                    <Dropdown
                      control={control}
                      name="activeDurationMonths"
                      options={monthOptions}
                      shape="square"
                    />
                  </div>
                </div>
              )}
            </div>
          </LabeledInputWrapper>

          <LabeledInputWrapper label="Campaign type">
            <div className="flex items-center gap-8 h-12">
              <RadioButtonGroup
                horizontal
                value="discount"
                options={campaignTypeOptions}
                disabledOptions={['allowance', 'loyalty']}
                tooltipOptions={{
                  allowance: 'Coming soon',
                  loyalty: 'Coming soon'
                }}
                onChange={() => {}}
              />
            </div>
          </LabeledInputWrapper>

          <div className="pt-4">
            <div className="text-right mb-3">
              <button
                type="button"
                className="hover:opacity-70"
                onClick={selectAllPlans}
              >
                Select all plans
              </button>
            </div>
            <div className="grid grid-cols-3 gap-3 items-start">
              {plans.map((plan) => (
                <CampaignPlanCard
                  key={plan.id}
                  plan={plan}
                  control={control}
                  register={register}
                  onPlanSelect={handlePlanSelection}
                  isPlanSelected={watchSelectedPlans.includes(plan.id)}
                />
              ))}
            </div>
            {errors.selectedPlans?.message && (
              <div className="mt-4">
                <Typography
                  size={TypographySize.Caption}
                  color={TypographyColor.Error}
                >
                  {errors.selectedPlans?.message}
                </Typography>
              </div>
            )}
          </div>
        </div>

        <div className="space-y-14 mt-24">
          <div className='flex justify-between items-center bg-[url("/monetize/info-bg.png")] h-[100px] px-10 rounded-3xl bg-cover'>
            <Typography>
              For existing subscribers, discounts will be applied to
              subscriber’s next billing cycle
            </Typography>
            <InfoBigIcon />
          </div>
          <LabeledInputWrapper
            label="Campaign start and finish"
            error={errors.endDate?.message}
          >
            <div className="flex gap-10 items-center">
              <div className="flex gap-4">
                <Calendar {...register('startDate')} />
                <Calendar
                  {...register('endDate')}
                  value={endDate}
                  min={startDate}
                  placeholder="End date"
                />
              </div>
              {campaignDuration && (
                <Typography shade={TypographyShade.Dark}>
                  Your campaign will run for{' '}
                  <Typography bold>{campaignDuration}</Typography>
                </Typography>
              )}
            </div>
          </LabeledInputWrapper>

          <LabeledInputWrapper label="Marketing communication">
            <div className="rounded-2xl bg-grey-area h-56 flex flex-col gap-10 justify-center items-center">
              <Typography
                size={TypographySize.Title}
                shade={TypographyShade.Dark}
              >
                Help spread the word
              </Typography>

              <Link to={ROUTES.BEAMS_ADD} target="_blank">
                New beam
              </Link>
            </div>
          </LabeledInputWrapper>
        </div>

        {responseError && (
          <ErrorText
            titles={{
              title: 'Title',
              end_date: 'End date'
            }}
          >
            {responseError}
          </ErrorText>
        )}

        <div className="flex justify-center mt-20 gap-5">
          <Link type="button" to={ROUTES.CAMPAIGNS}>
            Cancel
          </Link>
          <Button type="submit" color={ButtonColor.Pink}>
            Save campaign
          </Button>
        </div>
      </form>
    </Card>
  );
}
