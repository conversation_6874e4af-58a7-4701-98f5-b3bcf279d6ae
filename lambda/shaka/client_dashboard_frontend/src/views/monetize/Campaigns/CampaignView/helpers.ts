import { DateTime } from 'luxon';
import { Inputs } from './types';
import { Campaign } from 'src/types/campaign';

export const getCampaignDuration = (startDate?: string, endDate?: string) => {
  if (!startDate || !endDate) {
    return null;
  }

  const campaignDurationInDays =
  startDate && endDate
  ? DateTime.fromISO(endDate).diff(DateTime.fromISO(startDate), 'days').days
  : 0;

  
  const campaignDurationInHours =
    startDate && endDate
      ? DateTime.fromISO(endDate).diff(DateTime.fromISO(startDate), 'hours')
          .hours.toFixed()
      : 0;

  if (campaignDurationInDays >= 1) {
    const finalDays = Math.floor(campaignDurationInDays);
    return finalDays !== 1
      ? `${finalDays} days`
      : `${finalDays} day`;
  }

  if (campaignDurationInHours) {
    return parseInt(campaignDurationInHours) !== 1
      ? `${campaignDurationInHours} hours`
      : `${campaignDurationInHours} hour`;
  }

  return null;
};

export const getPayload = (data: Inputs) => {
  const {
    title,
    activeDurationMonths,
    campaignType,
    eligibility,
    endDate,
    planDiscounts,
    selectedPlans,
    startDate
  } = data;

  return {
    title,
    marketing_eligibility: {
      eligibility_type: eligibility,
      active_since: null,
      active_duration_months: activeDurationMonths
    },
    campaign_type: campaignType,
    start_date: DateTime.fromJSDate(new Date(startDate)).toISO() || '',
    end_date: DateTime.fromJSDate(new Date(endDate)).toISO() || '',
    status: 'draft',
    plan_discounts: selectedPlans.map((planId) => {
      const { discount_amount, discount_percentage, ...restProps } =
        planDiscounts[planId];

      return {
        discount_amount: discount_amount?.toString() || null,
        discount_percentage: discount_percentage?.toString() || null,
        ...restProps,
        plan: planId
      };
    })
  };
};

export const convertToFormData = (campaign: Campaign): Inputs => {
  return {
    title: campaign.title,
    activeDurationMonths: campaign.marketing_eligibility.active_duration_months  || 1,
    campaignType: campaign.campaign_type,
    eligibility: campaign.marketing_eligibility.eligibility_type,
    startDate: DateTime.fromISO(campaign.start_date).toISO()?.slice(0, 16)|| '',
    endDate: DateTime.fromISO(campaign.end_date).toISO()?.slice(0, 16) || '',
    selectedPlans: campaign.plan_discounts.map((discount) => discount.plan),
    planDiscounts: campaign.plan_discounts.reduce(
      (acc, discount) => {
        acc[discount.plan] = {
          discount_type: discount.discount_type,
          discount_amount: discount.discount_amount || 0,
          discount_percentage: discount.discount_percentage || 0,
          discount_duration_months: discount.discount_duration_months || 1
        };
        return acc;
      },
      {} as Inputs['planDiscounts']
    )
  };
};
