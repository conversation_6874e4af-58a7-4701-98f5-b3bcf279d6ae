import { useController, useWatch } from 'react-hook-form';
import Dropdown from 'src/components/form/Dropdown';
import Input from 'src/components/form/Input';
import Typography, { TypographySize } from 'src/components/Typography';
import { Plan } from 'src/types/plans';
import { discountTypeOptions, monthOptionsShort } from '../contants';
import Status, { StatusColor } from 'src/components/Status';
import PlanCard from 'src/components/PlanCard';

const planPropsWrapperStyles =
  'flex max-xl:flex-col justify-between items-center';

export default function CampaignPlanCard({
  plan,
  control,
  register,
  isPlanSelected,
  onPlanSelect
}: {
  plan: Plan;
  control: any;
  register: any;
  isPlanSelected: boolean;
  onPlanSelect: (planId: number) => void;
}) {
  const planId = plan.id;
  const {
    field: { value: selectedPlans, onChange: setSelectedPlans }
  } = useController({
    name: 'selectedPlans',
    control
  });

  const discountType = useWatch({
    control,
    name: `planDiscounts.${planId}.discount_type`
  });

  const isDiscountTypePercentage = discountType === 'percentage';

  const handlePlanSelection = () => {
    if (selectedPlans.includes(planId)) {
      setSelectedPlans(selectedPlans.filter((id: number) => id !== planId));
    } else {
      onPlanSelect(planId);
      setSelectedPlans([...selectedPlans, planId]);
    }
  };

  return (
    <div className="bg-white/5 rounded-3xl p-3 pb-4 col-span-1 space-y-4">
      <PlanCard {...plan} />
      <div className="text-center">
        <button
          type="button"
          className="bg-black/25 rounded-full px-8 py-1.5"
          onClick={handlePlanSelection}
        >
          {isPlanSelected ? (
            <Status status="Selected" color={StatusColor.Success} />
          ) : (
            'Select plan'
          )}
        </button>
      </div>
      {isPlanSelected && (
        <div className="space-y-4 px-4">
          <div className={planPropsWrapperStyles}>
            <Typography size={TypographySize.BodyS}>Discount type:</Typography>
            <div className="w-1/2">
              <Dropdown
                control={control}
                name={`planDiscounts.${planId}.discount_type`}
                options={discountTypeOptions}
                shape="square"
              />
            </div>
          </div>
          <div className={planPropsWrapperStyles}>
            <Typography size={TypographySize.BodyS}>Amount:</Typography>
            <div className="w-1/2">
              {isDiscountTypePercentage ? (
                <Input
                  shape="square"
                  {...register(`planDiscounts.${planId}.discount_percentage`)}
                />
              ) : (
                <Input
                  shape="square"
                  {...register(`planDiscounts.${planId}.discount_amount`)}
                />
              )}
            </div>
          </div>
          <div className={planPropsWrapperStyles}>
            <Typography size={TypographySize.BodyS}>
              Discount duration:
            </Typography>
            <div className="w-1/2">
              <Dropdown
                control={control}
                name={`planDiscounts.${planId}.discount_duration_months`}
                options={monthOptionsShort}
                shape="square"
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
