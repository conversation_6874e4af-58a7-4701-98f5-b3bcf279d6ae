import Card from 'src/components/Card';
import MonthPicker from 'src/components/MonthPicker';
import { useEffect, useMemo, useState } from 'react';
import { Statement } from 'src/types/statements';
import { fetchMonthStatements } from 'src/api/statements';
import { DateTime } from 'luxon';
import Typography, { TypographyColor } from 'src/components/Typography';
import { currencyFormat } from 'src/helper';
import StatementsTable from 'src/components/StatementsTable';
import {
  selectorStatements,
  selectorStatementsLoaded,
  setStatements,
  useStatementsStore
} from 'src/store/statements';
import { useAsync } from 'src/api/useApi';

const Statements = () => {
  const statements = useStatementsStore(selectorStatements);
  const statementsLoaded = useStatementsStore(selectorStatementsLoaded);

  const { isLoading: isLoadingData } = useAsync(fetchMonthStatements, {
    fetchOnMount: !statementsLoaded,
    setToStore: setStatements
  });

  const isLoading = !statementsLoaded && isLoadingData;

  const [yearsOptions, setYearsOptions] = useState<string[]>();
  const [minDate, setMinDate] = useState<{ month: string; year: string }>();
  const [selectedStatements, setSelectedStatements] = useState<Statement[]>([]);

  const total = useMemo(
    () =>
      selectedStatements.reduce((acc, statement) => acc + statement.profit, 0),
    [selectedStatements]
  );

  useEffect(() => {
    if (!statementsLoaded) return;

    const { year, month } = DateTime.now().toObject();
    const years = Object.keys(statements);
    const months = Object.keys(statements[years[0]]);
    const currentMonthStatements = statements[year][month.toString()];
    setSelectedStatements(Object.values(currentMonthStatements));
    setMinDate({ year: years[0], month: months[0] });
    setYearsOptions(years);
  }, [statements, statementsLoaded]);

  const handlePeriodChange = (year: string, month: string) => {
    const currentMonthStatements = statements
      ? statements[year][month.toString()]
      : {};
    setSelectedStatements(Object.values(currentMonthStatements));
  };

  return (
    <Card title="Monetize" isLoading={isLoading} fullHeight>
      <div className="flex flex-col gap-8">
        <MonthPicker
          onChange={handlePeriodChange}
          yearsOptions={yearsOptions}
          minDate={minDate}
        />
        <StatementsTable data={selectedStatements} />
      </div>
      <div className="text-right mt-4">
        <Typography>
          Total:{' '}
          <Typography color={TypographyColor.Accent} bold>
            {currencyFormat(total)}
          </Typography>
        </Typography>
      </div>
    </Card>
  );
};

export default Statements;
