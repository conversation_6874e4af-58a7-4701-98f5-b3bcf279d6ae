import { useForm } from 'react-hook-form';
import { useAsync } from 'src/api/useApi';
import { login } from 'src/api/auth';
import Button, { ButtonColor } from 'src/components/Button';
import { useNavigate } from 'react-router-dom';
import { ROUTES } from 'src/config/navigation';
import ErrorText from 'src/components/ErrorText';
import Input from 'src/components/form/Input';
import LabeledInputWrapper from 'src/components/form/WrapperWithLabel';
import { useContext, useState } from 'react';
import ForgotPassword from './conponents/ForgotPassword';
import { yupResolver } from '@hookform/resolvers/yup';
import { schemaLogin as schema } from '../schema';
import { LoginInputs } from '../types';
import { AuthContext } from 'src/contexts/AuthContext';

export default function Login() {
  const { loginUser } = useContext(AuthContext);
  const navigate = useNavigate();
  const { error, run: runLogin } = useAsync(login);
  const {
    watch,
    register,
    handleSubmit,
    formState: { errors }
  } = useForm<LoginInputs>({
    resolver: yupResolver(schema)
  });

  const watchEmail = watch('email');

  const [isForgotPasswordVisible, setIsForgotPasswordVisible] = useState(false);

  const onSubmit = async (data: LoginInputs) => {
    runLogin(data).then((result) => {
      loginUser(result);
      navigate(ROUTES.DASHBOARD, { replace: true });
    });
  };

  return (
    <>
      {isForgotPasswordVisible ? (
        <ForgotPassword
          onClose={() => setIsForgotPasswordVisible(false)}
          defaultEmail={watchEmail}
        />
      ) : (
        <form onSubmit={handleSubmit(onSubmit)} className="w-4/6 2xl:mt-[1vw]">
          <div className="flex flex-col items-start gap-6">
            <LabeledInputWrapper
              label="Username"
              error={errors.email?.message}
              className="w-full"
            >
              <Input placeholder="Email" {...register('email')} />
            </LabeledInputWrapper>
            <LabeledInputWrapper
              label="Password"
              error={errors.password?.message}
              className="w-full"
            >
              <Input
                type="password"
                placeholder="Password"
                {...register('password')}
              />

              <div>
                <button
                  type="button"
                  onClick={() => setIsForgotPasswordVisible(true)}
                  className="opacity-70 text-lg"
                >
                  Forgot your password?
                </button>
              </div>
            </LabeledInputWrapper>
          </div>

          <div>
            <ErrorText error={error} />

            <div className="m-auto w-1/3 mt-4">
              <Button
                type="submit"
                color={ButtonColor.Light}
                className="w-full px-1"
              >
                Login
              </Button>
            </div>
          </div>
        </form>
      )}
    </>
  );
}
