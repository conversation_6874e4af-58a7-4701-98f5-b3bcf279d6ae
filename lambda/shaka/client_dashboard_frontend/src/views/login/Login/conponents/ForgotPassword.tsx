import { yupResolver } from '@hookform/resolvers/yup';
import { useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { sendForgotPassword } from 'src/api/auth';
import { useAsync } from 'src/api/useApi';
import Button, { ButtonColor } from 'src/components/Button';
import ErrorText from 'src/components/ErrorText';
import Input from 'src/components/form/Input';
import LabeledInputWrapper from 'src/components/form/WrapperWithLabel';
import Typography from 'src/components/Typography';
import { ROUTES } from 'src/config/navigation';
import { schemaForgotPassword as schema } from '../../schema';
import { ForgotPasswordInputs } from '../../types';

export default function ForgotPassword({
  onClose,
  defaultEmail
}: {
  onClose: () => void;
  defaultEmail?: string;
}) {
  const navigate = useNavigate();
  const {
    register,
    handleSubmit,
    formState: { errors }
  } = useForm<ForgotPasswordInputs>({
    resolver: yupResolver(schema),
    defaultValues: { email: defaultEmail }
  });

  const { run: runSendForgotPassword, error } = useAsync(sendForgotPassword);

  const onSubmit = (data: any) => {
    runSendForgotPassword(data).then(() => {
      navigate(ROUTES.FORGOT_PASSWORD, { state: { email: data.email } });
    });
  };
  return (
    <div className="max-w-[450px] w-3/5 mt-[3vw]">
      <Typography>
        No worries, we’ll send you a magic link, just enter the email you used
        to sign up with below:
      </Typography>

      <form
        className="space-y-10 mt-10 w-full"
        onSubmit={handleSubmit(onSubmit)}
      >
        <LabeledInputWrapper
          label="Email"
          error={errors.email?.message}
          className="w-full"
        >
          <Input {...register('email')} placeholder="Enter your email" />
        </LabeledInputWrapper>

        <ErrorText>{error}</ErrorText>
        <div className="flex gap-4 justify-center">
          <Button color={ButtonColor.Default} onClick={onClose}>
            Return
          </Button>

          <Button type="submit" color={ButtonColor.Light}>
            Send link
          </Button>
        </div>
      </form>
    </div>
  );
}
