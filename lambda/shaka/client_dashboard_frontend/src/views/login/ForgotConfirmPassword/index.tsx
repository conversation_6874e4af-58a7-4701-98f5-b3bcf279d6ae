import { yupResolver } from '@hookform/resolvers/yup';
import { useForm } from 'react-hook-form';
import { useLocation, useNavigate } from 'react-router-dom';
import { confirmForgotPassword } from 'src/api/auth';
import { useAsync } from 'src/api/useApi';
import Button, { ButtonColor } from 'src/components/Button';
import ErrorText from 'src/components/ErrorText';
import Input from 'src/components/form/Input';
import Link from 'src/components/Link';
import Typography from 'src/components/Typography';
import { ROUTES } from 'src/config/navigation';
import { schemaResetPassword as schema } from '../schema';
import LabeledInputWrapper from 'src/components/form/WrapperWithLabel';
import { ResetPasswordInputs } from '../types';

export default function ForgotConfirmPassword() {
  const navigate = useNavigate();
  const { state } = useLocation();
  const email = state?.email || '';

  const { run: runConfirmForgotPassword, error: requestError } = useAsync(
    confirmForgotPassword
  );
  const {
    handleSubmit,
    register,
    formState: { errors }
  } = useForm<ResetPasswordInputs>({
    resolver: yupResolver(schema),
    defaultValues: {
      email: email,
      verification_code: '',
      new_password: '',
      new_password_confirmation: ''
    }
  });

  const error =
    requestError ||
    errors.verification_code?.message ||
    errors.email?.message ||
    errors.new_password?.message ||
    errors.new_password_confirmation?.message;

  const onSubmit = (data: ResetPasswordInputs) => {
    runConfirmForgotPassword(data).then(() => {
      navigate(ROUTES.LOGIN);
    });
  };

  return (
    <div className="2xl:mt-[1vw]">
      <Typography>
        There you&apos;ll find a verification code, enter it below:
      </Typography>
      <form className="space-y-4 mt-4" onSubmit={handleSubmit(onSubmit)}>
        <LabeledInputWrapper className="w-full">
          <Input
            {...register('verification_code')}
            placeholder="Verification code"
          />
        </LabeledInputWrapper>

        <LabeledInputWrapper className="w-full">
          <Input {...register('email')} placeholder="Email" />
        </LabeledInputWrapper>

        <LabeledInputWrapper className="w-full">
          <Input
            {...register('new_password')}
            placeholder="New password"
            type="password"
          />
        </LabeledInputWrapper>

        <LabeledInputWrapper className="w-full">
          <Input
            {...register('new_password_confirmation')}
            placeholder="Confirm new password"
            type="password"
          />
        </LabeledInputWrapper>
        <div className="max-w-[450px]">
          <ErrorText>{error}</ErrorText>
        </div>
        <div className="pt-4">
          <div className="flex gap-4 justify-center">
            <Link to={ROUTES.LOGIN}>Go to login</Link>
            <Button type="submit" color={ButtonColor.Light}>
              Submit
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
}
