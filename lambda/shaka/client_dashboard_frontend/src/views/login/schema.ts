import * as yup from 'yup';
import {
  ForgotPasswordInputs,
  LoginInputs,
  ResetPasswordInputs
} from './types';

const emailScheme = yup
  .string()
  .email('Email is invalid')
  .required('No email provided.');

const passwordScheme = yup
  .string()
  .required('No password provided.')
  .matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])(?=.{8,})/,
    'Must Contain 8 Characters, One Uppercase, One Lowercase, One Number and One Special Case Character'
  );

const schemaLogin = yup
  .object({
    email: emailScheme,
    password: passwordScheme
  })
  .required() as yup.ObjectSchema<LoginInputs>;

const schemaForgotPassword = yup
  .object({
    email: emailScheme
  })
  .required() as yup.ObjectSchema<ForgotPasswordInputs>;

const schemaResetPassword = yup
  .object({
    new_password: passwordScheme,
    new_password_confirmation: passwordScheme.oneOf(
      [yup.ref('new_password')],
      'Passwords must match'
    ),
    verification_code: yup.string().required('No verification code provided'),
    email: emailScheme
  })
  .required() as yup.ObjectSchema<ResetPasswordInputs>;

export { schemaLogin, schemaForgotPassword, schemaResetPassword };
