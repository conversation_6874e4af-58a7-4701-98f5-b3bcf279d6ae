import * as yup from 'yup';

export type Inputs = {
  email: string;
  name: string;
  old_password?: string;
  new_password?: string;
  new_password_confirmation?: string;
};

const passwordScheme = yup
  .string()
  .required('No password provided.')
  .matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])(?=.{8,})/,
    'Must Contain 8 Characters, One Uppercase, One Lowercase, One Number and One Special Case Character'
  );

export const schema = yup
  .object({
    email: yup.string().required('Email is required').email(),
    name: yup.string().optional(),
    old_password: yup.string().required('Old password is required'),

    new_password: passwordScheme.when(
      'old_password',
      (old_password, schema) => {
        return schema.test({
          test: (new_password) =>
            Boolean(new_password) &&
            Boolean(old_password) &&
            new_password !== old_password[0],
          message: "Passwords shouldn't match"
        });
      }
    ),
    new_password_confirmation: passwordScheme.when(
      'new_password',
      (new_password, schema) => {
        return schema.test({
          test: (new_password_confirmation) =>
            Boolean(new_password_confirmation) &&
            Boolean(new_password) &&
            new_password_confirmation === new_password[0],
          message: 'Passwords must match'
        });
      }
    )
  })
  .required() as yup.ObjectSchema<Inputs>;
