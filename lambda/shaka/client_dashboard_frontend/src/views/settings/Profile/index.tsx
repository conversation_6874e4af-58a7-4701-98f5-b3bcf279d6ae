import Card from 'src/components/Card';
import { SubmitHandler, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import LabeledInputWrapper from 'src/components/form/WrapperWithLabel';
import Input from 'src/components/form/Input';
import Button, { ButtonColor } from 'src/components/Button';
import { useContext, useEffect } from 'react';
import { AuthContext } from 'src/contexts/AuthContext';
import { Inputs, schema } from './schema';
import { useAsync } from 'src/api/useApi';
import { changePassword } from 'src/api/auth';

const Profile = () => {
  const {
    user: { email }
  } = useContext(AuthContext);

  const { handleSubmit, reset, register } = useForm<Inputs>({
    resolver: yupResolver(schema)
  });
  const { logoutUser } = useContext(AuthContext);

  const { run: runChangePassword } = useAsync(changePassword);

  useEffect(() => {
    reset({
      email,
      name: email
    });
  }, []);

  const onSubmit: SubmitHandler<Inputs> = (data) => {
    runChangePassword({
      old_password: data.old_password!,
      new_password: data.new_password!
    }).then(() => {
      logoutUser();
    });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Card
        title="Profile"
        bottomActions={
          <>
            <Button
              type="submit"
              color={ButtonColor.Pink}
              className="hover:opacity-70"
            >
              Save
            </Button>
          </>
        }
        fullHeight
      >
        <div className="space-y-8">
          <LabeledInputWrapper
            label="Name"
            description="This is displayed throughout this application"
          >
            <Input {...register('name')} disabled />
          </LabeledInputWrapper>

          <LabeledInputWrapper
            label="Email"
            description="This is used to sign in and to receive notifications"
          >
            <Input {...register('email')} disabled />
          </LabeledInputWrapper>

          <LabeledInputWrapper
            label="Change Password"
            description="This is used to sign in to your account and when completing high security actions"
          >
            <div className="w-1/2 space-y-4">
              <Input {...register('old_password')} placeholder="Old password" />
              <Input {...register('new_password')} placeholder="New password" />
              <Input
                {...register('new_password_confirmation')}
                placeholder="Confirm new password"
              />
            </div>
          </LabeledInputWrapper>
        </div>
      </Card>
    </form>
  );
};

export default Profile;
