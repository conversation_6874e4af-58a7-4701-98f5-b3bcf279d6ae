import { createColumnHelper } from '@tanstack/react-table';
import Table from 'src/components/Table';
import { roleLabels, USER_COLUMNS } from './constants';
import { User } from 'src/types/users';
import Link, { LinkColor } from 'src/components/Link';
import { getUserEditRoute } from 'src/config/navigation';

type Props = {
  data: User[];
};

const columnHelper = createColumnHelper<User & { action?: any }>();
const columns = [
  columnHelper.accessor('email', {
    cell: (info) => <div className="text-left">{info.getValue()}</div>,
    header: () => USER_COLUMNS.email
  }),
  columnHelper.accessor('role', {
    cell: (info) => roleLabels[info.getValue()],
    header: () => USER_COLUMNS.role
  }),

  columnHelper.accessor('action', {
    cell: (info) => (
      <div className="text-right">
        <Link
          to={getUserEditRoute(info.row.original.id)}
          color={LinkColor.Helper}
        >
          Edit
        </Link>
      </div>
    ),
    header: () => USER_COLUMNS.action
  })
];

export default function UsersTable({ data }: Props) {
  return <Table columns={columns} data={data} />;
}
