import { useMemo } from 'react';
import { Link, LinkProps } from 'react-router-dom';
import Status, { StatusColor } from 'src/components/Status';
import Typography, { TypographySize } from 'src/components/Typography';
import { BeamStatus } from 'src/types/sms';
import Warning from '../Warning';

const getTruncatedMessage = (message: string) =>
  message.length > 320 ? message.slice(0, 320) + '...' : message;

interface Props {
  message: string;
  title: string;
  publishingTime: React.ReactNode;
  to: LinkProps['to'];
  status?: BeamStatus;
  deliveredAmount?: number;
}

export default function Beam({
  message,
  publishingTime,
  title,
  to,
  status,
  deliveredAmount = 0
}: Props) {
  const hasError = status === BeamStatus.ERRORED;
  const truncatedMessage = useMemo(
    () => getTruncatedMessage(message),
    [message]
  );

  return (
    <Link to={to} className="block">
      <div className="min-h-[200px] grid grid-cols-5 gap-6 p-8 bg-[length:100%_100%] bg-[url('/engage/beam-bg.png')] rounded-2xl">
        <div className="relative col-span-2 pr-6">
          <div className="bg-black/[.08] rounded-2xl px-8 py-6 h-full">
            <Typography>{truncatedMessage}</Typography>
          </div>
          <span className="absolute -my-4 top-0 right-0 bottom-0 w-[1px] bg-gradient-divider"></span>
        </div>
        <div className="col-span-3 flex flex-col self-stretch">
          <div className="mb-4">
            <Typography size={TypographySize.Title}>{title}</Typography>
          </div>
          <div className="grow">
            {hasError ? (
              <Warning />
            ) : (
              <div className="flex flex-col gap-4 mb-4">
                <Typography>{publishingTime}</Typography>
                {Boolean(deliveredAmount) && (
                  <Typography>
                    Delivered to {deliveredAmount} subscribers
                  </Typography>
                )}
              </div>
            )}
          </div>
          <div className="flex gap-4">
            <Status status="Text message" color={StatusColor.Success} />
            <Status status="WhatsApp" color={StatusColor.Error} />
          </div>
        </div>
      </div>
    </Link>
  );
}
