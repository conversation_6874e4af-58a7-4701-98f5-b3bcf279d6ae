import { useForm<PERSON>ontext, Controller } from 'react-hook-form';
import ComboboxControl from 'src/components/form/ComboboxControl';

const fontOptions = [
  {
    label: 'Inter',
    value: 'Inter'
  },
  {
    label: 'Baloo 2',
    value: 'Baloo 2'
  }
];

export default function BrandingFonts() {
  const { control } = useFormContext();

  return (
    <div>
      <h3 className="text-navy-blue font-medium">Fonts</h3>
      <div className="py-6">
        <Controller
          name="headings_font"
          control={control}
          render={({ field, fieldState }) => (
            <>
              <ComboboxControl<string>
                label="Headings"
                options={fontOptions}
                classes={{
                  label: 'text-navy-blue'
                }}
                value={field.value}
                onChange={field.onChange}
              />
              {fieldState.error && (
                <div className="text-red-700">{fieldState.error.message}</div>
              )}
              <div
                className="text-3xl"
                style={{
                  fontFamily:
                    field.value === 'Baloo 2' ? '"Baloo 2"' : field.value
                }}
              >
                CLIENT mobile network powered by shaka
              </div>
            </>
          )}
        />
      </div>
      <div className="py-6">
        <Controller
          name="paragraph_font"
          control={control}
          render={({ field, fieldState }) => (
            <>
              <ComboboxControl<string>
                label="Paragraph"
                options={fontOptions}
                classes={{
                  label: 'text-navy-blue'
                }}
                value={field.value}
                onChange={field.onChange}
              />
              {fieldState.error && (
                <div className="text-red-700">{fieldState.error.message}</div>
              )}
              <div
                className="text-3xl"
                style={{
                  fontFamily:
                    field.value === 'Baloo 2' ? '"Baloo 2"' : field.value
                }}
              >
                CLIENT mobile network powered by shaka
              </div>
            </>
          )}
        />
      </div>
    </div>
  );
}
