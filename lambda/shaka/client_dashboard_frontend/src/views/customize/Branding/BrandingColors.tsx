import _ from 'lodash';
import { useFormContext, Controller } from 'react-hook-form';
import ColorPicker from 'src/components/form/ColorPicker';

const labels = {
  primary_color: 'primary',
  secondary_color: 'secondary',
  accent_color: 'accent',
  text_color: 'text',
  link_color: 'link'
};

export default function BrandingColors() {
  const { control } = useFormContext();

  return (
    <div className="pt-14">
      <h3 className="text-navy-blue font-medium pb-6">Color Palette</h3>
      {_.map(labels, (label, name) => (
        <Controller
          key={name}
          name={name}
          control={control}
          render={({ field, fieldState }) => {
            return (
              <>
                <div className="w-full xl:w-1/2 flex items-center justify-between py-1">
                  <div>{label} </div>
                  <ColorPicker value={field.value} onChange={field.onChange} />
                </div>
                {fieldState.error && (
                  <div className="text-red-700">{fieldState.error.message}</div>
                )}
              </>
            );
          }}
        />
      ))}
    </div>
  );
}
