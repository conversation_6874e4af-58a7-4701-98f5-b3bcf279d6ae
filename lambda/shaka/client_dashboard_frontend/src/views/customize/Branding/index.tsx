import { useContext, useEffect, useMemo, useState } from 'react';
import { useForm, FormProvider, SubmitHandler } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import _ from 'lodash';
import Card from 'src/components/Card';
import BrandingLogo from './BrandingLogo';
import { BrandingContext } from 'src/contexts/BrandingContext';
import {
  createBranding,
  updateBranding,
  BrandingPayload
} from 'src/api/branding';
import { useAsync } from 'src/api/useApi';
import ValidationErrorsBox from 'src/components/ValidationErrorsBox';
import { BrandingFormData } from './constants';
import Button, { ButtonColor } from 'src/components/Button';
import useClientData from 'src/hooks/useClient';

const schema = yup
  .object({
    logo: yup.string().required('Logo is required')
  })
  .required();

export default function BrandingView() {
  const { clientData, setClientData } = useClientData();
  const [isDataSaving, setIsDataSaving] = useState(false);
  const {
    isLoading: isLoadingBranding,
    branding,
    setBranding
  } = useContext(BrandingContext);

  const { run: doCreateBranding, error: createError } =
    useAsync(createBranding);
  const { run: doUpdateBranding, error: updateError } =
    useAsync(updateBranding);

  const validationErrors = useMemo<{
    [key: string]: string[];
  }>(() => {
    if (createError) return createError;
    if (updateError) return updateError;

    return null;
  }, [createError, updateError]);

  const form = useForm<BrandingFormData>({
    // @ts-expect-error now resolver no need to handle unused form fields
    resolver: yupResolver(schema),
    // TODO: now this values are required in API, so we need to set them to default values
    defaultValues: {
      headings_font: 'Baloo 2',
      paragraph_font: 'Inter',
      primary_color: '#2F548D',
      secondary_color: '#516FA6',
      accent_color: '#F18DFA',
      text_color: '#2E215C',
      link_color: '#F57CA3',
      logo: ''
    }
  });
  const { handleSubmit, setError, setValue } = form;

  const onSubmit: SubmitHandler<BrandingFormData> = (data) => {
    const payload: BrandingPayload = {
      ...data,
      client: clientData.id
    };

    setIsDataSaving(true);

    if (clientData.branding) {
      doUpdateBranding(clientData.branding, payload)
        .then((res) => {
          setBranding(res);
          setIsDataSaving(false);
          setClientData({
            ...clientData,
            branding: `${import.meta.env.VITE_BASE_URL}/api/clientbrandings/${
              res.id
            }/`
          });
        })
        .catch(() => {});
    } else {
      doCreateBranding(payload)
        .then((res) => {
          setBranding(res);
          setIsDataSaving(false);
          setClientData({
            ...clientData,
            branding: `${import.meta.env.VITE_BASE_URL}/api/clientbrandings/${
              res.id
            }/`
          });
        })
        .catch();
    }
  };

  const updateValues = (fields: BrandingFormData) => {
    _.forEach(fields, (value: string, field) => {
      setValue(field as keyof BrandingFormData, value);
    });
  };

  useEffect(() => {
    if (branding) {
      updateValues(_.omit(branding, ['id', 'client']));
    }
  }, [branding]);

  useEffect(() => {
    if (validationErrors) {
      _.forEach(validationErrors, (value, key) => {
        if (key !== 'non_field_errors') {
          setError(key as keyof BrandingFormData, {
            type: 'server',
            message: value[0]
          });
        }
      });
    }
  }, [validationErrors]);

  return (
    <FormProvider {...form}>
      <form onSubmit={handleSubmit(onSubmit)} className="h-full">
        <Card
          title="Brand Guidelines"
          isLoading={isLoadingBranding}
          actions={
            <Button
              type="submit"
              isLoading={isDataSaving}
              color={ButtonColor.Pink}
            >
              Save
            </Button>
          }
          fullHeight
        >
          {validationErrors?.non_field_errors && (
            <ValidationErrorsBox errors={validationErrors.non_field_errors} />
          )}

          <BrandingLogo />
        </Card>
      </form>
    </FormProvider>
  );
}
