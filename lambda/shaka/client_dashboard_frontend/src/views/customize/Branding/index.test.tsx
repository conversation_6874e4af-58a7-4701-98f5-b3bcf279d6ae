import { beforeEach, describe, expect, it, vi } from 'vitest';
import { act, fireEvent, render, waitFor } from '@testing-library/react';
import api from 'src/api';
import BrandingView from '.';
import { AuthContext } from 'src/contexts/AuthContext';
import { BrandingContext } from 'src/contexts/BrandingContext';

const testAuth = {
  accessToken: '',
  setAccessToken: () => {},
  parseToken: () => {},
  deleteUser: () => {},
  clientData: {
    id: 0,
    auth_app_client_id: '',
    auth_app_domain_prefix: '',
    auth_app_redirect_uri: '',
    authorisation_url: '',
    name: '',
    user_pool_id: '',
    branding: null
  },
  setClientData: () => {}
};

describe('Branding', () => {
  let wrapper: any;
  let fetchBrandingUrl: string;

  beforeEach(() => {
    const baseUrl = import.meta.env.VITE_BASE_URL;
    const clientBrandingId = 1;
    fetchBrandingUrl = `${baseUrl}/api/clientbrandings/${clientBrandingId}/`;
  });

  it('does logo required validation', async () => {
    const apiGet = api.get;

    api.get = vi.fn().mockImplementation((url: string) => {
      if (url === fetchBrandingUrl) return Promise.resolve(null);
      return Promise.reject('failed');
    });

    wrapper = render(
      <AuthContext.Provider value={testAuth}>
        <BrandingContext.Provider
          value={{
            branding: null,
            setBranding: () => {},
            isLoading: false
          }}
        >
          <BrandingView />
        </BrandingContext.Provider>
      </AuthContext.Provider>
    );

    const { container } = wrapper;

    await waitFor(() => {
      expect(container.querySelector('button[type="submit"]')).not.toBeNull();
    });

    const saveButton = container.querySelector('button[type="submit"]');

    await act(() => {
      fireEvent.click(saveButton as Element);
    });

    const validationMsg = await wrapper.findByText('Logo is required');
    expect(validationMsg).toBeInTheDocument();

    api.get = apiGet;
  });

  it('shows validation errors from the server response', async () => {
    const apiGet = api.get;
    const apiPut = api.put;

    api.put = vi.fn().mockImplementation(() => {
      return Promise.reject({
        response: {
          data: {
            logo: ['This field is required.']
          }
        }
      });
    });

    wrapper = render(
      <AuthContext.Provider
        value={{
          ...testAuth,
          clientData: {
            ...testAuth.clientData,
            branding: fetchBrandingUrl
          },
          setClientData: () => {}
        }}
      >
        <BrandingContext.Provider
          value={{
            branding: {
              id: 10,
              headings_font: 'Baloo 2',
              paragraph_font: 'Inter',
              primary_color: '#000000',
              secondary_color: '#516FA6',
              accent_color: '#F18DFA',
              text_color: '#2E215C',
              link_color: '#F57CA3',
              logo: 'default logo',
              client: 1
            },
            setBranding: () => {},
            isLoading: false
          }}
        >
          <BrandingView />
        </BrandingContext.Provider>
      </AuthContext.Provider>
    );

    const { container } = wrapper;

    await waitFor(() => {
      expect(container.querySelector('button[type="submit"]')).not.toBeNull();
    });

    const saveButton = container.querySelector('button[type="submit"]');

    await act(() => {
      fireEvent.click(saveButton as Element);
    });

    const validationMsg = await wrapper.findByText('This field is required.');

    await waitFor(() => {
      expect(validationMsg).toBeInTheDocument();
    });

    api.get = apiGet;
    api.put = apiPut;
  });
});
