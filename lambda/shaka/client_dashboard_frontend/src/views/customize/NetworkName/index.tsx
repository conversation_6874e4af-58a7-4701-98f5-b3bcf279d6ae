import React, { useContext, useEffect } from 'react';
import Button, { ButtonColor } from 'src/components/Button';
import Card from 'src/components/Card';
import Typography, {
  TypographyShade,
  TypographySize
} from 'src/components/Typography';
import Input from 'src/components/form/Input';
import LabeledInputWrapper from 'src/components/form/WrapperWithLabel';
import { ChartBarIcon } from '@heroicons/react/16/solid';
import { updateSpn } from 'src/api/spn';
import { SpnContext } from 'src/contexts/SpnContext';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useAsync } from 'src/api/useApi';
import useClientData from 'src/hooks/useClient';

const schema = yup
  .object({
    spn: yup.string().required('Name is required')
  })
  .required();

const NetworkName: React.FC = () => {
  const { clientData } = useClientData();
  const { spn, isLoading, setSpn } = useContext(SpnContext);
  const { run: doUpdateSpn } = useAsync(updateSpn);

  const {
    handleSubmit,
    register,
    watch,
    reset,
    formState: { errors }
  } = useForm<{ spn: string }>({
    resolver: yupResolver(schema)
  });
  const spnField = watch('spn');
  const truncatedSpn =
    spnField?.length > 10 ? `${spnField?.slice(0, 10)}...` : spnField;

  useEffect(() => {
    reset({ spn: spn || '' });
  }, [spn]);

  const onSubmit = (data: { spn: string }) => {
    doUpdateSpn(clientData.id, { spn: data.spn || '' }).then(() =>
      setSpn(data.spn)
    );
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Card
        title="Service Provider Name"
        isLoading={isLoading}
        actions={
          <Button
            type="submit"
            color={ButtonColor.Pink}
            className="hover:opacity-70"
          >
            Save
          </Button>
        }
      >
        <div className="flex md:gap-28 max-md:flex-wrap">
          <div className="md:w-1/2">
            <LabeledInputWrapper
              label="Network name"
              error={errors.spn?.message}
            >
              <Input {...register('spn')} />
            </LabeledInputWrapper>
            <div className="mt-12">
              <Typography
                size={TypographySize.Caption}
                shade={TypographyShade.Light}
              >
                Note, changing your network name can take up to 14 days to fully
                take effect. Speak with your account manager for more
                information.
              </Typography>
            </div>
          </div>

          <div className="mr-10">
            <div className="relative w-[366px] mx-auto">
              <div className="absolute top-[28px] left-[42px] right-[42px] flex justify-between items-center gap-8 h-8">
                <div className="max-w-[80px] overflow-hidden truncate text-sm">
                  <Typography truncate size={TypographySize.Inherit}>
                    {truncatedSpn}
                  </Typography>
                </div>
                <ChartBarIcon width={16} className="opacity-50" />
              </div>
              <img src="/phone-example.png" alt="Phone Example" className="" />
            </div>
          </div>
        </div>
      </Card>
    </form>
  );
};

export default NetworkName;
