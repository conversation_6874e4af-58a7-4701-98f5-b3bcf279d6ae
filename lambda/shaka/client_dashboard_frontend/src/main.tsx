import React from 'react';
import ReactD<PERSON> from 'react-dom/client';
import Swiper<PERSON>ore from 'swiper';
import { Controller, Navigation } from 'swiper/modules';
import App from './App.tsx';
import 'swiper/css';
import 'swiper/css/navigation';
import 'react-modern-drawer/dist/index.css';
import './index.css';
import ReactGA from "react-ga4";

SwiperCore.use([Controller, Navigation]);

if (import.meta.env.VITE_GOOGLE_TRACKING_ID) {
  ReactGA.initialize(import.meta.env.VITE_GOOGLE_TRACKING_ID);
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
/* Trigger a deploy */
