import { useMemo, useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import clsx from 'clsx';
import {
  disabledSidebarItems,
  sidebarConfigs,
  SidebarItem
} from 'src/config/navigation';
import Icons from 'src/icons';
import Typography, {
  TypographyColor,
  TypographyShade
} from 'src/components/Typography';
import ComingSoonCover from 'src/components/ComingSoonCover';
import { InfoBigIcon } from 'src/icons/InfoBig';
import { tipContent } from 'src/config/tips';
import { ActionsBg } from 'src/assets/actions-bg';

const MenuItem = ({ item }: { item: SidebarItem }) => {
  const [isHovered, setIsHovered] = useState(false);
  const { pathname } = useLocation();
  const Icon = Icons[item.icon];

  const isActive =
    pathname === item.path ||
    (item.path.split('/').length > 2 && pathname.includes(item.path));

  return (
    <Link
      onMouseOver={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      className={clsx(
        'flex gap-10 items-center px-8 py-5 rounded-l-2xl hover:cursor-pointer',
        {
          'opacity-70 hover:opacity-100 hover:text-white': !isActive,
          'text-white bg-[url("/sidebar-bg.png")] bg-full': isActive
        }
      )}
      to={item.path}
    >
      {Icon && (
        <span className="w-6 h-6 flex items-center">
          <Icon fill={isActive ? 'url(#grad1)' : 'currentColor'} />
        </span>
      )}
      <Typography
        shade={isActive || isHovered ? undefined : TypographyShade.Dark}
        color={TypographyColor.Inherit}
      >
        {item.title}
      </Typography>

      <svg height="0" width="0" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="grad1" x1="0%" y1="100%" x2="100%" y2="0%">
            <stop offset="20%" stopColor="#C468CC" />
            <stop offset="52%" stopColor="#607BB5" />
            <stop offset="100%" stopColor="#744A96" />
          </linearGradient>
        </defs>
      </svg>
    </Link>
  );
};

export default function Sidebar() {
  const { pathname } = useLocation();

  const tip = useMemo(() => {
    const tipKey = Object.keys(tipContent).find((key) =>
      pathname.includes(key)
    );

    if (!tipKey) {
      return null;
    }

    return tipContent[tipKey as keyof typeof tipContent];
  }, [pathname]);

  const sidebarConfig = useMemo(() => {
    const config = sidebarConfigs.find((item) => {
      return new RegExp('^' + item.prefix).test(pathname);
    });

    return config || sidebarConfigs[0];
  }, [pathname]);

  if (!sidebarConfig.nav) {
    return null;
  }

  return (
    <div className="2xl:flex-sidebar-long flex-sidebar-short relative">
      <div className="w-full h-full">
        {sidebarConfig.nav.map((item) =>
          disabledSidebarItems.includes(item.id) ? (
            <ComingSoonCover key={item.id} content="Coming soon" disabledChild>
              <MenuItem item={item} />
            </ComingSoonCover>
          ) : (
            <MenuItem key={item.id} item={item} />
          )
        )}

        {tip && (
          <div
            className={clsx(
              'w-[95%] overflow-hidden',
              'absolute top-[50vh]',
              'p-7 rounded-xl space-y-8'
            )}
          >
            <div className="flex justify-between items-center">
              <Typography trackingWide={4}>{tip.title}</Typography>
              <InfoBigIcon />
            </div>
            <Typography as="p">{tip.description}</Typography>
            <ActionsBg className="absolute inset-0 z-[-1]" />
          </div>
        )}
      </div>
      <span className="absolute top-0 right-0 h-full w-[1px] bg-gradient-divider" />
    </div>
  );
}
