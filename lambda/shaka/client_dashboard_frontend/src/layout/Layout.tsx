import { Outlet } from 'react-router-dom';
import Header from './Header';
import Sidebar from './Sidebar';
import Footer from './Footer';
import useClientData from 'src/hooks/useClient';
import Loader from 'src/components/Loader';

const Layout = () => {
  const { isLoading: clientLoading } = useClientData({ fetchOnMount: true });

  if (clientLoading) {
    return (
      <div className="w-screen h-screen">
        <Loader />
      </div>
    );
  }

  return (
    <div className="xl:px-20 px-10 py-4 pt-10 flex flex-col min-h-[100vh]">
      <Header />
      <div className="flex flex-col grow lg:flex-row xl:gap-20 gap-10 w-full max-w-[1960px] m-auto trigger-shaka">
        <Sidebar />

        <main className="grow transition-all">
          <Outlet />
        </main>
      </div>

      <Footer />
    </div>
  );
};

export default Layout;
