import { Outlet } from 'react-router-dom';
import { Shak<PERSON><PERSON>ogo } from 'src/assets/ShakaLogo';

export default function LoginLayout() {
  return (
    <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 p-5 rounded-xl bg-white/[0.03] bg-gradient-card-4 flex max-w-[1280px] w-[70vw] min-h-[516px] 2xl:min-h-[650px] ">
      <div className="grow h-full max-h-[718px] 2xl:w-1/3 w-2/5 self-center">
        <img
          src="/login-image.png"
          alt="login"
          className="w-full h-full object-contain object-center"
        />
      </div>

      <div className="flex flex-col items-center w-2/3 2xl:mt-[6vh] mt-5 pb-6">
        <h1 className="2xl:mb-[4vh] mb-10 text-center">
          <ShakaLogo className="max-w-[150px] w-[10vw]" />
        </h1>
        <Outlet />
      </div>
    </div>
  );
}
