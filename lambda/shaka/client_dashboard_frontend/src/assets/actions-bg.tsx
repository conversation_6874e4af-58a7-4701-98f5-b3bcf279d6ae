export const ActionsBg = (props: { className?: string }) => {
  return (
    <svg
      viewBox="0 0 423 366"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      style={{ transform: 'scale(1.25, 2)' }}
      {...props}
    >
      <g filter="url(#blur2)">
        <ellipse cx="288.5" cy="100.5" rx="104.5" ry="33.5" fill="#607BB5" />
        <rect
          x="55"
          y="311"
          width="230"
          height="34"
          fill="#644189"
          fillOpacity="0.7"
        />
      </g>
      <g filter="url(#filter0_b_0_1)">
        <rect
          width="423"
          height="366"
          rx="21.9057"
          fill="black"
          fillOpacity="0.22"
        />
      </g>
      <defs>
        <filter id="blur2">
          <feGaussianBlur stdDeviation="65" />
        </filter>
        <filter
          id="filter0_b_0_1"
          x="-152.609"
          y="-152.609"
          width="728.219"
          height="671.219"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="76.3047" />
          <feComposite
            in2="SourceAlpha"
            operator="in"
            result="effect1_backgroundBlur_0_1"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_backgroundBlur_0_1"
            result="shape"
          />
        </filter>
      </defs>
    </svg>
  );
};
