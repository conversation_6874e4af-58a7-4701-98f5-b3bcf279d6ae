export type CampaignType = 'discount';

export enum Eligibility {
  NEW_SUBSCRIBERS = 'new_subscribers',
  SUBSCRIBER_ACTIVITY = 'subscriber_activity'
}

export type DiscountType = 'percentage' | 'flat';

export type Campaign = {
  id: number;
  title: string;
  client: number;
  marketing_eligibility: {
    eligibility_type: Eligibility;
    active_since?: string | null;
    active_duration_months?: number;
  };
  campaign_type: CampaignType;
  start_date: string;
  end_date: string;
  status: string;
  plan_discounts: {
    id?: number;
    campaign?: number;
    plan: number;
    discount_type: DiscountType;
    discount_percentage?: string | null;
    discount_amount?: string | null;
    discount_duration_months?: number;
  }[];
  discount_type_summary?: string;
  discount_value_summary?: string;
};
