type MetricFormat = 'percentage' | 'currency';
type MetricsKeys =
  | 'profit-growth'
  | 'profit-per-subscriber'
  | 'profit-per-period'
  | 'lifetime-value'
  | 'run-rate';

export type MetricsData = {
  date: string;
  key: string;
  name: string;
  values: {
    value: number | null;
    context: string;
    format: MetricFormat;
    primary: boolean;
    description: string;
    is_extrapolated: boolean;
  }[];
};

export type Metrics = Record<MetricsKeys, Record<string, MetricsData>>;
