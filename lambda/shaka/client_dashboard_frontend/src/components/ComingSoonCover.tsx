import clsx from 'clsx';
import { useState, useRef } from 'react';
import Typography from './Typography';

interface CoverProps {
  children: React.ReactNode;
  content: React.ReactNode;
  disabledChild?: boolean;
  fullSize?: boolean;
  debounced?: boolean;
}

const tooltipStyle =
  'absolute shadow-md inset-0 flex justify-center items-center';

export default function ComingSoonCover({
  children,
  content,
  fullSize,
  debounced,
  disabledChild
}: CoverProps) {
  const [showCover, setShowCover] = useState(false);
  const debounceTimeoutRef = useRef<number | null>(null);

  const handleMouseEnter = () => {
    debounceTimeoutRef.current = setTimeout(
      () => {
        setShowCover(true);
      },
      debounced ? 200 : 0
    );
  };

  const handleMouseLeave = () => {
    setShowCover(false);

    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }
  };

  return (
    <div
      className={clsx('relative', fullSize && 'w-full h-full')}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div
        className={clsx(
          disabledChild && 'pointer-events-none opacity-50 ',
          fullSize && 'w-full h-full'
        )}
      >
        {children}
      </div>
      {showCover && (
        <div
          className={clsx(
            'pointer-events-none',
            tooltipStyle,
            fullSize ? 'bg-gradient-tooltip-2' : 'bg-gradient-tooltip-1'
          )}
        >
          <Typography semibold>{content}</Typography>
        </div>
      )}
    </div>
  );
}
