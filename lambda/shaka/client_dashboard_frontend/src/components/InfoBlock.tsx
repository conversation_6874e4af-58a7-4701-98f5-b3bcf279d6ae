import Typography, {
  TypographyColor,
  TypographySize
} from 'src/components/Typography';
import InfoCard from './InfoCard';

interface InfoBlockProps {
  title: string;
  value: JSX.Element | string | number;
  fontSize?: TypographySize;
  titleColor?: TypographyColor;
  bold?: boolean;
}

const InfoBlock = ({
  title,
  value,
  fontSize = TypographySize.CardTitle,
  titleColor = TypographyColor.Primary,
  bold
}: InfoBlockProps) => {
  return (
    <InfoCard classes="h-full">
      <div className="text-center">
        <Typography size={fontSize} bold={bold}>
          {value}
        </Typography>
      </div>
      <div className="absolute bottom-5 left-0 w-full text-center">
        <Typography size={TypographySize.BodyS} color={titleColor} uppercase>
          {title}
        </Typography>
      </div>
    </InfoCard>
  );
};

export default InfoBlock;
