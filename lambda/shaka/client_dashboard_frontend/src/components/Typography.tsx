import clsx from 'clsx';
import type { PropsWithChildren, Ref } from 'react';
import { createElement, forwardRef } from 'react';

export enum TypographySize {
  Title = 'Title',
  Subtitle = 'Subtitle',
  BodyM = 'BodyM',
  BodyS = 'BodyS',
  Caption = 'Caption',
  Display = 'Display',
  CardTitle = 'CardTitle',
  Inherit = 'Inherit'
}

const defaultVariantMapping: Partial<
  Record<TypographySize | 'inherit', string>
> = {
  Title: 'h3',
  BodyM: 'span',
  BodyS: 'span',
  Caption: 'span'
};

export enum TypographyColor {
  Primary = 'Primary',
  Secondary = 'Secondary',
  Accent = 'Accent',
  Error = 'Error',
  Success = 'Success',
  Inherit = 'Inherit'
}

export enum TypographyShade {
  Dark = 'Dark',
  Medium = 'Medium',
  Light = 'Light'
}

interface TypographyProps extends PropsWithChildren {
  ellipsis?: boolean;
  color?: TypographyColor;
  size?: TypographySize;
  shade?: TypographyShade;
  as?: 'span' | 'div' | 'p' | 'label' | 'h1' | 'h2' | 'h3' | 'h4';
  bold?: boolean;
  semibold?: boolean;
  uppercase?: boolean;
  underline?: boolean;
  truncate?: boolean;
  lineHeight?: number | string;
  trackingWide?: boolean | number;
  wrap?: boolean;
  noWrap?: boolean;
  hoverEffect?: boolean;
}

const colorStyle: Record<TypographyColor, string> = {
  [TypographyColor.Primary]: 'text-white',
  [TypographyColor.Secondary]: 'text-text-dark',
  [TypographyColor.Accent]: 'text-pink-100',
  [TypographyColor.Success]: 'text-green-success',
  [TypographyColor.Error]: 'text-error',
  [TypographyColor.Inherit]: 'text-inherit'
};

const sizeStyle = {
  [TypographySize.Title]: 'text-2xl', // 24px
  [TypographySize.Subtitle]: 'text-[22px]', // 22px
  [TypographySize.BodyM]: 'text-xl', // 20px
  [TypographySize.BodyS]: 'text-lg', // 18px
  [TypographySize.Caption]: 'text-base', // 16px
  [TypographySize.Display]: 'text-5xl', // 48px
  [TypographySize.CardTitle]: 'text-4xl', // 36px
  [TypographySize.Inherit]: ''
};

const shadeStyle = {
  [TypographyShade.Dark]: 'opacity-70',
  [TypographyShade.Medium]: 'opacity-50',
  [TypographyShade.Light]: 'opacity-40'
};

const Typography = forwardRef(function Typography(
  {
    color = TypographyColor.Primary,
    size = TypographySize.BodyM,
    as = 'span',
    shade,
    children,
    bold,
    uppercase,
    underline,
    truncate,
    lineHeight,
    trackingWide,
    wrap,
    noWrap,
    hoverEffect,
    ...restProps
  }: TypographyProps,
  ref?: Ref<HTMLElement>
) {
  const classes = clsx(
    colorStyle[color],
    sizeStyle[size],
    'transition-colors delay-50',
    shade ? shadeStyle[shade] : 'opacity-100',
    bold && 'font-semibold',
    uppercase && 'uppercase',
    truncate && 'truncate',
    truncate && 'w-full',
    underline && 'underline',
    lineHeight && `leading-${lineHeight}`,
    typeof trackingWide === 'boolean'
      ? trackingWide && 'tracking-widest'
      : trackingWide && `tracking-[${trackingWide}px]`,
    wrap && 'break-words',
    noWrap && 'whitespace-nowrap',
    hoverEffect && 'transition-opacity hover:opacity-80',
  );

  return createElement(
    as || defaultVariantMapping[size],
    { className: classes, ref, style: { cursor: 'inherit' }, ...restProps },
    children
  );
});

export default Typography;
