import clsx from 'clsx';
import { useMemo } from 'react';
import { currencyRounded } from 'src/helper';
import {
  selectorPlanComponentOfferingById,
  usePlansOfferingStore
} from 'src/store/plansOfferings';
import { Plan } from 'src/types/plans';

import Typography, { TypographySize } from 'src/components/Typography';
import { getDataText, getSmsText, getVoiceText } from 'src/helper/plans';


interface Props extends Partial<Plan> {
  classes?: string;
}

export default function PlanCard({
  name,
  price,
  data_component_offering: dataOfferingId,
  sms_component_offering: smsOfferingId,
  voice_component_offering: voiceOfferingId,
  custom_data_limit,
  custom_sms_limit,
  custom_voice_limit,
  classes = ''
}: Props) {
  const plansById = usePlansOfferingStore(selectorPlanComponentOfferingById);

  const dataDescription = useMemo(() => {
    const dataLimit =
      dataOfferingId && plansById[dataOfferingId].plan_component.max_limit;

    return getDataText(custom_data_limit || dataLimit);
  }, [plansById, dataOfferingId, custom_data_limit]);

  const smsDescription = useMemo(() => {
    const smsLimit =
      smsOfferingId && plansById[smsOfferingId].plan_component.max_limit;

    return getSmsText(custom_sms_limit || smsLimit);
  }, [plansById, smsOfferingId, custom_sms_limit]);

  const voiceDescription = useMemo(() => {
    const voiceLimit =
      voiceOfferingId && plansById[voiceOfferingId].plan_component.max_limit;

    return getVoiceText(custom_voice_limit || voiceLimit);
  }, [plansById, voiceOfferingId, custom_voice_limit]);

  return (
    <div
      className={clsx(
        'relative rounded-3xl px-5 pt-10 pb-5 bg-gradient-to-r from-[#2E215C] to-[#5A7AB2]',
        classes
      )}
    >
      <Typography size={TypographySize.Title} bold>
        {name || 'Default name'}
      </Typography>
      <div className="flex justify-between items-start mt-2">
        <div>
          <div>{dataDescription}</div>
          <div>{voiceDescription}</div>
          <div>{smsDescription}</div>
          <div>EU roaming</div>
        </div>
        <div className="bg-white/10 p-4 rounded-2xl text-2xl font-bold">
          {currencyRounded(Number(price))}
        </div>
      </div>
    </div>
  );
}
