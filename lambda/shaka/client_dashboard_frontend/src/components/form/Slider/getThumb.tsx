import clsx from 'clsx';
import { ReactSliderProps } from 'react-slider';
import Tooltip from 'src/components/Tooltip';

const getThumb =
  (
    format: (value: number) => string,
    disabled?: boolean,
    max?: number,
    unlimited?: boolean,
    hideTooltip?: boolean
  ): ReactSliderProps['renderThumb'] =>
  (props, state) => (
    <div {...props}>
      <div className="relative">
        <Tooltip
          text={
            state.valueNow === max && unlimited
              ? 'Unlimited'
              : format(state.valueNow)
          }
          alwaysShow={!hideTooltip}
          hidden={hideTooltip}
          bold
        >
          <div className="flex flex-col items-center absolute -top-1 -translate-x-1/2">
            <span className="block bg-white rounded-full w-6 h-6">
              <span
                className={clsx(
                  'block rounded-full w-4 h-4 translate-x-1 translate-y-1',
                  disabled ? ' bg-black/60' : ' bg-pink-100'
                )}
              />
            </span>
          </div>
        </Tooltip>
      </div>
    </div>
  );

export default getThumb;
