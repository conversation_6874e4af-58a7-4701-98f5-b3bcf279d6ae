import { useEffect, useState } from 'react';
import DropdownItem, { ListOptionType } from './DropdownItem';
import { PlusIcon } from 'src/icons/Plus';

type SelectedValues = ListOptionType['value'][];

interface Props {
  selectedOptions: SelectedValues;
  options: ListOptionType[];
  onChange: (values: SelectedValues) => void;
  hideNewOptionButton?: boolean;
}

const MultiDropdown = ({
  options,
  onChange,
  selectedOptions: defaultOptions,
  hideNewOptionButton
}: Props) => {
  const [availableOptions, setAvailableOptions] = useState(
    options.filter((_, index) => index !== 0)
  );

  const [selectedOptions, setSelectedOptions] = useState<SelectedValues>(
    defaultOptions || [options[0].value]
  );

  const allOptionsSelected = selectedOptions.length === options.length;

  const handleOptionSelect = (index: number) => (option: ListOptionType) => {
    const newSelectedOptions = [...selectedOptions];
    newSelectedOptions[index] = option!.value;

    setSelectedOptions(newSelectedOptions);
    onChange(newSelectedOptions);
  };

  const addNewOption = () => {
    const [first] = availableOptions;

    const newSelectedOptions = [...selectedOptions, first.value];

    setSelectedOptions(newSelectedOptions);
    onChange(newSelectedOptions);
  };

  const removeOption = (optionToRemove: ListOptionType) => {
    setSelectedOptions((options) =>
      options.filter((opt) => opt !== optionToRemove.value)
    );
  };

  useEffect(() => {
    setAvailableOptions(
      options.filter(
        (option) => !selectedOptions.find((opt) => opt === option.value)
      )
    );
  }, [selectedOptions, defaultOptions]);

  useEffect(() => {
    onChange(selectedOptions);
  }, []);

  return (
    <div className="relative bg-grey-area text-white rounded-xl py-3 px-4">
      <div className="flex items-center flex-wrap gap-3">
        {selectedOptions.map((value, i) => (
          <DropdownItem
            key={value}
            options={availableOptions}
            onChange={handleOptionSelect(i)}
            selectedOption={
              options.find((opt) => opt.value === value) || options[0]
            }
            disabled={allOptionsSelected}
            disableRemove={i === 0}
            onRemove={removeOption}
          />
        ))}
        {!allOptionsSelected && !hideNewOptionButton && (
          <button
            className="bg-white text-black rounded-lg p-1.5 h-7 w-7 flex items-center justify-center hover:bg-white/80"
            onClick={addNewOption}
            type="button"
          >
            <PlusIcon strokeWidth={3.5} />
          </button>
        )}
      </div>
    </div>
  );
};

export default MultiDropdown;
