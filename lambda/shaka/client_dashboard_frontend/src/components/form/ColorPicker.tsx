import { useRef, ChangeEvent } from 'react';

type Props = {
  value: string;
  onChange: (v: string) => void;
};

export default function ColorPicker({ value, onChange }: Props) {
  const colorRef = useRef<HTMLInputElement | null>(null);

  const handleChangeColor = (e: ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value);
  };

  const handleOpenPicker = () => {
    if (colorRef.current) {
      colorRef.current.click();
    }
  };

  return (
    <div
      className="w-8 h-8"
      style={{ backgroundColor: value }}
      onClick={handleOpenPicker}
    >
      <input
        type="color"
        className="invisible"
        value={value}
        ref={colorRef}
        onChange={handleChangeColor}
      />
    </div>
  );
}
