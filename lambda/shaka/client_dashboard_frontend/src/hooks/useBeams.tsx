import { fetchBeams } from 'src/api/sms';
import { useAsync } from 'src/api/useApi';
import {
  selectorBeamsById,
  selectorBeamsLoaded,
  selectorBeamsPublished,
  selectorBeamsScheduled,
  setBeams,
  useBeamsStore
} from 'src/store/beams';

export default function useBeams({
  loadOnMount
}: { loadOnMount?: boolean } = {}) {
  const isBeamsLoaded = useBeamsStore(selectorBeamsLoaded);
  const scheduledBeams = useBeamsStore(selectorBeamsScheduled);
  const publishedBeams = useBeamsStore(selectorBeamsPublished);
  const beamsById = useBeamsStore(selectorBeamsById);
  const { isLoading, run: doFetchBeams } = useAsync(fetchBeams, {
    setToStore: setBeams,
    fetchOnMount: loadOnMount && !isBeamsLoaded
  });

  return {
    isLoading: isLoading && !isBeamsLoaded,
    scheduledBeams,
    publishedBeams,
    beamsById,
    loadBeams: doFetchBeams
  };
}
