import { MonthNetworkStat } from 'src/types/network';
import api from './index';

export const fetchNetworkStatistic = (): Promise<MonthNetworkStat[]> => {
  const currentDate = new Date().toISOString();
  const startDate = new Date('2024-01-01').toISOString();

  return api
    .get(
      `frontend/network-statistics/?start_date=${startDate}&end_date=${currentDate}&aggregation_period=month`
    )
    .then((res) => res.data);
};
