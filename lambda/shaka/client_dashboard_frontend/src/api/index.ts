import axios from 'axios';
import {
  deleteAuthSessionKeys,
  getSessionKeys,
  setAuthSessionKeys
} from 'src/localStorage/auth';

const api = axios.create({
  baseURL: `${import.meta.env.VITE_BASE_URL ?? 'http://localhost:8000'}/api`
});

let isTokenValid = true;

api.interceptors.request.use(
  async function (config) {
    const { accessToken, expiresAt, refreshToken } = getSessionKeys();
    let validAccessToken = accessToken;

    if (
      isTokenValid &&
      (!expiresAt || new Date().getTime() >= Number(expiresAt)) &&
      refreshToken
    ) {
      isTokenValid = false;

      const response = await api
        .post('/auth/refresh-token/', {
          refresh_token: refreshToken
        })
        .then((res) => {
          isTokenValid = true;
          return res;
        })
        .catch(() => {
          deleteAuthSessionKeys();

          window.location.replace(window.location.origin + '/login');
        });

      const newAuthData = response?.data || {};
      setAuthSessionKeys(newAuthData);

      validAccessToken = newAuthData.access_token;
    }

    if (validAccessToken)
      config.headers.Authorization = `Bearer ${validAccessToken}`;

    return config;
  },
  function (error) {
    // Do something with request error
    return Promise.reject(error);
  }
);

export default api;
