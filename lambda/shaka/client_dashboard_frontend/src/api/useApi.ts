import { AxiosError } from 'axios';
import type { Dispatch, SetStateAction } from 'react';
import React, { useContext, useEffect, useState } from 'react';
import { AuthContext } from 'src/contexts/AuthContext';
import { flattenErrorsToSingleString } from 'src/helper/strings';

export type UseAsyncReturn<TR extends any[], T> = {
  run: (...args: TR) => Promise<T>;
  data?: T;
  error: any;
  isLoading: boolean;
  setData: Dispatch<SetStateAction<T | undefined>>;
};

export function useAsync<TR extends any[], T>(
  asyncFunc: (...args: TR) => Promise<T>,
  options?: { fetchOnMount?: boolean; props?: TR; setToStore?: any }
): UseAsyncReturn<TR, T> {
  const { logoutUser } = useContext(AuthContext);
  const [data, setData] = useState<T>();
  const [error, setError] = useState<Error | string | object>();
  const [promise, setPromise] = useState();
  const [isLoading, setIsLoading] = useState(true);
  const {
    fetchOnMount,
    props = [],
    setToStore
  } = options || { fetchOnMount: false };

  const submit = React.useCallback(
    async (...args: TR) => {
      let promiseInFlight = promise as Promise<T> | undefined;
      if (promiseInFlight) {
        return promiseInFlight;
      }

      setIsLoading(true);

      try {
        promiseInFlight = asyncFunc(...args);
        setPromise(promiseInFlight as any);
        setError(undefined);
        setData(undefined);

        const result = await promiseInFlight;
        setData(result);
        setToStore && setToStore(result);
        return result;
      } catch (e: unknown) {
        const error = e as AxiosError<Error>;

        if (error.response?.status === 403) {
          logoutUser();
        }

        let errorMessage: Error | string | undefined | object =
          error.response?.data;

        if (typeof errorMessage === 'object') {
          errorMessage = flattenErrorsToSingleString(errorMessage);
        }

        setError(errorMessage);
        throw e;
      } finally {
        setIsLoading(false);
        setPromise(undefined);
      }
    },
    [asyncFunc]
  );

  useEffect(() => {
    if (fetchOnMount && !data) {
      submit(...(props as TR));
    }
  }, []);

  return {
    run: submit,
    data,
    error,
    isLoading,
    setData
  };
}
