import { Beam } from 'src/types/sms';
import api from './index';

type BeamAddPayload = {
  title: string;
  message: string;
  send_on?: string;
};
type BeamUpdatePayload = BeamAddPayload & { id: number };

export const fetchBeams = (): Promise<Beam[]> =>
  api.get('/frontend/sms/').then((res) => res.data);

export const addBeam = (payload: BeamAddPayload) =>
  api.post('/frontend/sms/', payload).then((res) => res.data);

export const updateBeam = (payload: BeamUpdatePayload) =>
  api.put(`/frontend/sms/${payload.id}/`, payload).then((res) => res.data);

export const deleteBeam = (id: Beam['id']) =>
  api.delete('/frontend/sms/' + id).then((res) => res.data);
