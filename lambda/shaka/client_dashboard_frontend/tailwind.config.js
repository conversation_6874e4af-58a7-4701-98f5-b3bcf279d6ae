/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      transitionProperty: {
        height: 'height'
      },
      gradientColorStopPositions: {
        160: '160%'
      },
      flex: {
        'sidebar-long': '0 0 400px',
        'sidebar-short': '0 0 300px'
      },
      colors: {
        error: '#DA1E62',
        success: '#66E29F',
        warning: '#FFC107',
        grey: {
          100: '#F8F8F8',
          200: '#D9D9D9',
          300: '#9E9EA8',
          400: '#9698AB',
          500: '#9092B0',
          input: '#D9D9D90A',
          'input-solid': '#242845',
          slider: 'rgba(0, 0, 0, 0.11)',
          select: '#151923',
          hover: 'rgba(255, 255, 255, 0.1)',
          area: 'rgba(10, 12, 18, 0.5)'
        },
        'deep-blue': {
          100: '#516FA6',
          200: '#374a6f',
          300: '#475991'
        },
        'soft-lilac': {
          100: '#EBE5FC',
          200: '#BEB4D9'
        },
        primary: '#EF8DF8',
        silver: '#DEE2E6',
        'navy-blue': '#132557',
        'dark-purple': '#2E215C',
        green: {
          success: '#87FFBE'
        },
        lime: '#4BAA00',
        blue: {
          500: '#4575EE',
          600: '#607BB5'
        },
        orange: '#DA9D00',
        neutral: {
          100: '#E7EAEE',
          500: '#64748B',
          800: '#191D23'
        },
        text: {
          global: '',
          dark: '#0A0C16'
        },
        pink: {
          100: '#EF8DF8',
          200: '#694288'
        }
      },
      backgroundImage: () => ({
        'gradient-blue': 'var(--gradient-blue)',
        'gradient-pink': 'var(--gradient-pink)',
        'gradient-rainbow': 'var(--gradient-rainbow)',
        'gradient-plan': 'var( --gradient-plan)',
        'gradient-button': 'var( --gradient-button)',
        'gradient-tooltip-1': 'var( --gradient-tooltip-1)',
        'gradient-tooltip-2': 'var( --gradient-tooltip-2)',
        'gradient-tooltip-3': 'var( --gradient-tooltip-3)',
        'gradient-divider': 'var(--gradient-divider)',
        'gradient-card-1': 'var(--gradient-card-1)',
        'gradient-card-2': 'var(--gradient-card-2)',
        'gradient-card-3': 'var(--gradient-card-3)',
        'gradient-card-4': 'var(--gradient-card-4)',
        'gradient-actions': 'var(--gradient-actions)',
        'gradient-subscriber': 'var(--gradient-subscriber)',
        'gradient-bolton': 'var(--gradient-bolton)'
      }),
      backgroundSize: {
        full: '100% 100%'
      },
      boxShadow: {
        sm: '0px 4px 4px 0px rgba(0, 0, 0, 0.25);',
        md: '0px 0px 14px -3px rgba(0, 0, 0, 0.08)',
        lg: '0px 0px 19px 9px rgba(0, 0, 0, 0.04)'
      },
      fontFamily: {
        baloo: 'Baloo 2',
        inter: 'Inter',
        jakarta: 'Plus Jakarta Sans',
        manrope: 'Manrope'
      },
      screens: {
        desktop: '1920px'
      }
    }
  },
  plugins: []
};
