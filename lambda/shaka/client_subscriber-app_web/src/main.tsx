import ReactDOM from "react-dom/client";
import "./index.css";
import { RouterProvider, createRouter } from "@tanstack/react-router";
import { routeTree } from "./routeTree.gen";
import { useAuth } from "./hooks/useAuth";
import AuthProvider, { AuthContextType } from "./context/AuthContext";
import React from "react";
import { ClientProvider } from "./context/ClientContext";
import SubscriptionProvider from "./context/SubscriptionContext";
import useSubscription from "./hooks/useSubscription";
import FullPageLoader from "./components/common/FullPageLoader";
import { StripeProvider } from "./context/StripeContext";
import ReactGA from "react-ga4";
import { PlansProvider } from "./context/PlansContext";
import usePlans from "./hooks/usePlans";

if (import.meta.env.VITE_GOOGLE_TRACKING_ID) {
  ReactGA.initialize(import.meta.env.VITE_GOOGLE_TRACKING_ID);
}


const router = createRouter({
  routeTree,
  defaultPreload: "intent",
  context: {
    auth: {} as AuthContextType,
    clientProps: {
      isSignupDisabled: import.meta.env.VITE_DISABLE_SIGNUP === "true",
    },
  },
});

// Register things for typesafety
declare module "@tanstack/react-router" {
  interface Register {
    router: typeof router;
  }
}

function InnerApp() {
  const auth = useAuth();
  const { isLoading: isSubscriptionLoading } = useSubscription();
  const { isLoading: isPlansLoading } = usePlans();

  const isDataLoading =
    (isSubscriptionLoading && auth.isAuthenticated()) || isPlansLoading;

  if (isDataLoading) {
    return (
      <div className="w-[100vw] h-[100vh]">
        <FullPageLoader />
      </div>
    );
  }

  return <RouterProvider router={router} context={{ auth }} />;
}

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <AuthProvider>
      <StripeProvider>
        <ClientProvider>
          <PlansProvider>
            <SubscriptionProvider>
              <InnerApp />
            </SubscriptionProvider>
          </PlansProvider>
        </ClientProvider>
      </StripeProvider>
    </AuthProvider>
  </React.StrictMode>
);
