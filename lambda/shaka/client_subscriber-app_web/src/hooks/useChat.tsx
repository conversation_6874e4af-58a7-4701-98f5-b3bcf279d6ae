import { useEffect } from 'react';
import Intercom from '@intercom/messenger-js-sdk';
import useSubscription from './useSubscription';

const intercomKey = import.meta.env.VITE_INTERCOM_KEY;

async function createHmacSHA256(secretKey: string, userIdentifier: string) {
  const enc = new TextEncoder();
  const keyData = enc.encode(secretKey);
  const data = enc.encode(userIdentifier);

  const key = await crypto.subtle.importKey(
    'raw',
    keyData,
    { name: 'HMAC', hash: { name: 'SHA-256' } },
    false,
    ['sign']
  );

  const signature = await crypto.subtle.sign('HMAC', key, data);

  return Array.from(new Uint8Array(signature))
    .map((b) => b.toString(16).padStart(2, '0'))
    .join('');
}

const useChat = () => {
  const { subscriber } = useSubscription();

  useEffect(() => {
    if (!subscriber) return;

    const secretKey = intercomKey;
    const userIdentifier = subscriber.email;

    createHmacSHA256(secretKey, userIdentifier).then((hash) => {
      Intercom({
        api_base: 'https://api-iam.intercom.io',
        app_id: 'na34h0vg',
        name: subscriber?.name,
        email: subscriber?.email,
        user_hash: hash
      });
    });
  }, [subscriber]);
};

export default useChat;
