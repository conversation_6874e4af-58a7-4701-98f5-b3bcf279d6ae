import type { Dispatch, SetStateAction } from 'react';
import { useCallback, useEffect, useState } from 'react';
import { toast } from 'react-toastify';

type Error = {
  response: {
    data: {
      error: string;
    };
  };
};

export type UseAsyncReturn<TR, T> = {
  run: (...args: TR[]) => Promise<T>;
  data?: T;
  error?: string;
  isLoading: boolean;
  setData: Dispatch<SetStateAction<T | undefined>>;
};

type UseAsyncOptions<TR> = {
  isOnMount?: boolean;
  props?: TR[];
  onSuccess?: () => void;
  defaultErrorText?: string;
  withToast?: boolean;
  successMessage?: string;
};

export function useRequest<TR, T>(
  asyncFunc: (...args: TR[]) => Promise<T>,
  options: UseAsyncOptions<TR> = {}
): UseAsyncReturn<TR, T> {
  const {
    isOnMount = false,
    props = [],
    defaultErrorText,
    withToast,
    successMessage
  } = options;

  const [data, setData] = useState<T>();
  const [error, setError] = useState<string>();
  const [isLoading, setIsLoading] = useState(isOnMount);

  const submit = useCallback(
    async (...args: TR[]) => {
      setIsLoading(true);

      try {
        const service = asyncFunc(...args);
        setError(undefined);
        setData(undefined);

        const result = await service;
        setData(result);

        withToast && toast.success(successMessage || 'Success');
        return result;
      } catch (e: unknown) {
        const error = e as Error;
        const errorMessage =
          error.response?.data.error ||
          defaultErrorText ||
          'Something went wrong. Please try again later';

        setError(errorMessage);

        withToast && toast.error(errorMessage);
        throw e;
      } finally {
        setIsLoading(false);
      }
    },
    [asyncFunc]
  );

  useEffect(() => {
    if (isOnMount && !data) {
      submit(...(props as TR[]));
    }

    () => {
      setIsLoading(false);
    };
  }, []);

  return {
    run: submit,
    data,
    error,
    isLoading,
    setData
  };
}
