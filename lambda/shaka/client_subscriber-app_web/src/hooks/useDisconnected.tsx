import { useNavigate, useRouterState } from '@tanstack/react-router';
import { useCallback, useEffect } from 'react';
import { ROUTES } from 'src/config/routes';

export default function useDisconnected() {
  const navigate = useNavigate();

  const { location } = useRouterState();
  const pathname = location.pathname;

  const handleOffline = useCallback(() => {
    console.error('Connection error');
    navigate({
      to: ROUTES.ConnectionError,
      search: {
        redirect: pathname
      },
      replace: true
    });
  }, [navigate, pathname]);

  useEffect(() => {
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('offline', handleOffline);
    };
  }, [handleOffline]);
}
