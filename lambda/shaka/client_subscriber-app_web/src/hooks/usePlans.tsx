import { useContext, useEffect, useState } from 'react';
import { fetchPlans } from 'src/api/data';
import { PlansContext } from 'src/context/PlansContext';
import { ClientPlanDetails } from 'src/types/slide';
import { useRequest } from './useRequest';

const usePlans = (): { plans: ClientPlanDetails[]; isLoading: boolean, refreshPlans: () => Promise<void> } => {
  const { plans, setPlans } = useContext(PlansContext);
  const { run: runFetchPlans } = useRequest(fetchPlans);

  const plansLoaded = plans?.length > 0;

  const [isLoading, setIsLoading] = useState(!plansLoaded);

  const refreshPlans = () => {
    return runFetchPlans()
        .then((plans) => {
          const transformedSlides = plans
            .filter((plan) => plan.active)
            .map((plan) => ({
              key: plan.id,
              clientPlan: plan
            }));
          setPlans(transformedSlides);
        })
        .finally(() => {
          setIsLoading(false);
        });
  };
  
  useEffect(() => {
    if (!plansLoaded) {
      refreshPlans();
    }
  }, []);

  return { plans, isLoading, refreshPlans };
};

export default usePlans;
