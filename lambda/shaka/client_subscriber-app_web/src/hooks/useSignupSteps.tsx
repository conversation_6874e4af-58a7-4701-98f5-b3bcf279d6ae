import { LinkProps, useRouterState } from '@tanstack/react-router';
import { ROUTES } from 'src/config/routes';

type NavigationStep = {
  readonly step: number;
  readonly title: string;
  readonly isParallel?: boolean;
};

const navigation: Record<string, NavigationStep> = {
  [ROUTES.ExplorePlans]: {
    step: 1,
    title: 'Choose your plan'
  },
  [ROUTES.SignUp]: {
    step: 2,
    title: 'Tell us about yourself'
  },
  [ROUTES.PlanConfirmation]: {
    step: 3,
    title: 'Confirm your plan'
  },
  [ROUTES.Checkout]: {
    step: 4,
    title: 'Submit payment'
  },
  [ROUTES.ChooseSim]: {
    step: 5,
    title: 'Choose your sim'
  },
  [ROUTES.EsimSettings]: {
    step: 6,
    title: 'SETUP YOUR ESIM',
    isParallel: true
  },
  [ROUTES.AddressDetails]: {
    step: 6,
    title: 'Provide delivery info',
    isParallel: true
  },
  [ROUTES.SimActivation]: {
    step: 6,
    title: 'CONFIRM SIM SERIAL NUMBER',
    isParallel: true
  },
  [ROUTES.FinishSignUp]: {
    step: 7,
    title: 'Enjoy your new plan'
  },
  [ROUTES.Home]: {
    step: 0,
    title: ''
  }
} as const;

export default function useSignupSteps() {
  const pathname = useRouterState({
    select: (state) => state.location.pathname
  }) as keyof typeof navigation;

  const { step, title } = navigation[pathname] || {};

  const maxStep = Math.max(
    ...Object.values(navigation)
      .filter((route) => !route.isParallel)
      .map((route) => route.step)
  );

  return {
    step,
    stepTitle: title,
    stepsAmount: maxStep,
    isIndexPage: step === 0
  };
}

const pathNeedsPlanId: LinkProps['to'][] = [
  ROUTES.PlanConfirmation,
  ROUTES.Checkout
];
const publicPaths: LinkProps['to'][] = [ROUTES.Home, ROUTES.SignUp];

const restrictedPaths: LinkProps['to'][] = [
  ROUTES.EmailConfirmation,
  ROUTES.PlanConfirmation,
  ROUTES.Checkout,
  ROUTES.ChooseSim,
  ROUTES.EsimSettings,
  ROUTES.AddressDetails,
  ROUTES.FinishSignUp,
  ROUTES.CheckoutReturn
];

export const checkIfRestrictedPaths = (pathname: LinkProps['to']) =>
  restrictedPaths.includes(pathname);

export const checkIfPublicPaths = (pathname: (typeof publicPaths)[number]) =>
  publicPaths.includes(pathname);

export const checkIfPathNeedsPlanId = (pathname: LinkProps['to']) =>
  pathNeedsPlanId.includes(pathname);
