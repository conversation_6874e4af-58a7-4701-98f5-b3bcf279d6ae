import { buyRoamingSim, fetchRoamingSimOffers } from 'src/api/roaming-sim';
import { useRequest } from './useRequest';

export default function useRoamingSim() {
  const { isLoading, data: roamingSimData } = useRequest(
    fetchRoamingSimOffers,
    {
      isOnMount: true
    }
  );
  const { run: runBuyRoamingSim } = useRequest(buyRoamingSim);

  const roamingSimOffers = roamingSimData?.packages;
  const roamingCountries = roamingSimData?.countries;
  const roamingRegions = roamingSimData?.regions;
  const mostPopularCountries = roamingSimData?.most_popular || [];

  return {
    isLoading: isLoading,
    buyRoamingSim: runBuyRoamingSim,
    roamingSimOffers,
    roamingCountries,
    roamingRegions,
    mostPopularCountries
  };
}
