import { useRequest } from './useRequest';
import {
  useBoltOnsStore,
  selectorBoltOnsLoaded,
  setBoltOns,
  selectorBoltOns
} from 'src/store/bolt-ons';
import { useEffect } from 'react';
import {
  buyBoltOns as buyBoltOnsRequest,
  buyBoltOnEU as buyEUBoltOnRequest,
  fetchRoamingBoltOns
} from 'src/api/boltons';

export default function useBoltOns() {
  const boltOns = useBoltOnsStore(selectorBoltOns);
  const isBoltOnsLoaded = useBoltOnsStore(selectorBoltOnsLoaded);

  const { run: runFetchBoltOns, isLoading } = useRequest(fetchRoamingBoltOns);

  const { run: runBuyBoltOn } = useRequest(buyBoltOnsRequest);
  const { run: runBuyBoltOnEU } = useRequest(buyEUBoltOnRequest);

  useEffect(() => {
    if (!isBoltOnsLoaded) {
      runFetchBoltOns().then((data) => {
        setBoltOns(data);
      });
    }
  }, []);

  return {
    isLoading: isLoading || !isBoltOnsLoaded,
    loadBoltOns: runFetchBoltOns,
    boltOns,
    setBoltOns,
    buyBoltOn: runBuyBoltOn,
    buyBoltOnEU: runBuyBoltOnEU
  };
}
