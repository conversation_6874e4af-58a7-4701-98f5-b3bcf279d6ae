import { useRouterState } from "@tanstack/react-router";
import { useEffect } from "react";
import ReactGA from "react-ga4";

const useAnalytics = () => {
  const { location } = useRouterState();
  const href = location.href;

  useEffect(() => {
    if (import.meta.env.VITE_GOOGLE_TRACKING_ID) {
      ReactGA.send({
        hitType: "pageview",
        page: href,
        title: document.title,
      });
    }
  }, [href]);
};

export default useAnalytics;
