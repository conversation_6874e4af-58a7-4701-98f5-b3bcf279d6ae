import { useCallback, useEffect, useState } from 'react';
import useSubscription from './useSubscription';
import {
  getSimSettingsSelectedType,
  getStripeSessionExpiresAt,
  getStripeSessionId
} from 'src/config/localStorageActions';
import { useNavigate, useSearch } from '@tanstack/react-router';
import { ROUTES } from 'src/config/routes';
import { isPhysicalSimDisabled } from 'src/config/env-vars';

export default function useSubscriptionRedirect() {
  const {
    subscriber,
    isSubscriptionLoaded,
    currentPlan,
    currentPlanIndex,
    subscriberPlanSimList
  } = useSubscription();
  const navigate = useNavigate();
  const search = useSearch({ strict: false });
  const isInNewSubscriptionFlow = 'new-subscription' in search;

  const [isSessionExpired, setIsSessionExpired] = useState(false);

  const isEsimSubscriber = currentPlan.sim_type === 'esim';
  const isTravelSimSelected =
    subscriberPlanSimList[currentPlanIndex]?.type === 'sim';
  const isSimSettingsInProgress = Boolean(getSimSettingsSelectedType());

  const checkSession = useCallback(async () => {
    const sessionId = getStripeSessionId();
    const sessionExpiresAt = getStripeSessionExpiresAt();
    const isSessionActive = Boolean(
      sessionExpiresAt && new Date(Number(sessionExpiresAt)) > new Date()
    );

    setIsSessionExpired(!sessionId || !isSessionActive);
  }, []);

  useEffect(() => {
    const shouldRedirectToAddress =
      !isTravelSimSelected &&
      !subscriber?.address &&
      currentPlan.sim_type === 'physical' &&
      getSimSettingsSelectedType() != 'self-serve' &&
      !isPhysicalSimDisabled;

    if (isSubscriptionLoaded && subscriber) {
      if (subscriber?.address) {
        return;
      }

      if (!subscriber.is_verified) {
        navigate({ to: ROUTES.EmailConfirmation });
        return;
      }

      if (!subscriber?.plans?.length && !subscriber?.phone_number) {
        navigate({ to: ROUTES.ExplorePlans });
        return;
      }

      if (isSimSettingsInProgress) {
        navigate({ to: ROUTES.ChooseSim });
        return;
      }

      if (!subscriber?.plans) {
        if (shouldRedirectToAddress) {
          navigate({ to: ROUTES.AddressDetails });
          return;
        }

        navigate({ to: ROUTES.ExplorePlans });
        return;
      } else {
        if (
          !isInNewSubscriptionFlow &&
          !subscriber?.plans[0]?.self_activated &&
          shouldRedirectToAddress
        ) {
          navigate({ to: ROUTES.AddressDetails });
          return;
        }
      }
    }
  }, [
    isSessionExpired,
    subscriber,
    isSubscriptionLoaded,
    navigate,
    isEsimSubscriber,
    isSimSettingsInProgress
  ]);

  useEffect(() => {
    checkSession();
  }, [checkSession]);
}
