/* prettier-ignore-start */

/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file is auto-generated by TanStack Router

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as TermsImport } from './routes/terms'
import { Route as PrivacyPolicyImport } from './routes/privacy-policy'
import { Route as ForgotPasswordImport } from './routes/forgot-password'
import { Route as ForgotConfirmPasswordImport } from './routes/forgot-confirm-password'
import { Route as ConnectionErrorImport } from './routes/connection-error'
import { Route as SignupFlowImport } from './routes/_signup-flow'
import { Route as AuthImport } from './routes/_auth'
import { Route as Import } from './routes/*'
import { Route as SignupFlowIndexImport } from './routes/_signup-flow/index'
import { Route as OpenidCallbackImport } from './routes/openid/callback'
import { Route as SignupFlowSimActivationImport } from './routes/_signup-flow/sim-activation'
import { Route as SignupFlowSignupImport } from './routes/_signup-flow/signup'
import { Route as SignupFlowPlanConfirmationImport } from './routes/_signup-flow/plan-confirmation'
import { Route as SignupFlowLoginImport } from './routes/_signup-flow/login'
import { Route as SignupFlowFinishSignUpImport } from './routes/_signup-flow/finish-sign-up'
import { Route as SignupFlowExplorePlansImport } from './routes/_signup-flow/explore-plans'
import { Route as SignupFlowEsimSettingsImport } from './routes/_signup-flow/esim-settings'
import { Route as SignupFlowEsimConfirmationImport } from './routes/_signup-flow/esim-confirmation'
import { Route as SignupFlowEmailConfirmationImport } from './routes/_signup-flow/email-confirmation'
import { Route as SignupFlowChooseSimImport } from './routes/_signup-flow/choose-sim'
import { Route as SignupFlowAddressDetailsImport } from './routes/_signup-flow/address-details'
import { Route as AuthLayoutImport } from './routes/_auth/_layout'
import { Route as SignupFlowCheckoutIndexImport } from './routes/_signup-flow/checkout/index'
import { Route as SignupFlowOpenidLoginImport } from './routes/_signup-flow/openid/login'
import { Route as SignupFlowCheckoutReturnImport } from './routes/_signup-flow/checkout/return'
import { Route as AuthNewSubscriptionReturnImport } from './routes/_auth/new-subscription.return'
import { Route as AuthLayoutPerksImport } from './routes/_auth/_layout/perks'
import { Route as AuthLayoutDashboardImport } from './routes/_auth/_layout/dashboard'
import { Route as AuthLayoutSettingsIndexImport } from './routes/_auth/_layout/settings/index'
import { Route as AuthLayoutSettingsSupportImport } from './routes/_auth/_layout/settings/support'
import { Route as AuthLayoutSettingsSubscriberImport } from './routes/_auth/_layout/settings/subscriber'
import { Route as AuthLayoutSettingsPortNumberImport } from './routes/_auth/_layout/settings/port-number'
import { Route as AuthLayoutSettingsEsimSettingsImport } from './routes/_auth/_layout/settings/esim-settings'
import { Route as AuthLayoutSettingsChangePasswordImport } from './routes/_auth/_layout/settings/change-password'
import { Route as AuthLayoutSettingsCardDetailsImport } from './routes/_auth/_layout/settings/card-details'
import { Route as AuthLayoutSettingsCancelSubscriptionImport } from './routes/_auth/_layout/settings/cancel-subscription'
import { Route as AuthLayoutRoamingSimReturnImport } from './routes/_auth/_layout/roaming-sim.return'
import { Route as AuthLayoutCardDetailsReturnImport } from './routes/_auth/_layout/card-details.return'
import { Route as AuthLayoutBoltOnsReturnImport } from './routes/_auth/_layout/bolt-ons.return'

// Create/Update Routes

const TermsRoute = TermsImport.update({
  path: '/terms',
  getParentRoute: () => rootRoute,
} as any)

const PrivacyPolicyRoute = PrivacyPolicyImport.update({
  path: '/privacy-policy',
  getParentRoute: () => rootRoute,
} as any)

const ForgotPasswordRoute = ForgotPasswordImport.update({
  path: '/forgot-password',
  getParentRoute: () => rootRoute,
} as any)

const ForgotConfirmPasswordRoute = ForgotConfirmPasswordImport.update({
  path: '/forgot-confirm-password',
  getParentRoute: () => rootRoute,
} as any)

const ConnectionErrorRoute = ConnectionErrorImport.update({
  path: '/connection-error',
  getParentRoute: () => rootRoute,
} as any)

const SignupFlowRoute = SignupFlowImport.update({
  id: '/_signup-flow',
  getParentRoute: () => rootRoute,
} as any)

const AuthRoute = AuthImport.update({
  id: '/_auth',
  getParentRoute: () => rootRoute,
} as any)

const Route = Import.update({
  path: '/*',
  getParentRoute: () => rootRoute,
} as any)

const SignupFlowIndexRoute = SignupFlowIndexImport.update({
  path: '/',
  getParentRoute: () => SignupFlowRoute,
} as any)

const OpenidCallbackRoute = OpenidCallbackImport.update({
  path: '/openid/callback',
  getParentRoute: () => rootRoute,
} as any)

const SignupFlowSimActivationRoute = SignupFlowSimActivationImport.update({
  path: '/sim-activation',
  getParentRoute: () => SignupFlowRoute,
} as any)

const SignupFlowSignupRoute = SignupFlowSignupImport.update({
  path: '/signup',
  getParentRoute: () => SignupFlowRoute,
} as any)

const SignupFlowPlanConfirmationRoute = SignupFlowPlanConfirmationImport.update(
  {
    path: '/plan-confirmation',
    getParentRoute: () => SignupFlowRoute,
  } as any,
)

const SignupFlowLoginRoute = SignupFlowLoginImport.update({
  path: '/login',
  getParentRoute: () => SignupFlowRoute,
} as any)

const SignupFlowFinishSignUpRoute = SignupFlowFinishSignUpImport.update({
  path: '/finish-sign-up',
  getParentRoute: () => SignupFlowRoute,
} as any)

const SignupFlowExplorePlansRoute = SignupFlowExplorePlansImport.update({
  path: '/explore-plans',
  getParentRoute: () => SignupFlowRoute,
} as any)

const SignupFlowEsimSettingsRoute = SignupFlowEsimSettingsImport.update({
  path: '/esim-settings',
  getParentRoute: () => SignupFlowRoute,
} as any)

const SignupFlowEsimConfirmationRoute = SignupFlowEsimConfirmationImport.update(
  {
    path: '/esim-confirmation',
    getParentRoute: () => SignupFlowRoute,
  } as any,
)

const SignupFlowEmailConfirmationRoute =
  SignupFlowEmailConfirmationImport.update({
    path: '/email-confirmation',
    getParentRoute: () => SignupFlowRoute,
  } as any)

const SignupFlowChooseSimRoute = SignupFlowChooseSimImport.update({
  path: '/choose-sim',
  getParentRoute: () => SignupFlowRoute,
} as any)

const SignupFlowAddressDetailsRoute = SignupFlowAddressDetailsImport.update({
  path: '/address-details',
  getParentRoute: () => SignupFlowRoute,
} as any)

const AuthLayoutRoute = AuthLayoutImport.update({
  id: '/_layout',
  getParentRoute: () => AuthRoute,
} as any)

const SignupFlowCheckoutIndexRoute = SignupFlowCheckoutIndexImport.update({
  path: '/checkout/',
  getParentRoute: () => SignupFlowRoute,
} as any)

const SignupFlowOpenidLoginRoute = SignupFlowOpenidLoginImport.update({
  path: '/openid/login',
  getParentRoute: () => SignupFlowRoute,
} as any)

const SignupFlowCheckoutReturnRoute = SignupFlowCheckoutReturnImport.update({
  path: '/checkout/return',
  getParentRoute: () => SignupFlowRoute,
} as any)

const AuthNewSubscriptionReturnRoute = AuthNewSubscriptionReturnImport.update({
  path: '/new-subscription/return',
  getParentRoute: () => AuthRoute,
} as any)

const AuthLayoutPerksRoute = AuthLayoutPerksImport.update({
  path: '/perks',
  getParentRoute: () => AuthLayoutRoute,
} as any)

const AuthLayoutDashboardRoute = AuthLayoutDashboardImport.update({
  path: '/dashboard',
  getParentRoute: () => AuthLayoutRoute,
} as any)

const AuthLayoutSettingsIndexRoute = AuthLayoutSettingsIndexImport.update({
  path: '/settings/',
  getParentRoute: () => AuthLayoutRoute,
} as any)

const AuthLayoutSettingsSupportRoute = AuthLayoutSettingsSupportImport.update({
  path: '/settings/support',
  getParentRoute: () => AuthLayoutRoute,
} as any)

const AuthLayoutSettingsSubscriberRoute =
  AuthLayoutSettingsSubscriberImport.update({
    path: '/settings/subscriber',
    getParentRoute: () => AuthLayoutRoute,
  } as any)

const AuthLayoutSettingsPortNumberRoute =
  AuthLayoutSettingsPortNumberImport.update({
    path: '/settings/port-number',
    getParentRoute: () => AuthLayoutRoute,
  } as any)

const AuthLayoutSettingsEsimSettingsRoute =
  AuthLayoutSettingsEsimSettingsImport.update({
    path: '/settings/esim-settings',
    getParentRoute: () => AuthLayoutRoute,
  } as any)

const AuthLayoutSettingsChangePasswordRoute =
  AuthLayoutSettingsChangePasswordImport.update({
    path: '/settings/change-password',
    getParentRoute: () => AuthLayoutRoute,
  } as any)

const AuthLayoutSettingsCardDetailsRoute =
  AuthLayoutSettingsCardDetailsImport.update({
    path: '/settings/card-details',
    getParentRoute: () => AuthLayoutRoute,
  } as any)

const AuthLayoutSettingsCancelSubscriptionRoute =
  AuthLayoutSettingsCancelSubscriptionImport.update({
    path: '/settings/cancel-subscription',
    getParentRoute: () => AuthLayoutRoute,
  } as any)

const AuthLayoutRoamingSimReturnRoute = AuthLayoutRoamingSimReturnImport.update(
  {
    path: '/roaming-sim/return',
    getParentRoute: () => AuthLayoutRoute,
  } as any,
)

const AuthLayoutCardDetailsReturnRoute =
  AuthLayoutCardDetailsReturnImport.update({
    path: '/card-details/return',
    getParentRoute: () => AuthLayoutRoute,
  } as any)

const AuthLayoutBoltOnsReturnRoute = AuthLayoutBoltOnsReturnImport.update({
  path: '/bolt-ons/return',
  getParentRoute: () => AuthLayoutRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/*': {
      preLoaderRoute: typeof Import
      parentRoute: typeof rootRoute
    }
    '/_auth': {
      preLoaderRoute: typeof AuthImport
      parentRoute: typeof rootRoute
    }
    '/_signup-flow': {
      preLoaderRoute: typeof SignupFlowImport
      parentRoute: typeof rootRoute
    }
    '/connection-error': {
      preLoaderRoute: typeof ConnectionErrorImport
      parentRoute: typeof rootRoute
    }
    '/forgot-confirm-password': {
      preLoaderRoute: typeof ForgotConfirmPasswordImport
      parentRoute: typeof rootRoute
    }
    '/forgot-password': {
      preLoaderRoute: typeof ForgotPasswordImport
      parentRoute: typeof rootRoute
    }
    '/privacy-policy': {
      preLoaderRoute: typeof PrivacyPolicyImport
      parentRoute: typeof rootRoute
    }
    '/terms': {
      preLoaderRoute: typeof TermsImport
      parentRoute: typeof rootRoute
    }
    '/_auth/_layout': {
      preLoaderRoute: typeof AuthLayoutImport
      parentRoute: typeof AuthImport
    }
    '/_signup-flow/address-details': {
      preLoaderRoute: typeof SignupFlowAddressDetailsImport
      parentRoute: typeof SignupFlowImport
    }
    '/_signup-flow/choose-sim': {
      preLoaderRoute: typeof SignupFlowChooseSimImport
      parentRoute: typeof SignupFlowImport
    }
    '/_signup-flow/email-confirmation': {
      preLoaderRoute: typeof SignupFlowEmailConfirmationImport
      parentRoute: typeof SignupFlowImport
    }
    '/_signup-flow/esim-confirmation': {
      preLoaderRoute: typeof SignupFlowEsimConfirmationImport
      parentRoute: typeof SignupFlowImport
    }
    '/_signup-flow/esim-settings': {
      preLoaderRoute: typeof SignupFlowEsimSettingsImport
      parentRoute: typeof SignupFlowImport
    }
    '/_signup-flow/explore-plans': {
      preLoaderRoute: typeof SignupFlowExplorePlansImport
      parentRoute: typeof SignupFlowImport
    }
    '/_signup-flow/finish-sign-up': {
      preLoaderRoute: typeof SignupFlowFinishSignUpImport
      parentRoute: typeof SignupFlowImport
    }
    '/_signup-flow/login': {
      preLoaderRoute: typeof SignupFlowLoginImport
      parentRoute: typeof SignupFlowImport
    }
    '/_signup-flow/plan-confirmation': {
      preLoaderRoute: typeof SignupFlowPlanConfirmationImport
      parentRoute: typeof SignupFlowImport
    }
    '/_signup-flow/signup': {
      preLoaderRoute: typeof SignupFlowSignupImport
      parentRoute: typeof SignupFlowImport
    }
    '/_signup-flow/sim-activation': {
      preLoaderRoute: typeof SignupFlowSimActivationImport
      parentRoute: typeof SignupFlowImport
    }
    '/openid/callback': {
      preLoaderRoute: typeof OpenidCallbackImport
      parentRoute: typeof rootRoute
    }
    '/_signup-flow/': {
      preLoaderRoute: typeof SignupFlowIndexImport
      parentRoute: typeof SignupFlowImport
    }
    '/_auth/_layout/dashboard': {
      preLoaderRoute: typeof AuthLayoutDashboardImport
      parentRoute: typeof AuthLayoutImport
    }
    '/_auth/_layout/perks': {
      preLoaderRoute: typeof AuthLayoutPerksImport
      parentRoute: typeof AuthLayoutImport
    }
    '/_auth/new-subscription/return': {
      preLoaderRoute: typeof AuthNewSubscriptionReturnImport
      parentRoute: typeof AuthImport
    }
    '/_signup-flow/checkout/return': {
      preLoaderRoute: typeof SignupFlowCheckoutReturnImport
      parentRoute: typeof SignupFlowImport
    }
    '/_signup-flow/openid/login': {
      preLoaderRoute: typeof SignupFlowOpenidLoginImport
      parentRoute: typeof SignupFlowImport
    }
    '/_signup-flow/checkout/': {
      preLoaderRoute: typeof SignupFlowCheckoutIndexImport
      parentRoute: typeof SignupFlowImport
    }
    '/_auth/_layout/bolt-ons/return': {
      preLoaderRoute: typeof AuthLayoutBoltOnsReturnImport
      parentRoute: typeof AuthLayoutImport
    }
    '/_auth/_layout/card-details/return': {
      preLoaderRoute: typeof AuthLayoutCardDetailsReturnImport
      parentRoute: typeof AuthLayoutImport
    }
    '/_auth/_layout/roaming-sim/return': {
      preLoaderRoute: typeof AuthLayoutRoamingSimReturnImport
      parentRoute: typeof AuthLayoutImport
    }
    '/_auth/_layout/settings/cancel-subscription': {
      preLoaderRoute: typeof AuthLayoutSettingsCancelSubscriptionImport
      parentRoute: typeof AuthLayoutImport
    }
    '/_auth/_layout/settings/card-details': {
      preLoaderRoute: typeof AuthLayoutSettingsCardDetailsImport
      parentRoute: typeof AuthLayoutImport
    }
    '/_auth/_layout/settings/change-password': {
      preLoaderRoute: typeof AuthLayoutSettingsChangePasswordImport
      parentRoute: typeof AuthLayoutImport
    }
    '/_auth/_layout/settings/esim-settings': {
      preLoaderRoute: typeof AuthLayoutSettingsEsimSettingsImport
      parentRoute: typeof AuthLayoutImport
    }
    '/_auth/_layout/settings/port-number': {
      preLoaderRoute: typeof AuthLayoutSettingsPortNumberImport
      parentRoute: typeof AuthLayoutImport
    }
    '/_auth/_layout/settings/subscriber': {
      preLoaderRoute: typeof AuthLayoutSettingsSubscriberImport
      parentRoute: typeof AuthLayoutImport
    }
    '/_auth/_layout/settings/support': {
      preLoaderRoute: typeof AuthLayoutSettingsSupportImport
      parentRoute: typeof AuthLayoutImport
    }
    '/_auth/_layout/settings/': {
      preLoaderRoute: typeof AuthLayoutSettingsIndexImport
      parentRoute: typeof AuthLayoutImport
    }
  }
}

// Create and export the route tree

export const routeTree = rootRoute.addChildren([
  Route,
  AuthRoute.addChildren([
    AuthLayoutRoute.addChildren([
      AuthLayoutDashboardRoute,
      AuthLayoutPerksRoute,
      AuthLayoutBoltOnsReturnRoute,
      AuthLayoutCardDetailsReturnRoute,
      AuthLayoutRoamingSimReturnRoute,
      AuthLayoutSettingsCancelSubscriptionRoute,
      AuthLayoutSettingsCardDetailsRoute,
      AuthLayoutSettingsChangePasswordRoute,
      AuthLayoutSettingsEsimSettingsRoute,
      AuthLayoutSettingsPortNumberRoute,
      AuthLayoutSettingsSubscriberRoute,
      AuthLayoutSettingsSupportRoute,
      AuthLayoutSettingsIndexRoute,
    ]),
    AuthNewSubscriptionReturnRoute,
  ]),
  SignupFlowRoute.addChildren([
    SignupFlowAddressDetailsRoute,
    SignupFlowChooseSimRoute,
    SignupFlowEmailConfirmationRoute,
    SignupFlowEsimConfirmationRoute,
    SignupFlowEsimSettingsRoute,
    SignupFlowExplorePlansRoute,
    SignupFlowFinishSignUpRoute,
    SignupFlowLoginRoute,
    SignupFlowPlanConfirmationRoute,
    SignupFlowSignupRoute,
    SignupFlowSimActivationRoute,
    SignupFlowIndexRoute,
    SignupFlowCheckoutReturnRoute,
    SignupFlowOpenidLoginRoute,
    SignupFlowCheckoutIndexRoute,
  ]),
  ConnectionErrorRoute,
  ForgotConfirmPasswordRoute,
  ForgotPasswordRoute,
  PrivacyPolicyRoute,
  TermsRoute,
  OpenidCallbackRoute,
])

/* prettier-ignore-end */
