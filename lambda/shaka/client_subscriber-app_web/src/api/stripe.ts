import { PaymentStatus, SessionStatus } from 'src/types/checkout';
import api from '.';
import { StripeResponse } from 'src/types/stripe';

export const getClientSecretSignUp = (
  planId: string,
  returnURL?: string
): Promise<StripeResponse> =>
  api
    .post(
      `/plans/sign-up/${planId}/${returnURL ? `?return_path=${returnURL}` : ''}`
    )
    .then((res) => res.data);

export const checkSessionStatus = (
  sessionId: string
): Promise<{
  payment_status: PaymentStatus;
  status: SessionStatus;
  expires_at: number;
}> =>
  api
    .get(`/checkout/session/status/?session_id=${sessionId}`)
    .then((res) => res.data);

export const getClientSecretCard =
  ({
    returnURL,
    subscriptionId
  }: {
    returnURL?: string;
    subscriptionId?: string | number;
  }) =>
  (): Promise<string> =>
    api
      .post(
        'subscription/change-card/' +
          (returnURL ? `?return_path=${returnURL}&is_query=true` : ''),
        {
          subscriptionId
        }
      )
      .then((res) => res.data.client_secret);
