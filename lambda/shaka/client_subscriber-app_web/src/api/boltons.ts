import { BoltOn, RoamingBoltOn } from 'src/types/boltons';
import api from '.';
import { StripeResponse } from 'src/types/stripe';

const fetchBoltOns = (): Promise<BoltOn[]> =>
  api.get(`/data/boltons/`).then((res) => res.data);

const fetchRoamingBoltOns = (): Promise<RoamingBoltOn[]> =>
  api.get(`/data/roaming-bolt-ons/`).then((res) => res.data);

const buyBoltOns = (payload: {
  id: number;
  isNew: boolean;
  country_code?: string;
  subscription_id?: number;
}): Promise<StripeResponse> =>
  api.post(`/boltons/buy/`, payload).then((res) => res.data);

const buyBoltOnEU = (payload: {
  days: number;
  subscription_id: number;
}): Promise<StripeResponse> =>
  api.post(`/boltons/buy-eu/`, payload).then((res) => res.data);

export { fetchBoltOns, buyBoltOns, fetchRoamingBoltOns, buyBoltOnEU };
