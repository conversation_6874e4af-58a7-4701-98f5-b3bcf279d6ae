import { ClientPlan } from 'src/types/plans';
import api from './index';
import { Subscriber, SubscriberPlan } from 'src/types/subscriber';
import { Client } from 'src/types/client';

export const fetchPlans = (): Promise<ClientPlan[]> =>
  api.get(`/data/plans/`).then((res) => res.data);

export const fetchClient = (): Promise<Client> =>
  api.get(`/data/client/`).then((res) => res.data);

export const fetchSubscriber = (): Promise<Subscriber> =>
  api.get(`/data/subscriber/`).then((res) => res.data);

export const updateSubscriber = (payload: {
  address?: string;
  date_of_birth?: string;
  name?: string;
  send_marketing?: boolean;
  sim_type?: string;
  plan_name?: string;
  roaming_sim_name?: string;
  subscription_id?: SubscriberPlan['id'];
}): Promise<Subscriber> =>
  api.put(`/data/subscriber/`, payload).then((res) => res.data);
