import { AxiosRequestConfig } from 'axios';
import api from '.';
import { Subscriber, SubscriberPlan } from 'src/types/subscriber';

type SubscriptionId = SubscriberPlan['id'];

type SubscriptionChange = {
  changeType: 'upgrade' | 'downgrade';
  planId: string;
  subscriptionId: SubscriptionId;
};

export type NotificationDialog =
  | 'number_transfer'
  | 'number_porting_progress'
  | 'update_apn'
  | 'set_up_esim';

export const cancelSubscription = (subscription_id: SubscriptionId) =>
  api.post('subscription/cancel/', { subscription_id }).then((res) => res.data);

export const cancelSubscriptionUpdate = (
  planId: number,
  subscriptionId: SubscriptionId
) =>
  api
    .post(`subscription/plan-change/${planId}/cancel/`, {
      subscription_id: subscriptionId
    })
    .then((res) => res.data);

export const hideNotificationDialog = ({
  type,
  subscription_id
}: {
  type: NotificationDialog;
  subscription_id: SubscriptionId;
}) =>
  api
    .post('subscription/hide-notification-dialog/', {
      dialog_type: type,
      subscription_id
    })
    .then((res) => res.data);

export const updateSubscription = ({
  changeType,
  planId,
  requestConfig,
  subscriptionId
}: SubscriptionChange & { requestConfig: AxiosRequestConfig }) =>
  api
    .post(
      'subscription/plan-change/',
      {
        change_type: changeType,
        target_plan_id: planId,
        subscription_id: subscriptionId
      },
      requestConfig
    )
    .then((res) => res.data);

export const activateSim = ({
  code,
  subscription_id
}: {
  code: string;
  subscription_id: SubscriberPlan['id'];
}) =>
  api
    .post('/subscription/activate-sim/', { code, subscription_id })
    .then((res) => res.data);

export const portPhoneNumber = ({
  pacCode,
  phoneNumber,
  date,
  subscription_id
}: {
  phoneNumber: string;
  pacCode: string;
  date: string;
  subscription_id: SubscriberPlan['id'];
}): Promise<Subscriber> =>
  api
    .post('/subscription/pac-code/', {
      pac_code: pacCode,
      phone_number: phoneNumber,
      desired_date: date,
      subscription_id
    })
    .then((res) => res.data);
