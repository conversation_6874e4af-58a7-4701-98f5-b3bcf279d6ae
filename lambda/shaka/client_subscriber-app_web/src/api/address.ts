import axios from 'axios';

const api = axios.create();

export const getAddressAutocompleteByPostCode = async (postCode: string) =>
  api
    .post(
      `https://api.getAddress.io/autocomplete/${postCode}?api-key=1UXtXDoNFkm1heEtFgi9Tg44168`
    )
    .then((res) => res.data);

export const getAddressDetailsByUrl = async (url: string) =>
  api
    .get(`https://api.getAddress.io${url}?api-key=1UXtXDoNFkm1heEtFgi9Tg44168`)
    .then((res) => res.data);
