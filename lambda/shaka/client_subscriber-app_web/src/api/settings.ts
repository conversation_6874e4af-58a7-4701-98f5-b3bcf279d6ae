import { SimSettings } from 'src/types/sim-settings';
import api from '.';

export const fetchEsimSettings = (
  subscription_id?: number
): Promise<SimSettings> => {
  return api
    .get(`/data/esim-settings/${subscription_id || ''}`)
    .then((res) => res.data);
};

export const shareEsim = (payload: { email: string }, subscription_id?: number) => {
  const url = subscription_id ? `/share-esim/${subscription_id}/` : '/share-esim/';
  return api.post(url, payload).then((res) => res.data);
}
