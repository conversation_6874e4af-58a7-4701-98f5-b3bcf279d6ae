import { twMerge } from 'tailwind-merge';

export function Corner({ className }: { className?: string }) {
  return (
    <svg
      viewBox="0 0 15 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={twMerge('size-6', className)}
    >
      <path
        d="M12.403 2.802H6.55033C5.51551 2.802 4.52309 3.21308 3.79136 3.9448C3.05964 4.67653 2.64856 5.66895 2.64856 6.70378V12.5564"
        stroke="#1EC25F"
        stroke-width="3.90177"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
}
