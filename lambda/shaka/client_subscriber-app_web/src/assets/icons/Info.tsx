import React from 'react';
import { twMerge } from 'tailwind-merge';

export function InfoIcon({
  className,
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      viewBox="0 0 59 60"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={twMerge('w-5 h-5', className)}
      {...props}
    >
      <g clipPath="url(#clip0_412_1794)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M29.6492 60C45.5235 60 58.3921 46.5686 58.3921 30C58.3921 13.4315 45.5235 0 29.6492 0C13.7749 0 0.90625 13.4315 0.90625 30C0.90625 46.5686 13.7749 60 29.6492 60ZM23.49 40.1786C22.0726 40.1786 20.9236 41.3778 20.9236 42.8571C20.9236 44.3366 22.0726 45.5357 23.49 45.5357H29.6492H35.8084C37.2257 45.5357 38.3747 44.3366 38.3747 42.8571C38.3747 41.3778 37.2257 40.1786 35.8084 40.1786H32.2155V27.8571C32.2155 26.3778 31.0665 25.1786 29.6492 25.1786H25.543C24.1257 25.1786 22.9767 26.3778 22.9767 27.8571C22.9767 29.3365 24.1257 30.5357 25.543 30.5357H27.0828V40.1786H23.49ZM33.7553 17.1429C33.7553 19.5098 31.9169 21.4286 29.6492 21.4286C27.3814 21.4286 25.543 19.5098 25.543 17.1429C25.543 14.7759 27.3814 12.8571 29.6492 12.8571C31.9169 12.8571 33.7553 14.7759 33.7553 17.1429Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_412_1794">
          <rect
            width="57.4858"
            height="60"
            fill="white"
            transform="translate(0.90625)"
          />
        </clipPath>
      </defs>
    </svg>
  );
}
