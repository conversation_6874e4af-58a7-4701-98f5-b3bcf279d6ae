import React from 'react';
import { twMerge } from 'tailwind-merge';

export function AirDropIcon({
  className,
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 86 86"
      stroke="currentColor"
      className={twMerge('w-6 h-6', className)}
      {...props}
    >
      <g clipPath="url(#clip0_603_1317)">
        <path
          d="M85.9837 29.3945C84.4551 16.6961 72.8149 7.15547 59.3942 2.73789C65.5083 9.35586 69.8754 19.6355 69.8754 32.25H74.5954L55.0102 54.002C54.5903 53.9012 54.204 53.75 53.7504 53.75H45.6879V32.25H64.5004C64.5004 12.9 52.9274 0 43.0004 0C33.0735 0 21.5004 12.9 21.5004 32.25H40.313V53.75H32.2505C31.7969 53.75 31.4106 53.9012 30.9907 54.002L11.4055 32.25H16.1254C16.1254 19.6355 20.4926 9.35586 26.6067 2.73789C13.186 7.17227 1.54576 16.7129 0.0172442 29.3945C-0.167521 30.923 1.15943 32.25 2.70474 32.25H4.16607L27.1274 57.7645C27.0098 58.2012 26.8587 58.6379 26.8587 59.125V80.625C26.8587 83.598 29.2606 86 32.2337 86H53.7336C56.7067 86 59.1086 83.598 59.1086 80.625V59.125C59.1086 58.6379 58.9575 58.218 58.8399 57.7645L81.818 32.25H83.2794C84.8415 32.25 86.1684 30.9398 85.9837 29.3945Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_603_1317">
          <rect width="86" height="86" fill="currentColor" />
        </clipPath>
      </defs>
    </svg>
  );
}
