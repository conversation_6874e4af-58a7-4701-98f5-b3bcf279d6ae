import React from 'react';
import { twMerge } from 'tailwind-merge';

interface CheckGreenProps extends React.SVGProps<SVGSVGElement> {
  fillColor?: string;
  strokeColor?: string;
}

export function CheckGreenIcon({
  className,
  fillColor = '#1EC25F',
  strokeColor = 'white',
  ...props
}: CheckGreenProps) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 17 16"
      className={twMerge('w-6 h-6', className)}
      {...props}
    >
      <path
        d="M16.3414 7.80108C16.3414 12.1095 12.8486 15.6022 8.54008 15.6022C4.23152 15.6022 0.738739 12.1095 0.738739 7.80108C0.738739 3.49266 4.23152 0 8.54008 0C12.8486 0 16.3414 3.49266 16.3414 7.80108Z"
        fill={fillColor}
      />
      <path
        d="M11.1925 5.10438L7.62621 10.4542L5.84305 8.84924"
        fill={fillColor}
      />
      <path
        d="M11.1925 5.10438L7.62621 10.4542L5.84305 8.84924"
        stroke={strokeColor}
        strokeWidth="1.09544"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
