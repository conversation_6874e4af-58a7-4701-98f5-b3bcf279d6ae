import React from 'react';
import { twMerge } from 'tailwind-merge';

export function LockerIcon({
  className,
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 10 13"
      className={twMerge('w-5 h-5', className)}
      {...props}
    >
      <path
        d="M8.36357 5.23178H1.63529C1.1708 5.23178 0.79425 5.60833 0.79425 6.07282V11.119C0.79425 11.5835 1.1708 11.9601 1.63529 11.9601H8.36357C8.82807 11.9601 9.2046 11.5835 9.2046 11.119V6.07282C9.2046 5.60833 8.82807 5.23178 8.36357 5.23178Z"
        stroke="#1EC25F"
        strokeWidth="1.02153"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.94315 5.23194V3.97039C7.94315 3.18969 7.63307 2.44096 7.08098 1.88893C6.52895 1.33689 5.78023 1.02676 4.99953 1.02676C4.21883 1.02676 3.47011 1.33689 2.91808 1.88893C2.36604 2.44096 2.05591 3.18969 2.05591 3.97039V5.23194"
        stroke="#1EC25F"
        strokeWidth="1.02153"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M4.99938 9.01639C5.23162 9.01639 5.41989 8.82812 5.41989 8.59587C5.41989 8.36363 5.23162 8.17535 4.99938 8.17535C4.76713 8.17535 4.57886 8.36363 4.57886 8.59587C4.57886 8.82812 4.76713 9.01639 4.99938 9.01639Z"
        stroke="#1EC25F"
        strokeWidth="1.02153"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
