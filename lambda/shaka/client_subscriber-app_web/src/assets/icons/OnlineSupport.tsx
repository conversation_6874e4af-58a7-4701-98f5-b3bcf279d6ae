import React from 'react';
import { twMerge } from 'tailwind-merge';

export function OnlineSupportIcon({
  className,
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 67 67"
      className={twMerge('w-5 h-5', className)}
      {...props}
    >
      <g clipPath="url(#clip0_1136_10524)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M33.4357 7.18101C31.4338 7.14512 29.4446 7.50457 27.5819 8.2388C25.7193 8.97302 24.0198 10.0676 22.5809 11.4597C21.142 12.8519 19.9919 14.5143 19.1966 16.3517C18.4046 18.1812 17.9799 20.1484 17.9464 22.1416V23.9286V34.6994V43.0714C17.9464 45.7145 15.8038 47.8571 13.1607 47.8571H7.17857C3.21396 47.8571 0 44.6432 0 40.6786V31.1071C0 27.1425 3.21396 23.9286 7.17857 23.9286H10.7679V22.113V22.0588C10.8124 19.1126 11.4382 16.2041 12.6087 13.5C13.7792 10.7959 15.4718 8.3494 17.5894 6.30058C19.707 4.25173 22.2082 2.6409 24.9495 1.56035C27.6708 0.487669 30.5756 -0.0414883 33.5 0.00253792C36.4244 -0.0414883 39.3292 0.487669 42.0505 1.56035C44.7918 2.6409 47.293 4.25173 49.4106 6.30058C51.5283 8.3494 53.221 10.7959 54.3911 13.5C55.5617 16.2041 56.1872 19.1126 56.2317 22.0588L56.2326 22.113L56.2321 23.9286H59.8214C63.7859 23.9286 67 27.1425 67 31.1071V40.6786C67 44.6432 63.7859 47.8571 59.8214 47.8571H56.2321V50.25C56.2321 53.7402 54.8457 57.0878 52.3772 59.5558C50.2605 61.6725 47.4972 62.9939 44.5515 63.3274C43.3225 65.5188 40.9771 67 38.2857 67H31.1071C27.1425 67 23.9286 63.7859 23.9286 59.8214C23.9286 55.8569 27.1425 52.6429 31.1071 52.6429H38.2857C40.8794 52.6429 43.1518 54.0183 44.4132 56.08C45.4983 55.8301 46.5015 55.2798 47.3014 54.4801C48.4233 53.3583 49.0536 51.8365 49.0536 50.25V22.1416C49.0201 20.1485 48.5956 18.1812 47.8034 16.3517C47.0081 14.5143 45.858 12.8519 44.4191 11.4597C42.9802 10.0676 41.2807 8.97302 39.4181 8.2388C37.5554 7.50457 35.5662 7.14512 33.5643 7.18101C33.5214 7.18178 33.4786 7.18178 33.4357 7.18101Z"
          fill="black"
        />
      </g>
      <defs>
        <clipPath id="clip0_1136_10524">
          <rect width="67" height="67" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}
