import React from 'react';
import { twMerge } from 'tailwind-merge';

export function WiFiIcon({
  className,
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 9 8"
      className={twMerge('w-5 h-5', className)}
      {...props}
    >
      <path
        d="M8.65442 2.39079C7.65449 1.39087 6.32401 0.837189 4.90263 0.837189C3.48124 0.837189 2.15076 1.39087 1.15084 2.39079C0.919448 2.62218 0.919448 2.98579 1.15084 3.21718C1.26653 3.33287 1.41528 3.39072 1.56403 3.39072C1.71278 3.39072 1.86153 3.33287 1.97722 3.21718C2.75402 2.43211 3.79527 2.00239 4.90263 2.00239C6.00998 2.00239 7.05123 2.43211 7.82803 3.21718C8.05942 3.44857 8.42303 3.44857 8.65442 3.21718C8.88581 2.98579 8.88581 2.61392 8.65442 2.39079Z"
        fill="black"
      />
      <path
        d="M2.70455 3.95287C2.47316 4.18425 2.47316 4.54786 2.70455 4.77925C2.82024 4.89495 2.96899 4.95279 3.11774 4.95279C3.26649 4.95279 3.41524 4.89495 3.53093 4.77925C4.29121 4.01898 5.52252 4.01898 6.27453 4.77099C6.49766 5.00238 6.86953 5.00238 7.10092 4.77099C7.33231 4.5396 7.33231 4.17599 7.10092 3.9446C5.88613 2.73808 3.91933 2.73808 2.70455 3.95287Z"
        fill="black"
      />
      <path
        d="M4.91082 7.05169C5.41286 7.05169 5.81985 6.64471 5.81985 6.14267C5.81985 5.64063 5.41286 5.23364 4.91082 5.23364C4.40878 5.23364 4.0018 5.64063 4.0018 6.14267C4.0018 6.64471 4.40878 7.05169 4.91082 7.05169Z"
        fill="black"
      />
    </svg>
  );
}
