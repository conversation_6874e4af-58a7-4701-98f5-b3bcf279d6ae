export function PiggyBankIcon({ ...props }: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 39 39"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M20.4719 5.79615C22.4473 1.96138 26.5485 0.978372 28.6972 1.07549C30.2382 1.14513 30.9324 2.51344 30.9324 3.55635V8.4409C32.8838 9.42189 34.1889 10.7296 34.9898 12.3595C35.7796 13.9665 36.0191 15.7715 36.0612 17.6217L37.9973 19.212C38.3193 19.4765 38.506 19.8715 38.506 20.2883V26.4691C38.506 26.6719 38.4617 26.8722 38.3761 27.0562C36.8504 30.3386 34.1452 32.1123 30.9324 33.3709V35.1457C30.9324 36.6839 29.6853 37.9314 28.1467 37.9314H23.272C21.7335 37.9314 20.4863 36.6839 20.4863 35.1457V33.7963H14.5231V34.9278C14.5231 36.4664 13.2759 37.7135 11.7374 37.7135H6.81876C5.28208 37.7135 4.03285 36.4697 4.03302 34.9289C4.03318 33.3608 4.03338 30.93 4.03296 30.1459C4.0099 30.0673 3.97588 29.9584 3.92855 29.8096L3.87301 29.6352C3.77422 29.3257 3.6453 28.9218 3.50398 28.4513L3.49156 28.4098C2.53515 25.2248 0.759134 19.3102 3.30422 13.6694C3.49373 13.2494 3.7067 12.8381 3.94474 12.4377C2.62894 11.3963 1.30991 9.92569 0.578119 7.96466C0.24194 7.06379 0.699726 6.06093 1.60061 5.72475C2.5015 5.38857 3.50434 5.84637 3.84052 6.74724C4.29791 7.97301 5.16388 8.97125 6.14776 9.74033C7.02407 8.94517 8.06331 8.24028 9.28682 7.65402C12.0644 6.32312 15.7226 5.63533 20.4719 5.79615ZM20.5459 13.6555C20.5459 14.6171 19.7664 15.3966 18.8048 15.3966H13.6374C12.6758 15.3966 11.8963 14.6171 11.8963 13.6555C11.8963 12.6939 12.6758 11.9144 13.6374 11.9144H18.8048C19.7664 11.9144 20.5459 12.6939 20.5459 13.6555ZM27.3932 19.2755C28.571 19.2755 29.5259 18.3206 29.5259 17.1427C29.5259 15.9648 28.571 15.0099 27.3932 15.0099C26.2152 15.0099 25.2604 15.9648 25.2604 17.1427C25.2604 18.3206 26.2152 19.2755 27.3932 19.2755Z"
        fill="currentColor"
      />
    </svg>
  );
}
