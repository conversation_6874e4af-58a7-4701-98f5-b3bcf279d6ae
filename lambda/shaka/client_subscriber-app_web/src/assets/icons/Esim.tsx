export function EsimIcon({ ...props }: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      viewBox="0 0 133 133"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M55.5006 7.23592C55.5006 3.23963 52.2611 0 48.2647 0C44.2685 0 41.0288 3.23963 41.0288 7.23592V23.0845H37.5563C29.5637 23.0845 23.0845 29.5637 23.0845 37.5563V41.0288H7.23592C3.23963 41.0288 0 44.2685 0 48.2647C0 52.2611 3.23963 55.5006 7.23592 55.5006H23.0845V77.4994H7.23592C3.23963 77.4994 0 80.7389 0 84.7353C0 88.7315 3.23963 91.9712 7.23592 91.9712H23.0845V95.4437C23.0845 103.436 29.5637 109.915 37.5563 109.915H41.0288V125.764C41.0288 129.76 44.2685 133 48.2647 133C52.2611 133 55.5006 129.76 55.5006 125.764V109.915H77.4994V125.764C77.4994 129.76 80.7389 133 84.7353 133C88.7315 133 91.9712 129.76 91.9712 125.764V109.915H95.4437C103.436 109.915 109.915 103.436 109.915 95.4437V91.9712H125.764C129.76 91.9712 133 88.7315 133 84.7353C133 80.7389 129.76 77.4994 125.764 77.4994H109.915V55.5006H125.764C129.76 55.5006 133 52.2611 133 48.2647C133 44.2685 129.76 41.0288 125.764 41.0288H109.915V37.5563C109.915 29.5637 103.436 23.0845 95.4437 23.0845H91.9712V7.23592C91.9712 3.23963 88.7315 0 84.7353 0C80.7389 0 77.4994 3.23963 77.4994 7.23592V23.0845H55.5006V7.23592ZM70.4454 79.7658C67.1152 79.7658 64.4155 82.4655 64.4155 85.7958C64.4155 89.126 67.1152 91.8257 70.4454 91.8257H89.7412C93.0714 91.8257 95.7707 89.126 95.7707 85.7958C95.7707 82.4655 93.0714 79.7658 89.7412 79.7658H70.4454Z"
        fill="#4b4a4a"
      />
      <rect
        x="33.25"
        y="35.9102"
        width="66.5"
        height="61.18"
        rx="17.29"
        fill="white"
      />
      <defs>
        <filter
          id="filter0_b_1_4448"
          x="12.5061"
          y="30.1748"
          width="107.33"
          height="74.7052"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="15.0852" />
          <feComposite
            in2="SourceAlpha"
            operator="in"
            result="effect1_backgroundBlur_1_4448"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_backgroundBlur_1_4448"
            result="shape"
          />
        </filter>
      </defs>
    </svg>
  );
}
