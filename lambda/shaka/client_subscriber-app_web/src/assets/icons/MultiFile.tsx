export function MultiFileIcon({ ...props }: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 39 39"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.5356 0C11.3817 0 10.4463 0.935404 10.4463 2.08929C10.4463 3.24316 11.3817 4.17857 12.5356 4.17857H30.6428C31.0275 4.17857 31.3392 4.49038 31.3392 4.875V31.3393C31.3392 32.4931 32.2746 33.4286 33.4285 33.4286C34.5823 33.4286 35.5178 32.4931 35.5178 31.3393V4.875C35.5178 2.18261 33.3351 0 30.6428 0H12.5356ZM4.17822 11.1429C4.17822 8.83509 6.04903 6.96429 8.35679 6.96429H23.6782C25.986 6.96429 27.8568 8.83509 27.8568 11.1429V34.8214C27.8568 37.1291 25.986 39 23.6782 39H8.35679C6.04903 39 4.17822 37.1291 4.17822 34.8214V11.1429ZM8.70414 13.9286C8.70414 12.967 9.48367 12.1875 10.4452 12.1875H21.5881C22.5496 12.1875 23.3291 12.967 23.3291 13.9286C23.3291 14.8901 22.5496 15.6696 21.5881 15.6696H10.4452C9.48367 15.6696 8.70414 14.8901 8.70414 13.9286ZM10.4466 19.1518C9.48501 19.1518 8.70551 19.9313 8.70551 20.8929C8.70551 21.8544 9.48501 22.6339 10.4466 22.6339H21.5894C22.551 22.6339 23.3305 21.8544 23.3305 20.8929C23.3305 19.9313 22.551 19.1518 21.5894 19.1518H10.4466ZM8.70339 27.8571C8.70339 26.8956 9.48292 26.1161 10.4445 26.1161H16.0159C16.9775 26.1161 17.757 26.8956 17.757 27.8571C17.757 28.8188 16.9775 29.5982 16.0159 29.5982H10.4445C9.48292 29.5982 8.70339 28.8188 8.70339 27.8571Z"
        fill="currentColor"
      />
    </svg>
  );
}
