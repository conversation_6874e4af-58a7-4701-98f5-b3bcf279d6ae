import React from 'react';
import { twMerge } from 'tailwind-merge';

export function EmailIcon({
  className,
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 128 128"
      className={twMerge('w-6 h-6', className)}
      {...props}
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M64 128C99.3462 128 128 99.3462 128 64C128 28.6538 99.3462 0 64 0C28.6538 0 0 28.6538 0 64C0 99.3462 28.6538 128 64 128ZM24 46.1052C24 39.9714 28.973 34.9984 35.1067 34.9984H92.8933C99.0269 34.9984 104 39.9714 104 46.1052V82.6652C104 88.7989 99.0269 93.7719 92.8933 93.7719H35.1067C28.973 93.7719 24 88.7989 24 82.6652V46.1052ZM36.2853 87.9227L51.8384 74.6539C52.9366 73.7178 53.0667 72.0686 52.1305 70.9717C51.1942 69.8734 49.5452 69.7433 48.4483 70.6795L32.8952 83.9483C31.7969 84.8844 31.6667 86.5336 32.603 87.6306C33.5391 88.7288 35.1883 88.8589 36.2853 87.9227ZM58.3266 67.5497C59.9031 68.9131 61.9197 69.6044 64 69.6095C66.0816 69.6069 68.0955 68.9053 69.6669 67.5561L93.5372 46.8375C94.6278 45.8909 94.7438 44.2417 93.7988 43.1527C92.8523 42.062 91.2031 41.9459 90.1139 42.8911L66.2678 63.5894L66.2562 63.5995C65.718 64.0689 64.8916 64.3878 64 64.3852C63.1072 64.3903 62.2666 64.0625 61.7564 63.6084L37.8859 42.8909C36.7969 41.9458 35.1477 42.0619 34.2013 43.1525C33.2561 44.2417 33.3722 45.8908 34.4628 46.8373L58.3258 67.549C58.3252 67.5486 58.3245 67.5484 58.3239 67.548L58.3278 67.5509L58.3266 67.5497ZM91.7159 87.9227C92.8127 88.8589 94.4619 88.7288 95.3981 87.6306C96.3344 86.5336 96.2042 84.8844 95.1061 83.9483L79.553 70.6795C78.4561 69.7434 76.8069 69.8734 75.8708 70.9717C74.9345 72.0686 75.0647 73.7177 76.1628 74.6539L91.7159 87.9227Z"
        fill="currentColor"
      />
    </svg>
  );
}
