import { useEffect, useRef, useState } from 'react';
import Dialog from 'src/components/common/Dialog';
import Drawer from 'src/components/common/Drawer';
import { twMerge } from 'tailwind-merge';

export type DrawerProps = {
  title: string;
  isOpen: boolean;
  onClose?: () => void;
  disableClose?: boolean;
  withOverflow?: boolean;
  minHeightMobile?: string;
};

export type WithDrawerProps = {
  closeDialog: () => void;
};

interface BoltOnsContentProps {
  navPosition: 'fixed' | 'absolute';
}

export default function withDrawer<T extends object>(
  WrappedComponent: React.ComponentType<
    T & BoltOnsContentProps & WithDrawerProps
  >
) {
  return function MultipurposeDrawer({
    title,
    isOpen,
    onClose,
    disableClose,
    withOverflow = true,
    minHeightMobile,
    ...props
  }: DrawerProps & Omit<T, keyof BoltOnsContentProps>) {
    const [isMobile] = useState(window.innerWidth < 640);
    const drawerContentRef = useRef<HTMLDivElement>(null);
    const dialogContentRef = useRef<HTMLDivElement>(null);
    const [open, setOpen] = useState(false);

    useEffect(() => {
      // for smooth transition effect
      if (isOpen) {
        setOpen(true);
      }
    }, [isOpen]);

    const handleClose = () => {
      if (disableClose) return;

      setOpen(false);

      // for smooth transition effect
      setTimeout(() => {
        onClose && onClose();
      }, 300);
    };

    return (
      <>
        {isMobile ? (
          <Drawer
            title={title}
            isOpen={open}
            onClose={handleClose}
            adaptiveHeight
            minHeight={minHeightMobile}
          >
            <div
              className={twMerge(
                'max-h-[calc(100dvh_-_80px)] mt-6 relative',
                withOverflow && 'overflow-y-auto'
              )}
              ref={drawerContentRef}
            >
              <WrappedComponent
                {...(props as T)}
                navPosition="fixed"
                closeDialog={handleClose}
              />
            </div>
          </Drawer>
        ) : (
          <Dialog
            title={title}
            isOpen={isOpen}
            onClose={handleClose}
            panelStyles="bg-[#F3F3F3]"
          >
            <div
              className={twMerge(
                'relative max-h-[800px] min-h-[350px] text-black',
                withOverflow && 'overflow-y-auto'
              )}
              ref={dialogContentRef}
            >
              <WrappedComponent
                {...(props as T)}
                navPosition="absolute"
                closeDialog={handleClose}
              />
            </div>
          </Dialog>
        )}
      </>
    );
  };
}
