export enum LocalKey {
  ID_TOKEN = 'id_token',
  ACCESS_TOKEN = 'access_token',
  EXPIRES_AT = 'expires_at',
  PLAN_ID = 'plan_id',
  CLIENT_SECRET = 'client_secret',
  SESSION_EXPIRES_AT = 'session_expires_at',
  SESSION_ID = 'session_id',
  REFRESH_TOKEN = 'refresh_token',
  SIM_SETTINGS_SELECTED_OPTION = 'sim_settings_selected_option',
  OPENID_STATE = 'code_verifier',
  OPENID_CODE = 'state_plaintext',
  NEW_SUBSCRIPTION_STEP = 'new_subscription_step',
  CURRENT_PLAN_INDEX = 'current_plan_index',
  ROAMING_ZONE = 'roaming_zone',
  CORRELATION_ID = 'correlationId'
}

export const setStripeSessionInfo = (
  session_id: string,
  expires_at: number
) => {
  localStorage.setItem(LocalKey.SESSION_EXPIRES_AT, expires_at.toString());
  localStorage.setItem(LocalKey.SESSION_ID, session_id);
};

export const removeStripeInfo = () => {
  localStorage.removeItem(LocalKey.PLAN_ID);
  localStorage.removeItem(LocalKey.CLIENT_SECRET);
  localStorage.removeItem(LocalKey.SESSION_EXPIRES_AT);
  localStorage.removeItem(LocalKey.SESSION_ID);
};

export const getAwaitingPlanId = () => localStorage.getItem(LocalKey.PLAN_ID);

export const setPlanId = (planId: number) =>
  localStorage.setItem(LocalKey.PLAN_ID, planId.toString());

export const getStripeSessionId = () =>
  localStorage.getItem(LocalKey.SESSION_ID);

export const getStripeSessionExpiresAt = () =>
  localStorage.getItem(LocalKey.SESSION_EXPIRES_AT);

export const setSubscriberSession = (
  id_token: string,
  access_token: string,
  expires_at: string
) => {
  localStorage.setItem(LocalKey.ID_TOKEN, id_token);
  localStorage.setItem(LocalKey.ACCESS_TOKEN, access_token);
  localStorage.setItem(LocalKey.EXPIRES_AT, expires_at);
};

export const setSimSettingsSelectedType = (value: string) =>
  localStorage.setItem(LocalKey.SIM_SETTINGS_SELECTED_OPTION, value);
export const getSimSettingsSelectedType = () =>
  localStorage.getItem(LocalKey.SIM_SETTINGS_SELECTED_OPTION);
export const removeSimSettingsSelectedType = () =>
  localStorage.removeItem(LocalKey.SIM_SETTINGS_SELECTED_OPTION);

export const clearStorage = () => {
  localStorage.clear();
};

export const deleteAllStorageExceptKey = (keyToKeep: string) => {
  Object.keys(localStorage).forEach((key) => {
    if (key !== keyToKeep) {
      localStorage.removeItem(key);
    }
  });
};

export const setOpenIdData = (data: Record<string, string>) => {
  localStorage.setItem(LocalKey.OPENID_STATE, data.state);
  localStorage.setItem(LocalKey.OPENID_CODE, data.code);
};

export const getOpenIdData = () => {
  const state = localStorage.getItem(LocalKey.OPENID_STATE) || '';
  const code = localStorage.getItem(LocalKey.OPENID_CODE) || '';
  return { state_plaintext: state, code_verifier: code };
};

export const removeOpenIdData = () => {
  localStorage.removeItem(LocalKey.OPENID_STATE);
  localStorage.removeItem(LocalKey.OPENID_CODE);
};

export const setNewSubscriptionStep = (step: number) =>
  localStorage.setItem(LocalKey.NEW_SUBSCRIPTION_STEP, step.toString());
export const getNewSubscriptionStep = () =>
  localStorage.getItem(LocalKey.NEW_SUBSCRIPTION_STEP);
export const removeNewSubscriptionStep = () =>
  localStorage.removeItem(LocalKey.NEW_SUBSCRIPTION_STEP);

export const setCurrentPlanIndex = (index: number) =>
  localStorage.setItem(LocalKey.CURRENT_PLAN_INDEX, index.toString());
export const getCurrentPlanIndex = () =>
  localStorage.getItem(LocalKey.CURRENT_PLAN_INDEX);
export const removeCurrentPlanIndex = () =>
  localStorage.removeItem(LocalKey.CURRENT_PLAN_INDEX);

export const setRoamingZone = (zone: string) =>
  localStorage.setItem(LocalKey.ROAMING_ZONE, zone);
export const getRoamingZone = () => localStorage.getItem(LocalKey.ROAMING_ZONE);
export const removeRoamingZone = () =>
  localStorage.removeItem(LocalKey.ROAMING_ZONE);

export const setCorrelationId = (correlationId: string) =>
  localStorage.setItem(LocalKey.CORRELATION_ID, correlationId);

export const getCorrelationId = () =>
  localStorage.getItem(LocalKey.CORRELATION_ID);
