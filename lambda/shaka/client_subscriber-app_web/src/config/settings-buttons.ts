import {
  CustomerSupportIcon,
  HeadPhonesIcon,
  PiggyBankIcon
} from 'src/assets/icons';
import { ROUTES } from './routes';
import { isOpenId } from './env-vars';

export const subscriptionLinks = [
  {
    to: ROUTES.Settings,
    label: 'Upgrade plan',
    search: { 'plan-change': 1 }
  },
  {
    id: 'bolt-ons',
    to: ROUTES.Settings,
    label: 'View bolt-ons',
    search: { modal: 'bolt-ons' }
  },
  {
    to: ROUTES.Dashboard,
    label: 'View usage',
    disabled: true
  }
];

export const travelSimLinks = [
  {
    to: ROUTES.Settings,
    label: 'Add more data',
    search: { 'travel-esim': 2, 'action': 'add-data' },
    saveRoamingZone: true
  }
  // {
  //   to: ROUTES.Settings,
  //   label: 'Add new country',
  //   search: { 'travel-esim': 1, 'action': 'add-country' },
  // }
];

export const supportLinks = [
  { to: ROUTES.Support, label: 'Request help', Icon: HeadPhonesIcon }
];

export const subscriberLinks = [
  ...(!isOpenId
    ? [
        {
          to: ROUTES.Subscriber,
          label: 'Change details',
          Icon: CustomerSupportIcon
        }
      ]
    : []),
  {
    to: ROUTES.CardDetails,
    label: 'Update billing',
    Icon: PiggyBankIcon
  }
];
