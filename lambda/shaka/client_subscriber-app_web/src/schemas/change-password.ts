import * as yup from "yup";
import { passwordScheme } from "./common";

export type ChangePasswordFormInputs = {
  old_password: string;
  new_password: string;
};

export const schema = yup
  .object({
    old_password: yup.string().required("No old password provided."),
    new_password: passwordScheme.when(
      "old_password",
      (old_password, schema) => {
        return schema.test({
          test: (new_password) =>
            <PERSON><PERSON><PERSON>(new_password) &&
            <PERSON><PERSON><PERSON>(old_password) &&
            new_password !== old_password[0],
          message: "Passwords shouldn't match",
        });
      }
    ),
  })
  .required() as yup.ObjectSchema<ChangePasswordFormInputs>;
