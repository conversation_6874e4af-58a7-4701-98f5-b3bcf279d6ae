import * as yup from "yup";

const dateErrorMessage = "Please enter a valid date.";

export const dobScheme = yup
  .string()
  .required("No date provided.")
  .min(10, "hidden")
  .test("is a valid date", dateErrorMessage, (value) => {
    if (!value) return false;

    const [year, month, day] = value!.split("-").map((v) => parseInt(v));
    const date = new Date(year, month - 1, day);

    if (isNaN(date.getTime()) || date.getMonth() != month - 1) {
      return false;
    }

    return true;
  })
  .test("is user over 18", "Age must be 18 or above.", function (value) {
    const date = new Date(value!);

    const isOver18 = (date: Date) => {
      const diff = Date.now() - date.getTime();
      const ageDate = new Date(diff);
      return Math.abs(ageDate.getUTCFullYear() - 1970) >= 18;
    };

    return isOver18(date);
  });

export const emailScheme = yup
  .string()
  .email("Email is invalid")
  .required("No email provided.");

export const subscriberName = yup
  .string()
  .required("Name is required")
  .matches(
    /^[a-zA-Z\s.'-]+$/,
    "Name shouldn't contain any numbers or other symbols."
  )
  .transform((value) => value.trim());

export const passwordScheme = yup
  .string()
  .required("No password provided.")
  .matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])(?=.{8,})/,
    "Must Contain 8 Characters, One Uppercase, One Lowercase, One Number and One Special Case Character"
  );
