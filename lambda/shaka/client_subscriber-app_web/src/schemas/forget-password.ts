import * as yup from "yup";
import { passwordScheme } from "./common";

export type ForgotConfirmPasswordInputs = {
  verification_code: string;
  email: string;
  new_password: string;
  new_password_confirmation: string;
};

export const schema = yup
  .object({
    email: yup
      .string()
      .email("Email is invalid")
      .required("No email provided."),
    new_password: passwordScheme,
    new_password_confirmation: yup
      .string()
      .when("new_password", (new_password, schema) => {
        return schema.test({
          test: (confirmation) =>
            <PERSON><PERSON><PERSON>(new_password) &&
            <PERSON><PERSON><PERSON>(confirmation) &&
            confirmation === new_password[0],
          message: "Passwords must match",
        });
      }),
  })
  .required() as yup.ObjectSchema<ForgotConfirmPasswordInputs>;
