import { isWeekendOrHoliday } from 'src/helpers/date';
import * as yup from 'yup';

export type PortNumberFormInputs = {
  phoneNumber: string;
  pacCode: string;
  date: Date;
  selectedPlan?: number;
};

export const schema = yup
  .object({
    phoneNumber: yup
      .string()
      .required('Port number is required')
      .matches(
        /^(\+44\s?|0)(7\d{3}|\d{4})\s?\d{3}\s?\d{3}$/,
        'Invalid phone number. Must be in the format "+44234567890" or "01234567890"'
      ),
    pacCode: yup
      .string()
      .required('PAC code is required')
      .matches(
        /[A-Z][A-Z][A-Z][0-9][0-9][0-9][0-9][0-9][0-9]/,
        'Invalid PAC code.'
      ),
    date: yup
      .date()
      .optional()
      .test('is-not-a-holiday', 'Please select a valid working day', (date) => {
        if (!date) return true;

        return !isWeekendOrHoliday(date);
      }),
    selectedPlan: yup.number().required('Please select a plan')
  })
  .required() as yup.ObjectSchema<PortNumberFormInputs>;
