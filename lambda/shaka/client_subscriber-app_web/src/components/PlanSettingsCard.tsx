import { useContext, useMemo } from 'react';
import { ClientContext } from 'src/context/ClientContext';
import useSubscription from 'src/hooks/useSubscription';
import PlanStatus from './common/PlanStatus';
import Chip from './common/Chip';
import {
  PlanChangeStatus,
  PlanChangeType,
  Subscriber,
  SubscriberPlan
} from 'src/types/subscriber';
import { currencyRounded, epochToDate, getBgIndex } from 'src/helpers';

import Logo from './common/Logo';
import { twMerge } from 'tailwind-merge';

const isPlainCard = window.clientConfig.plainDashboardCard;

const getSubscriptionStatus = (subscriber: Subscriber | null) => {
  if (!subscriber) return '';

  const {
    sim_activation_status: simStatus,
    subscription_status: subscriptionStatus,
    latest_plan_change: latestPlanChange
  } = subscriber.plans?.[0] || {};

  if (subscriptionStatus === 'active') {
    switch (simStatus) {
      case 'not-assigned':
        return 'Your plan is being set up';
      case 'pending-activation':
        return 'Your SIM is pending activation';
      default:
        break;
    }
  }

  if (
    latestPlanChange &&
    latestPlanChange.change_type === PlanChangeType.UPGRADE &&
    latestPlanChange.status === PlanChangeStatus.IN_PROGRESS
  ) {
    return 'Your plan is being upgraded, we will let you know when it is ready!';
  }

  switch (subscriptionStatus) {
    case 'inactive':
      return 'Your subscription is inactive. Please wait for activation';
    case 'cancelled':
      return 'Your subscription is cancelled';
    default:
      return '';
  }
};

export default function PlanSettingsCard({
  plan = {} as SubscriberPlan
}: {
  plan?: SubscriberPlan;
}) {
  const { subscriber } = useSubscription();
  const { getPlanById } = useContext(ClientContext);

  const {
    id,
    title,
    data,
    voice,
    sms,
    price,
    bg,
    // can_cancel_change: canCancelPlanChange,
    next_bill_date_epoch: billDate,
    latest_plan_change: latestPlanChange,
    sim_type,
    next_bill_amount: nexBillAmount
  } = plan;
  const withDiscount = price !== nexBillAmount;
  const bgIndex = getBgIndex(id, bg);
  const status = getSubscriptionStatus(subscriber);
  const simType = sim_type === 'esim' ? 'eSIM' : 'SIM';

  const isPlanCancelled =
    latestPlanChange &&
    latestPlanChange.change_type === PlanChangeType.CANCELLATION;

  const planInfo = useMemo(() => {
    if (plan.plan_id) {
      return getPlanById(plan.plan_id);
    }

    return null;
  }, [getPlanById, subscriber?.plans]);

  return (
    <>
      <div className="plan-card-wrapper">
        <div
          className="plan-card-detailed flex flex-col size-full text-base py-6 px-6 justify-between absolute inset-0"
          style={{ backgroundImage: `url(/background/card-${bgIndex}.png)` }}
        >
          {isPlainCard ? (
            <div className="w-full flex justify-between text-black font-bold text-xl">
              <div className="plan-card-logo">
                <Logo disableRedirect />
              </div>
              <span className="opacity-20">{simType}</span>
            </div>
          ) : (
            <div className="card-top text-xl font-bold text-black/20">
              {simType}
            </div>
          )}
          <div className="flex justify-between gap-4">
            <h3
              className={twMerge(
                'plan-card-title card-text grow text-2xl mb-4 font-bold',
                isPlainCard ? 'capitalize' : 'uppercase'
              )}
            >
              {title}
            </h3>
            {price && (
              <div className="text-right">
                <p className="font-bold text-2xl relative card-text">
                  {withDiscount ? (
                    <>
                      <span className="text-sm line-through mr-2 font-semibold">
                        {currencyRounded(Number(price))}
                      </span>
                      <span>{currencyRounded(Number(nexBillAmount))}</span>
                      <span className="absolute -top-6 right-0 text-xs font-semibold bg-black/10 rounded-full py-0.5 px-3 ">
                        % offer
                      </span>
                    </>
                  ) : (
                    currencyRounded(Number(price))
                  )}
                </p>
                {billDate &&
                  (isPlanCancelled ? (
                    <p className="text-xs whitespace-nowrap card-text">
                      cancelled {epochToDate(billDate)}
                    </p>
                  ) : (
                    <p className="text-xs whitespace-nowrap card-text">
                      next on {epochToDate(billDate)}
                    </p>
                  ))}
              </div>
            )}
          </div>
          <div className="flex justify-between items-end">
            {status ? (
              <PlanStatus>{status}</PlanStatus>
            ) : (
              planInfo && (
                <div className="flex flex-wrap gap-1.5">
                  <Chip>
                    {sms?.is_unlimited
                      ? 'Unlimited SMS'
                      : `${planInfo.sms} SMS`}
                  </Chip>
                  <Chip>
                    {data?.is_unlimited
                      ? 'Unlimited data'
                      : `${planInfo.data}GB data`}
                  </Chip>
                  <Chip>
                    {voice?.is_unlimited
                      ? 'Unlimited calls'
                      : `${planInfo.voice} mins`}
                  </Chip>
                  <Chip>EU roaming</Chip>
                  <Chip>5G</Chip>
                </div>
              )
            )}
          </div>
        </div>
      </div>
    </>
  );
}
