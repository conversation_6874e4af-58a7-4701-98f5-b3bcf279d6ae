import { ElementType, useCallback, useMemo, useState } from "react";
import Slide from "./Slide";
import { ArrowDownIcon } from "src/assets/icons/ArrowDown";

const wrapperStyles = "relative flex justify-center w-full h-full";
const navBtnStyle = "cursor-pointer";
const nawButtonsStyle = "flex justify-center m-auto z-50";

function mod(a: number, b: number) {
  return ((a % b) + b) % b;
}

type VerticalCarouselProps<T> = {
  defaultIndex?: number;
  slides: (T & { key: number })[];
  showNavigation: boolean;
  Component: ElementType;
  onChange?: (index: number) => void;
  onCardClick?: (slide: unknown) => void;
};

const VerticalCarousel = <T,>({
  slides,
  showNavigation,
  Component,
  onCardClick,
  onChange,
  defaultIndex,
}: VerticalCarouselProps<T>) => {
  const [customIndex, setCustomIndex] = useState(0);
  const index = defaultIndex || customIndex;
  const backSlidesAmount = 2;

  const modBySlidesLength = useCallback(
    (index: number) => mod(index, slides.length),
    [slides.length]
  );

  const moveSlide = (direction: number) => {
    if (onChange) {
      onChange(modBySlidesLength(index + direction));
      return;
    }

    setCustomIndex(modBySlidesLength(index + direction));
  };

  const getPresentableSlides = useMemo(() => {
    const presentableSlides = [];

    for (let i = 0; i < slides.length; i++) {
      if (slides[modBySlidesLength(index + i)]) {
        presentableSlides.push(slides[modBySlidesLength(index + i)]);
      }
    }

    return presentableSlides;
  }, [slides, modBySlidesLength, index]);

  return (
    <>
      {showNavigation && (
        <div className={nawButtonsStyle}>
          <div className={navBtnStyle} onClick={() => moveSlide(-1)}>
            <ArrowDownIcon className="size-3 rotate-180" />
          </div>
          <div className={navBtnStyle} onClick={() => moveSlide(1)}>
            <ArrowDownIcon className="size-3" />
          </div>
        </div>
      )}
      <div className={wrapperStyles}>
        {getPresentableSlides.map((slide, presentableIndex) => (
          <Slide
            key={slide?.key}
            content={slide}
            moveSlide={moveSlide}
            backSlidesAmount={backSlidesAmount}
            index={presentableIndex}
            Component={Component}
            onCardClick={onCardClick}
          />
        ))}
      </div>
    </>
  );
};

export default VerticalCarousel;
