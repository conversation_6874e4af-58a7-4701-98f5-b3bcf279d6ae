@keyframes rotate {
    0% {
        transform: rotate3d(0, 0, 0, 0deg);
    }

    50% {
        transform: rotate3d(0, 1, -0.5, 90deg);
    }

    100% {
        transform: rotate3d(1, 1, 0, -0.5turn);
        margin: -24px 0;
    }
}

@keyframes fade-in {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }

}

.card {
    transform-style: preserve-3d;
    transform-origin: center;
    margin: 20px 0;
}

.card.rotated {
    animation: rotate 0.75s linear forwards 0.2s;
}


.side-front,
.side-back {
    backface-visibility: hidden;
}

.side-back {
    transform: rotateY(.5turn);
}

.sim-activation-input {
    opacity: 0;
    animation: fade-in 0.25s ease-in forwards 0.75s;
}