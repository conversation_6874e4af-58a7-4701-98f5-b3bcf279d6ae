import { useEffect, useState } from 'react';
import SimSpaces from './SimSpaces';
import { twMerge } from 'tailwind-merge';
import './style.css';
import clsx from 'clsx';
import Logo from '../common/Logo';

const cardStyles = 'w-[61vw] h-[100vw] max-w-[268px] max-h-[350px]';

const CardSide = ({
  isBack,
  serial
}: {
  isBack?: boolean;
  serial?: string;
}) => {
  return (
    <div
      className={clsx(
        'absolute top-0 bottom-0 right-0 left-0',
        isBack ? 'side-back' : 'side-front'
      )}
    >
      <div
        className={clsx(
          'bg-white relative rounded-xl overflow-hidden',
          cardStyles
        )}
      >
        <div
          className={
            'sim-activation-logo m-auto flex items-center justify-center'
          }
        >
          <Logo disableRedirect />
        </div>
        <div
          className={twMerge(
            'absolute inset-0 text-primary flex items-end ',
            !isBack && 'scale-x-[-1]'
          )}
        >
          <SimSpaces backSide={isBack} code={serial} />
        </div>
      </div>
    </div>
  );
};

export default function SimCard({
  backSide,
  serialNumber = ''
}: {
  backSide?: boolean;
  serialNumber?: string;
}) {
  const [isBackAnimation, setIsBackAnimation] = useState(false);

  useEffect(() => {
    if (backSide) {
      setIsBackAnimation(true);
    }
  }, [backSide]);

  return (
    <div
      className={clsx(
        'card rotated z-20',
        cardStyles,
        isBackAnimation && !backSide && ' rotated-back',
        'relative transition-transform duration-1000'
      )}
    >
      <CardSide serial={serialNumber} />
      <CardSide isBack serial={serialNumber} />
    </div>
  );
}
