import { useEffect, useRef, useState } from 'react';
import { EsimSettings as EsimSettingsContent } from 'src/components/esim-settings';
import Button from './common/Button';
import { RightArrowIcon } from 'src/assets/icons';
import ProgressLinks, { ProgressLinksProps } from './common/ProgressLinks';
import { twMerge } from 'tailwind-merge';

export default function EsimSettings({
  progressLinksModalView,
  onShareEsim,
  esimId,
  ...props
}: ProgressLinksProps & {
  progressLinksModalView?: boolean;
  onShareEsim?: () => void;
  esimId?: number;
}) {
  const [scrolled, setScrolled] = useState(false);
  const scrollRef = useRef<HTMLDivElement>(null);

  const scrollPage = () => {
    if (scrollRef.current) {
      scrollRef.current.scrollTo({
        behavior: 'smooth',
        top: scrollRef.current.scrollHeight
      });
      setScrolled(true);
    }
  };

  const handleScroll = () => {
    const element = scrollRef.current;

    if (
      element &&
      element.scrollTop + element.clientHeight >= element.scrollHeight
    ) {
      setScrolled(true);
    }
  };

  useEffect(() => {
    const element = scrollRef.current;

    element?.addEventListener('scroll', handleScroll);

    return () => {
      element?.removeEventListener('scroll', handleScroll);
    };
  }, []);

  useEffect(() => {
    const handleWheel = (event: WheelEvent) => {
      const element = scrollRef.current;
      if (!element) return;

      if (element.scrollHeight > element.clientHeight) {
        element.scrollTop += event.deltaY;
        event.preventDefault();
      }
    };

    document.addEventListener('wheel', handleWheel, { passive: false });

    return () => {
      document.removeEventListener('wheel', handleWheel);
    };
  }, []);

  return (
    <>
      <div
        className={twMerge(
          progressLinksModalView
            ? 'h-full overflow-hidden pb-16'
            : 'esim-settings-content'
        )}
        ref={scrollRef}
        onWheel={handleScroll}
      >
        <EsimSettingsContent onShareEsim={onShareEsim} esimId={esimId} />
      </div>

      {!scrolled && (
        <div
          className={twMerge(
            'bottom-0 right-0',
            progressLinksModalView
              ? 'fixed md:right-[calc(50%_-_250px)] p-6 pl-5 sm:bottom-10'
              : 'fixed md:right-max-nav p-6 pr-5'
          )}
        >
          <Button
            type="submit"
            color="secondary"
            rightIcon={<RightArrowIcon className="rotate-90" />}
            onClick={scrollPage}
            narrow
            fullWidth
            transparentBg
          >
            Complete all steps
          </Button>
        </div>
      )}
      <ProgressLinks
        {...props}
        hideNextButton={!scrolled}
        isModalView={progressLinksModalView}
      />
    </>
  );
}
