import { useEffect, useState } from 'react';
import { fetchEsimSettings } from 'src/api/settings';
import { SimSettings } from 'src/types/sim-settings';
import { InfoIcon } from 'src/assets/icons/Info';
import ErrorText from 'src/components/common/ErrorText';
import Android from 'src/components/esim-settings/EsimSetupGuide/Android';
import Desktop from 'src/components/esim-settings/EsimSetupGuide/Desktop';
import IOSSafari from 'src/components/esim-settings/EsimSetupGuide/IOS-Safari';
import OldIOS from 'src/components/esim-settings/EsimSetupGuide/Old-IOS';
import ManualSetupDrawerDrawer from 'src/components/esim-settings/ManualSetupDrawer';
import { checkIsIos11OrHigher } from 'src/helpers';
import { useRequest } from 'src/hooks/useRequest';
import VideoDrawer from './VideoDrawer';
import ShareEsim from './ShareEsim.tsx';
import useSubscription from 'src/hooks/useSubscription.tsx';
import DefaultIOS from 'components/esim-settings/EsimSetupGuide/DefaultIos.tsx';


const checkIsSafari = () => {
  const userAgent = window.navigator.userAgent;
  const safari = /Safari/.test(userAgent);
  return safari;
};

const checkIsAndroid = () => {
  const userAgent = window.navigator.userAgent;
  return /Android/.test(userAgent);
};

const checkIsIos = () => {
  const userAgent = window.navigator.userAgent;
  return /iPad|iPhone|iPod/.test(userAgent);
};

const checkIsDesktop = () => {
  const userAgent = window.navigator.userAgent;
  return !/Mobile/.test(userAgent);
};

function shouldShowInstallButton(): boolean {
  const ua = window.navigator.userAgent;
  const isIphone = /iPhone/.test(ua);
  const isSafari =
    /Safari/.test(ua) &&
    !/CriOS|Chrome|FxiOS|EdgiOS|OPiOS|Opera Mini|UCBrowser/.test(ua);
  return isIphone && isSafari;
}

export function EsimSettings({
  esimId,
  onShareEsim
}: {
  esimId?: number;
  onShareEsim?: () => void;
}) {
  const { error, run: runFetchEsimSettings } = useRequest(fetchEsimSettings);
  const [esimSettings, setEsimSettings] = useState<SimSettings | undefined>(
    undefined
  );
  const [settingsForPlanId, setSettingsForPlanId] = useState<number | undefined>(undefined);

  const { subscriberPlanSimList, currentPlanIndex } = useSubscription();

  const selectedPlan = (esimId ? subscriberPlanSimList.find((item) => item.id === esimId) : undefined) || subscriberPlanSimList[currentPlanIndex];
  const plan = selectedPlan;
  // added extra condition because in manage tab => esim instructions the first sim did not have share button, although I had more than 1 sim
  const isFirstEverSim =
    selectedPlan.initialIndex === 0 && subscriberPlanSimList.length === 1;
  const isLocalEsim =
    selectedPlan.type === 'plan' && selectedPlan.data.sim_type == 'esim';
  const isTravelEsim = selectedPlan.type === 'sim';
  const isEsim = isLocalEsim || isTravelEsim;

  const isShareablePlan = isEsim && !isFirstEverSim;

  const isSafari = checkIsSafari();
  const isIos = checkIsIos();
  const isAndroid = checkIsAndroid();
  const isDesktop = checkIsDesktop();
  const isLastIos = isIos && checkIsIos11OrHigher();
  const isOldIos = isIos && !checkIsIos11OrHigher();
  const shouldShowInstall = shouldShowInstallButton();

  const [isManualSetupDrawerOpen, setIsManualSetupDrawerOpen] = useState(false);
  const [isVideoDrawerOpen, setIsVideoDrawerOpen] = useState(false);

  const qr_code = esimSettings?.qr_code || '';
  const ios_link = esimSettings?.ios_settings?.app_link || '';
  const sm_dp =
    esimSettings?.android_settings?.sm_dp_activation ||
    esimSettings?.ios_settings?.sm_dp;

  let settingsId = esimId;
  if (!esimId) {
    settingsId = plan?.id;
  }


  useEffect(() => {
    let intervalId: NodeJS.Timeout | undefined;

    if (!qr_code) {
      intervalId = setInterval(() => {
        runFetchEsimSettings(settingsId).then(setEsimSettings).then(() => setSettingsForPlanId(settingsId));
      }, 2000);
    } else {
      clearInterval(intervalId);
    }
    return () => clearInterval(intervalId);
  }, [qr_code, runFetchEsimSettings, settingsId, setEsimSettings, setSettingsForPlanId]);

  useEffect(() => {
    runFetchEsimSettings(settingsId);
    if (settingsForPlanId != settingsId) {
      setEsimSettings(undefined);
    }
  }, [settingsId, settingsForPlanId, setEsimSettings, runFetchEsimSettings]);

  return (
    <div className="text-black">
      {isShareablePlan && (
        <div className="mb-4">
          <ShareEsim
            onSuccessfulShare={onShareEsim}
            planId={esimId || plan?.id}
          />
        </div>
      )}
      {/*LOGIC*/}
      {/*conditions:*/}
      {/*1. The device is a mobile device*/}
      {/*2.It is an iPhone*/}
      {/*3. The browser is Safari*/}
      {/*Only when all of these conditions are met should the "Click to Install" button be displayed.*/}
      {/*For example:*/}
      {/*If I am using an iPhone and the Safari browser on my phone, then I should see the button.*/}
      {/*If I am using an iPhone but the Chrome browser, then I should not see the button.*/}
      {/*If I am using a MacBook with the Safari browser, it is not a mobile device, so I should not see the button.*/}
      {/*If these requirements are not met, the UI should default to displaying the Desktop component instead.*/}
      {/*// local plan */}
      {/*// IOS - IOSSafari, OldIOS, DefaultIOS*/}
      {/*// Android*/}
      {/*// Desktop*/}
      {shouldShowInstall && (
        <IOSSafari
          isLocalPlan={isLocalEsim}
          link={ios_link}
          onVideoInstructionClick={() => setIsVideoDrawerOpen(true)}
        />
      )}
      {(isOldIos || (isLastIos && !isSafari)) && (
        <OldIOS isLocalPlan={isLocalEsim} />
      )}
      {isAndroid && <Android isLocalPlan={isLocalEsim} />}
      {isDesktop && <Desktop isLocalPlan={isLocalEsim} qr_code={qr_code} />}
      {!shouldShowInstall && isIos && (
        <DefaultIOS isLocalPlan={isLocalEsim} qr_code={qr_code} />
      )}
      {sm_dp && (
        <div className="flex justify-end items-center gap-2 mt-4">
          <InfoIcon className="size-4" />

          <span className="font-semibold text-[10px]">
            eSIM installation not working? Try{' '}
            <button
              className="underline hover:opacity-60 hover:no-underline"
              onClick={() => setIsManualSetupDrawerOpen(true)}
            >
              manual set up here
            </button>
          </span>
        </div>
      )}
      <ErrorText>{error}</ErrorText>
      <ManualSetupDrawerDrawer
        isOpen={isManualSetupDrawerOpen}
        onClose={() => setIsManualSetupDrawerOpen(false)}
        title="Manual set up"
        esimSettings={esimSettings}
        isTravelEsim={isTravelEsim}
      />
      <VideoDrawer
        isOpen={isVideoDrawerOpen}
        onClose={() => setIsVideoDrawerOpen(false)}
        title="eSIM INSTRUCTIONS - iOS"
        withOverflow={false}
      />
    </div>
  );
}
