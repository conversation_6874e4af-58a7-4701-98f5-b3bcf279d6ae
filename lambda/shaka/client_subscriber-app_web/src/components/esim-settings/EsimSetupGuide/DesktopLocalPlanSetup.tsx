import WhiteBlock from 'components/common/WhiteBlock.tsx';
import { FingerTapIcon } from 'src/assets/icons/FingerTap.tsx';

export function DesktopLocalPlanSetup({
  label = 'Step 2',
  labelColor = '#CC387F'
}: {
  label?: string;
  labelColor?: string;
}) {
  return (
    <WhiteBlock title="Set-up Data" label={label} labelColor={labelColor}>
      <div className="text-sm my-2">
        <p className="font-semibold">In your settings head to:</p>
        <p className="italic">
          Mobile Service &gt; Click this eSIM &gt; Mobile Data Network
        </p>
      </div>

      <div className="mt-3">
        <div className="h-[100px] m-auto">
          <img
            src="/apn-setup.png"
            className="w-full h-full object-contain"
            alt="APN setup"
          />
        </div>
        <div className="text-xs flex gap-2 items-center justify-center text-[#1EC25F] mt-1">
          <FingerTapIcon />
          <span className="font-semibold">Tap to change</span>
        </div>

        <p className="text-sm mt-4 font-bold">
          Make sure your APN setting is set to “gamma”
        </p>
      </div>
    </WhiteBlock>
  );
}
