import { UploadBoxIcon } from 'src/assets/icons/UploadBox';
import { FingerTapIcon } from 'src/assets/icons/FingerTap';
import WhiteBlock from 'src/components/common/WhiteBlock';
import InternetWarning from './InternetWarning';
import AndroidThirdStep from 'components/esim-settings/EsimSetupGuide/AndroidThirdStep.tsx';

export default function Android({ isLocalPlan }: { isLocalPlan: boolean }) {
  return (
    <div className="space-y-5">
      <WhiteBlock title="Check your email" label="step 1">
        <div className="flex gap-5 items-center ml-1 mb-2">
          <div className="grow">
            <UploadBoxIcon className="size-6" />
          </div>
          <span className="text-sm">
            <span className="font-semibold">
              Download the QR code attachment
            </span>{' '}
            to your photos
          </span>
        </div>
      </WhiteBlock>
      <WhiteBlock title="Install your eSIM" label="step 2">
        <InternetWarning />
        <div className="text-sm my-2">
          <p className="font-semibold">In your settings head to:</p>
          <p className="italic">
            Connections &gt; SIM Manager &gt; Add eSIM &gt; From QR Code &gt;{' '}
            Scan from Photo
          </p>
        </div>
        <div className="w-[48%] m-auto">
          <img src="/qr-with-phone.png" className="w-full" />
        </div>
        <div className="flex gap-2 items-center justify-center text-[#1EC25F] mt-2">
          <FingerTapIcon />
          <span className="text-xs font-semibold">Tap “Scan from Photo”</span>
        </div>
        <div className="mt-3 text-sm">
          <span className="font-bold">Select the downloaded QR Code</span> from
          your Photos and follow the prompts to install
        </div>
      </WhiteBlock>
      {isLocalPlan ? null : <AndroidThirdStep />}
    </div>
  );
}
