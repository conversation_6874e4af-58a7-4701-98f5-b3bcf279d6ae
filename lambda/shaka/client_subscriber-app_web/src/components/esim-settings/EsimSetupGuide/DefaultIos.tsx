import { FingerTapIcon } from 'src/assets/icons/FingerTap';
import IosSecondStep from './IosSecondStep';
import QrCode from 'src/components/common/QrCode';
import WhiteBlock from 'src/components/common/WhiteBlock';
import InternetWarning from 'components/esim-settings/EsimSetupGuide/InternetWarning.tsx';
import { IosSecondStepLocalPlan } from 'components/esim-settings/EsimSetupGuide/IosSecondStepLocalPlan.tsx';

export default function DefaultIOS({
  qr_code,
  isLocalPlan
}: {
  qr_code: string;
  isLocalPlan: boolean;
}) {
  return (
    <div className="space-y-5">
      <WhiteBlock title="Install your eSIM" label="step 1">
        <InternetWarning />
        <div className="flex flex-col gap-7">
          <div className="flex flex-col gap-2 text-base">
            <div className="flex gap-2">
              <FingerTapIcon />
              <span className="font-semibold">
                Hard press and hold the QR code
              </span>
            </div>
            <span>
              You may need to do this multiple times for the QR to be recognised
            </span>
            <span className="font-semibold">Then tap “Add eSIM”</span>
          </div>
          <QrCode qr_code={qr_code} />
        </div>
      </WhiteBlock>
      {isLocalPlan ? <IosSecondStepLocalPlan /> : <IosSecondStep />}
    </div>
  );
}
