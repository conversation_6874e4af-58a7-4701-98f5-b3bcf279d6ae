import { FingerTapIcon } from 'src/assets/icons/FingerTap';
import IosSecondStep from './IosSecondStep';
import WhiteBlock from 'src/components/common/WhiteBlock';
import InternetWarning from './InternetWarning';
import Button from 'src/components/common/Button';
import { IosSecondStepLocalPlan } from 'components/esim-settings/EsimSetupGuide/IosSecondStepLocalPlan.tsx';

export default function IOSSafari({
  link,
  onVideoInstructionClick,
  isLocalPlan
}: {
  link: string;
  onVideoInstructionClick: () => void;
  isLocalPlan: boolean;
}) {
  return (
    <div className="space-y-5">
      <WhiteBlock title="Install your eSIM" label="step 1">
        <InternetWarning />
        <div>
          <a href={link} target="_blank">
            <Button color="black" size="medium" fullWidth>
              Click to install
            </Button>
          </a>
        </div>
        <div className="flex flex-col gap-2.5 text-sm mt-6">
          <div className="flex gap-2">
            <FingerTapIcon />
            <span className="font-semibold">
              First time installing an eSIM?
            </span>
          </div>
          <p>Click the link below to watch an instructional video.</p>
          <Button
            color="white"
            withBorder
            size="small"
            onClick={onVideoInstructionClick}
          >
            Installation instructions
          </Button>
          <p>
            We recommend you install your eSIM now, but you can always do it
            later - head to the app or follow the instructions in the email we
            just sent you.
          </p>
        </div>
      </WhiteBlock>
      {isLocalPlan ? <IosSecondStepLocalPlan /> : <IosSecondStep />}
    </div>
  );
}
