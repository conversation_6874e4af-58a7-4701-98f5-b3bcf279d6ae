import { useState } from 'react';
import { AddUserIcon } from 'src/assets/icons/AddUser';
import Button from 'src/components/common/Button';
import WhiteBlock from 'src/components/common/WhiteBlock';
import ShareEsimDrawer from '../ShareEsimDrawer';
import { ShareEsimInputs } from 'src/schemas/share-sim';
import { shareEsim } from 'src/api/settings';
import NotificationDialog from 'components/common/NotificationDialog.tsx';

export default function ShareEsim({
  planId,
  onSuccessfulShare
}: {
  planId: number | undefined;
  onSuccessfulShare?: () => void;
}) {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSuccessModalOpen, setIsSuccessModalOpen] = useState(false);
  const handleSimSharing = (data: ShareEsimInputs) => {
    shareEsim(data, planId)
      .then(() => {
        setIsDrawerOpen(false);
        setIsSuccessModalOpen(true);
      })
      .catch((err) => {
        setError(
          err?.response?.data?.message ||
            'Something went wrong. Please try again later.'
        );
      });
  };

  return (
    <WhiteBlock>
      <div className="flex items-center gap-2.5 mb-4">
        <AddUserIcon className="w-4 h-4" />
        <span className="text-sm font-semibold">
          Is this eSIM not for your phone?
        </span>
      </div>
      <Button color="black" size="medium" onClick={() => setIsDrawerOpen(true)}>
        Share eSIM
      </Button>
      <NotificationDialog
        isOpen={isSuccessModalOpen}
        onCancel={() => {
          setIsSuccessModalOpen(false);
          onSuccessfulShare?.();
        }}
        title="eSIM shared successfully"
        dialogType="success"
        cancelActionColor="default"
        cancelButtonText="Continue"
      />

      <ShareEsimDrawer
        isOpen={isDrawerOpen}
        onClose={() => setIsDrawerOpen(false)}
        title="Share eSIM"
        onShareEsim={handleSimSharing}
        error={error}
      />
    </WhiteBlock>
  );
}
