import { toast } from 'react-toastify';
import { CopyPasteIcon } from 'src/assets/icons/CopyPaste';
import { twMerge } from 'tailwind-merge';

export default function CopyArea({
  title,
  label,
  layout = 'horizontal',
  titleBold
}: {
  title: string;
  label?: string;
  layout?: 'vertical' | 'horizontal';
  titleBold?: boolean;
}) {
  const copyCodeToClipboard = () => {
    navigator.clipboard.writeText(title);
    toast.success('Code copied to clipboard');
  };

  return (
    <button
      className="w-full flex justify-between items-center gap-3 py-2.5 px-5 border-dotted bg-[#EBEBEB] rounded-xl text-black"
      onClick={copyCodeToClipboard}
    >
      {layout === 'vertical' ? (
        <div className="flex flex-col truncate">
          <span className="text-left text-[10px] text-[#8D8D8D]">{label}</span>
          <p
            className={twMerge(
              'text-sm font-semibold text-left truncate max-h-full',
              titleBold && 'font-semibold'
            )}
          >
            {title}
          </p>
        </div>
      ) : (
        <div className="flex gap-2 text-xs w-[calc(100%_-_30px)]">
          <span className="whitespace-nowrap font-semibold">{label}</span>
          <p className={twMerge('truncate', titleBold && 'font-semibold')}>
            {title}
          </p>
        </div>
      )}

      <CopyPasteIcon className="min-w-5" />
    </button>
  );
}
