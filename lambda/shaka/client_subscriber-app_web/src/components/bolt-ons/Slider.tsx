import { PropsWithChildren } from "react";
import SlickSlider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import "./styles.css";
import clsx from "clsx";

interface Props {
  oneItemMode?: boolean;
}


export default function Slider({ children, oneItemMode }: PropsWithChildren<Props>) {
  const settings = {
    infinite: false,
    speed: 500,
    slidesToShow: 2,
    slidesToScroll: 1,
    arrows: false,
    variableWidth: true,
    touchThreshold: 20,
  };

  return <SlickSlider {...settings} className={clsx(oneItemMode && "one-item-slider")}>{children}</SlickSlider>;
}
