import { useContext } from 'react';
import BackButton from '../common/BackButton';
import Container from '../common/Container';
import {
  EmbeddedCheckout,
  EmbeddedCheckoutProvider
} from '@stripe/react-stripe-js';
import { StripeContext } from 'src/context/StripeContext';

export default function BoltonPayment({
  onReturn,
  clientSecret
}: {
  onReturn: () => void;
  clientSecret: string;
}) {
  const { stripePromise } = useContext(StripeContext);

  return (
    <>
      <BackButton onClick={onReturn} />
      <Container className="m-0 p-0 md:pt-0 translate-y-10 max-h-[65vh] min-h-0 overflow-scroll">
        <EmbeddedCheckoutProvider
          stripe={stripePromise}
          options={{
            clientSecret
          }}
        >
          <EmbeddedCheckout />
        </EmbeddedCheckoutProvider>
      </Container>
    </>
  );
}
