import { PropsWithChildren } from "react";
import { twMerge } from "tailwind-merge";

interface Props {
  title: string;
  withRightGradient?: boolean;
}

export default function BoltOnBlock({
  title,
  children,
  withRightGradient,
}: PropsWithChildren<Props>) {
  return (
    <div>
      <div className="font-semibold mb-3">{title}</div>
      <div className="relative">
        {children}
        <div
          className={twMerge(
            "absolute h-full top-0 right-0 w-1/12",
            withRightGradient && "bg-gradient-to-l from-bg-color to-transparent"
          )}
        />
      </div>
    </div>
  );
}
