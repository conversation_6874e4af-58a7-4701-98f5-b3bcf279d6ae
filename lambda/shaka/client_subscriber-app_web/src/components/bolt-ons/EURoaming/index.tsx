import CountryIcon from 'src/components/common/CountryIcon';
import * as countriesIcons from 'country-flag-icons/react/3x2';
import { currencyRounded } from 'src/helpers';
import IncrementalInput from 'src/components/common/IncrementalInput';
import ProgressLinks from 'src/components/common/ProgressLinks';
import { useMemo, useState } from 'react';
import useBoltOns from 'src/hooks/useBoltOns';
import useSubscription from 'src/hooks/useSubscription';
import { useNavigate } from '@tanstack/react-router';
import { ROUTES } from 'src/config/routes';

export default function EURoaming({
  isNavAbsolute,
  setClientSecret,
  openTerms
}: {
  isNavAbsolute: boolean;
  setClientSecret: (client_secret: string) => void;
  openTerms: () => void;
}) {
  const navigate = useNavigate();

  const { buyBoltOnEU } = useBoltOns();
  const { currentPlan } = useSubscription();

  const { day_price, days_used, days_total } = currentPlan.roaming_bolt_on_eu;

  const [days, setDays] = useState(1);
  const [isNextLoading, setIsNextLoading] = useState(false);

  const daysLimit = useMemo(() => {
    const daysLeft =
      Math.ceil(
        (new Date(
          new Date().getFullYear(),
          new Date().getMonth() + 1,
          0
        ).getTime() -
          new Date().getTime()) /
          (1000 * 60 * 60 * 24)
      ) + 1;

    const availableDays = daysLeft - (days_total - days_used);

    return availableDays > 0 ? availableDays : 0;
  }, [days_total, days_used]);

  const noDaysAvailable = daysLimit <= 0;

  const handleConfirmAndPayBoltonEU = () => {
    setIsNextLoading(true);
    buyBoltOnEU({
      days,
      subscription_id: currentPlan.id
    }).then(({ client_secret }) => {
      if (client_secret) {
        setClientSecret(client_secret);
        return;
      }

      navigate({ to: ROUTES.Settings, search: { modal: 'bolt-ons-payment' } });
    });
  };

  return (
    <div className="p-3 py-4">
      <div className="flex justify-between">
        <p className="font-semibold">European daily roaming pass</p>

        <CountryIcon
          Icon={countriesIcons.EU}
          title="European daily roaming pass"
        />
      </div>
      <div className="h-[1px] my-5 bg-gray-200" />
      <p className="text-sm mb-6">
        Buy more EU roaming days for{' '}
        {new Date().toLocaleString('default', { month: 'long' })}{' '}
        {new Date().getFullYear()}
      </p>
      <div className="flex justify-between items-center">
        <IncrementalInput
          min={noDaysAvailable ? 0 : 1}
          max={daysLimit}
          defaultValue={noDaysAvailable ? 0 : days}
          extension="days"
          extensionSingular="day"
          onChange={setDays}
        />
        <p className="text-primary text-lg font-semibold">
          {currencyRounded(day_price * days)}
        </p>
      </div>
      <div className="h-[1px] my-5 mt-10 bg-gray-200" />
      <div className="flex justify-between items-center">
        <div className="text-xs font-semibold">
          {days_used} / {days_total} days used this month
        </div>
        <button className="text-gray-400 text-xs underline" onClick={openTerms}>
          Terms apply
        </button>
      </div>
      <ProgressLinks
        hideBackButton
        onNextClick={handleConfirmAndPayBoltonEU}
        absolute={isNavAbsolute}
        isLoading={isNextLoading}
        disabledNext={noDaysAvailable}
      />
    </div>
  );
}
