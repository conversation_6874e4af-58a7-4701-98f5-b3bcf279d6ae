import { currencyRounded } from "src/helpers";
import { BoltOn } from "src/types/boltons";
import { twMerge } from "tailwind-merge";

export default function BoltOnItem({
  selected,
  onSelect,
  price,
  title,
}: {
  selected: boolean;
  onSelect: () => void;
} & Partial<BoltOn>) {
  return (
    <div className="min-w-[100px] px-1.5">
      <div
        className={twMerge(
          "border bg-white rounded-xl max-w-[250px] h-full min-h-[60px] py-2.5 px-3 flex flex-col justify-between border-transparent cursor-pointer",
          selected ? "border-black" : "md:hover:border-gray-200"
        )}
        onClick={onSelect}
      >
        <div className="font-bold text-lg leading-5">{title}</div>
        <div className="font-semibold text-sm text-right text-primary">
          {currencyRounded(Number(price))}
        </div>
      </div>
    </div>
  );
}
