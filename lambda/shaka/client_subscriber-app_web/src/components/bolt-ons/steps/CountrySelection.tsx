import ProgressLinks from 'src/components/common/ProgressLinks';
import { currencyRounded } from 'src/helpers';
import { Combobox, Tab } from '@headlessui/react';
import { Fragment, useMemo, useState } from 'react';
import { twMerge } from 'tailwind-merge';
import { Transition } from '@headlessui/react';
import { RoamingBoltOn, BoltOnCountry } from 'src/types/boltons';
import { getTimeDifferenceFromNow } from 'src/helpers/date';
import Terms from '../Terms';
import useBoltOns from 'src/hooks/useBoltOns';
import { countries } from 'src/config/countries';
import { SearchIcon } from 'src/assets/icons/Search';
import * as countriesIcons from 'country-flag-icons/react/3x2';
import CountryOption from './CountryOption';
import CountryIcon from 'src/components/common/CountryIcon';
import EURoaming from '../EURoaming';
import useSubscription from 'src/hooks/useSubscription';
import TabTitle from 'src/components/common/TabTitle';

export default function CountrySelection({
  selectedBoltOn = {} as RoamingBoltOn,
  selectedCountry,
  onNextClick,
  onSelect,
  navPosition,
  setClientSecret
}: {
  selectedBoltOn?: RoamingBoltOn;
  selectedCountry?: BoltOnCountry;
  onNextClick: () => void;
  onSelect: (value?: BoltOnCountry) => void;
  navPosition: 'fixed' | 'absolute';
  setClientSecret: (client_secret: string) => void;
}) {
  const { boltOns } = useBoltOns();
  const { currentPlan } = useSubscription();

  const hasEUBoltOn = Boolean(currentPlan?.roaming_bolt_on_eu);
  const hasBoltOns = boltOns.length > 0;

  const [query, setQuery] = useState('');
  const [selectedOption, setSelectedOption] = useState<
    BoltOnCountry | undefined
  >(selectedCountry);
  const [selectedTab, setSelectedTab] = useState(0);
  const [isCountryDropdownOpen, setIsCountryDropdownOpen] =
    useState(!selectedCountry);
  const [isTermsOpen, setIsTermsOpen] = useState(false);
  const isNavAbsolute = navPosition === 'absolute';

  const SelectedCountryIcon = selectedOption?.value
    ? countriesIcons[selectedOption?.value as keyof typeof countriesIcons]
    : null;

  const searchResults = useMemo(() => {
    const availableZones = boltOns.map((b) => b.zone);
    const availableCountries = countries.filter((country) =>
      availableZones.includes(country.zone)
    );

    return query === ''
      ? availableCountries
      : availableCountries.filter((country) =>
          country.label
            .toLowerCase()
            .replace(/\s+/g, '')
            .includes(query.toLowerCase().replace(/\s+/g, ''))
        );
  }, [boltOns, query]);

  const handleCountrySelect = (option: BoltOnCountry) => {
    setSelectedOption(option);
    onSelect(option);
    setIsCountryDropdownOpen(false);
    setQuery('');
  };

  return (
    <div>
      <div className="font-semibold mb-4">Choose roaming country</div>
      <div className="">
        <Tab.Group onChange={setSelectedTab}>
          <Tab.List className="flex relative text-sm bg-[#EBEBEB] rounded-xl w-fit">
            {hasEUBoltOn && <TabTitle>Europe</TabTitle>}
            <TabTitle last>International</TabTitle>
          </Tab.List>

          <Tab.Panels
            className={twMerge(
              'py-2 px-2 bg-white rounded-b-xl text-base rounded-tr-xl'
            )}
          >
            {hasEUBoltOn && (
              <Tab.Panel>
                <EURoaming
                  isNavAbsolute={isNavAbsolute}
                  setClientSecret={setClientSecret}
                  openTerms={() => setIsTermsOpen(true)}
                />
              </Tab.Panel>
            )}
            <Tab.Panel
              className={!hasBoltOns ? 'opacity-30 pointer-events-none' : ''}
            >
              <Combobox value={selectedOption} onChange={handleCountrySelect}>
                <div className="relative mt-1">
                  <div className="relative mb-2 flex gap-4 items-center">
                    {!selectedOption && (
                      <Combobox.Button
                        className="absolute inset-y-0 left-2 flex items-center pl-2"
                        onClick={() => setIsCountryDropdownOpen(true)}
                      >
                        <span className="bg-[#B9B9B9] rounded-full p-1 text-white">
                          <SearchIcon className="w-3.5 h-3.5" />
                        </span>
                      </Combobox.Button>
                    )}
                    <Combobox.Input
                      className={twMerge(
                        'w-full border-none p-3 font-semibold rounded-lg focus:bg-[#F3F3F3] focus:outline-none',
                        !selectedOption && 'bg-[#F3F3F3] pl-12'
                      )}
                      displayValue={(country: BoltOnCountry) => country.label}
                      onChange={(event) => setQuery(event.target.value)}
                      onClick={() => setIsCountryDropdownOpen(true)}
                      placeholder="Type in country name"
                    />
                    {selectedOption && SelectedCountryIcon && (
                      <div className="min-w-6 mr-3">
                        <CountryIcon
                          Icon={SelectedCountryIcon}
                          title={selectedOption.value}
                        />
                      </div>
                    )}
                  </div>
                  <Transition
                    as={Fragment}
                    leave="transition ease-in duration-100"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                    show={isCountryDropdownOpen}
                  >
                    <Combobox.Options className="bg-white -left-2 px-2 absolute max-h-80 w-[calc(100%_+_16px)] overflow-auto divide-y divide-slate-100 rounded-b-xl">
                      {searchResults.length === 0 && query !== '' ? (
                        <div className="relative cursor-default select-none p-4 pt-6 border-t">
                          Nothing found.
                        </div>
                      ) : (
                        searchResults.map((country: BoltOnCountry) => (
                          <CountryOption
                            key={country.value}
                            country={country}
                            Icon={
                              countriesIcons[
                                country.value as keyof typeof countriesIcons
                              ]
                            }
                          />
                        ))
                      )}
                    </Combobox.Options>
                  </Transition>
                </div>
              </Combobox>
            </Tab.Panel>
          </Tab.Panels>
        </Tab.Group>
      </div>
      {selectedTab === 1 && (
        <>
          <Transition
            as={Fragment}
            enter="transition ease-in duration-300 delay-100"
            enterTo="opacity-100 block"
            enterFrom="opacity-0 hidden"
            show={!isCountryDropdownOpen}
          >
            <div className="mt-10">
              <div className="font-semibold mb-4">Boltons:</div>

              {selectedBoltOn && (
                <div className=" bg-white rounded-xl px-5 py-4">
                  <div className="flex justify-between items-center mb-4">
                    <h2 className="text-xl font-semibold">
                      {selectedBoltOn.title}
                    </h2>
                    <span className="text-primary text-lg font-semibold">
                      {currencyRounded(selectedBoltOn.price)}
                    </span>
                  </div>
                  <ul className="mb-2">
                    <li>
                      <span className="font-bold">{selectedBoltOn.data}GB</span>{' '}
                      data
                    </li>
                    <li>
                      <span className="font-bold">{selectedBoltOn.voice}</span>{' '}
                      mins (inbound & outbound)
                    </li>
                    <li>
                      <span className="font-bold">{selectedBoltOn.sms}</span>{' '}
                      texts
                    </li>
                  </ul>
                  <p className="mb-8">
                    Valid for the next{' '}
                    <span className="font-semibold">
                      {getTimeDifferenceFromNow(selectedBoltOn.valid_until)}
                    </span>
                  </p>
                  <p className="text-right">
                    <button
                      className="text-gray-400 text-xs underline"
                      onClick={() => setIsTermsOpen(true)}
                    >
                      Terms apply
                    </button>
                  </p>
                </div>
              )}

              <div className="mt-10 pt-4 pb-10 flex flex-col justify-center">
                <span className="text-center text-sm font-semibold mb-2">
                  Total:{' '}
                  <span className="text-primary ml-3">
                    {currencyRounded(selectedBoltOn?.price)}
                  </span>
                </span>
              </div>
            </div>
          </Transition>
          <ProgressLinks
            hideBackButton
            onNextClick={onNextClick}
            disabledNext={!selectedOption}
            absolute={navPosition === 'absolute'}
          />
        </>
      )}
      <Terms isOpen={isTermsOpen} onClose={() => setIsTermsOpen(false)} />
    </div>
  );
}
