import { useNavigate, useRouter } from '@tanstack/react-router';
import clsx from 'clsx';
import { MouseEventHandler } from 'react';
import { CloseIcon, LeftArrowIcon } from 'src/assets/icons';
import { ROUTES } from 'src/config/routes';
import { twMerge } from 'tailwind-merge';

interface BackButtonProps {
  label?: string;
  to?: string;
  size?: 'small' | 'medium' | 'large';
  onClick?: MouseEventHandler<HTMLButtonElement>;
  iconType?: 'default' | 'cross' | 'back';
  colorMimic?: boolean;
}

const BackButton = ({ label, to, size = 'small', onClick, iconType = 'default', colorMimic }: BackButtonProps) => {
  const navigate = useNavigate();
  const { history } = useRouter();

  const onBack = () => {
    const length = window.history.length;

    if (to) {
      navigate({ to });
      return;
    }

    if (length === 1) {
      navigate({ to: ROUTES.Dashboard });
      return
    }

    history.back();
  };

  const clickHandler = onClick ?? onBack;

  return (
    <button
      type="button"
      onClick={clickHandler}
      className={twMerge(
        'flex gap-4 items-center z-[1]',
        size === 'small' ? 'absolute top-4 md:top-8 left-5' : 'w-12 h-12'
      )}
    >
      <span
        className={clsx(
          'back-button block',
          size,
          colorMimic && 'backdrop-invert-[0.7]'
        )}
      >
        {(iconType === 'default' || iconType === 'back') && (
          <LeftArrowIcon className="w-5 h-5" />
        )}
        {iconType === 'cross' && <CloseIcon className="size-6 -m-0.5" />}
      </span>
      <span className="font-bold text-lg">{label}</span>
    </button>
  );
};

export default BackButton;
