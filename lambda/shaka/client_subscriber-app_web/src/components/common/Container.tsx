import { PropsWithChildren } from 'react';
import { twMerge } from 'tailwind-merge';

interface Props {
  className?: string;
  removeBottomPadding?: boolean;
}

const Container = ({
  children,
  className,
  removeBottomPadding
}: PropsWithChildren<Props>) => {
  return (
    <div
      className={twMerge(
        'container mx-auto w-full max-w-md pt-6 px-5 md:pt-10 pb-24 min-h-[100dvh] relative',
        removeBottomPadding && 'pb-0',
        className
      )}
    >
      {children}
    </div>
  );
};

export default Container;
