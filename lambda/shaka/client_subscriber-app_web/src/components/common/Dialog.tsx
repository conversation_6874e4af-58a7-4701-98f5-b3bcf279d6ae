import { MouseE<PERSON>, PropsWithChildren, ReactNode, useState } from 'react';
import { Dialog as DialogHeadless, Transition } from '@headlessui/react';
import CloseButton from './CloseButton';
import { twMerge } from 'tailwind-merge';
import Loader from './Loader';
import { createPortal } from 'react-dom';

export type ButtonActionColor = 'default' | 'success' | 'danger';

interface Props {
  title: string | ReactNode;
  isOpen: boolean;
  onClose: () => void;
  rightButton?: ReactNode;
  leftButton?: ReactNode;
  panelStyles?: string;
  hideCloseButton?: boolean;
}

const Dialog = ({
  isOpen,
  onClose,
  leftButton,
  rightButton,
  title,
  children,
  panelStyles,
  hideCloseButton
}: PropsWithChildren<Props>) => {
  return createPortal(
    <Transition show={isOpen}>
      <DialogHeadless
        as="div"
        open={isOpen}
        onClose={onClose}
        className="relative z-50"
      >
        <Transition.Child
          enter="ease-in-out duration-100"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in-out duration-100"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="transition-all fixed inset-0 bg-black/50" />
        </Transition.Child>
        <Transition.Child
          enter="ease-in duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-out duration-300"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 -inset-x-1 flex w-screen items-center justify-center p-4">
            <DialogHeadless.Panel
              className={twMerge(
                'relative overflow-hidden rounded-3xl bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg px-4 p-6',
                panelStyles
              )}
            >
              <div className="relative mb-4">
                <DialogHeadless.Title className="text-base text-center font-semibold leading-6 text-gray-900 uppercase">
                  {title}
                  {!hideCloseButton && (
                    <div className="absolute -right-1 -top-2">
                      <CloseButton onClose={onClose} />
                    </div>
                  )}
                </DialogHeadless.Title>
              </div>
              <div className="text-sm text-gray-500">{children}</div>

              <div className="flex gap-4 justify-end mt-6">
                {leftButton}
                {rightButton}
              </div>
            </DialogHeadless.Panel>
          </div>
        </Transition.Child>
      </DialogHeadless>
    </Transition>,
    document.body
  );
};

export default Dialog;

export const DialogActionButton = ({
  children,
  className,
  actionColor = 'success',
  onClick,
  ...props
}: React.ButtonHTMLAttributes<HTMLButtonElement> & {
  actionColor?: ButtonActionColor;
}) => {
  const [isClicked, setIsClicked] = useState(false);

  const handleClick = (e: MouseEvent<HTMLButtonElement>) => {
    setIsClicked(true);
    onClick?.(e);
  };

  return (
    <button
      className={twMerge(
        'bg-gray-100 rounded-xl text-center py-3 px-5 flex flex-col items-center w-[4.5rem] hover:bg-gray-200 transition-colors duration-200',
        actionColor === 'success' && 'text-green-500',
        actionColor === 'danger' && 'text-red-500',
        className
      )}
      onClick={handleClick}
      {...props}
    >
      <span className="inline-block font-semibold mt-0.5">
        {isClicked ? <Loader /> : children}
      </span>
    </button>
  );
};
