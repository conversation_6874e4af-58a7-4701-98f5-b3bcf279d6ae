import { useState } from "react";

const LogoCircle = () => {
  const [isError, setIsError] = useState(false);

  if (isError)
    return (
      <div className="size-full object-cover rounded-full border border-gray-300" />
    );

  return (
    <img
      src="/logo-circle.png"
      alt="logo"
      className="size-full object-cover rounded-full logo-circle"
      onError={() => setIsError(true)}
    />
  );
};

export default LogoCircle;
