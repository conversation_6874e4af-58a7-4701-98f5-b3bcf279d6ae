import { monthsShort } from "src/config/dates";
import { Input } from "./types";

export const yearsOptions = [
  ...Array.from({ length: 100 }, (_, i) => ({
    data: new Date().getFullYear() - i,
  })),
];

export const monthsOptions = Array.from({ length: 12 }, (_, i) => ({
  data: i + 1,
  label: monthsShort[i],
}));

export const daysOptions = Array.from({ length: 31 }, (_, i) => ({
  data: i + 1,
}));

export const getDefaultValue = (
  value: string | undefined,
  options: Input[]
) => {
  if (!value) {
    return undefined;
  }

  return options.find((option) => Number(option?.data) === Number(value));
};
