import { useState } from 'react';
import DatePicker, { DatePickerProps } from 'react-date-picker';
import {
  FieldVal<PERSON>,
  useC<PERSON>roller,
  UseControllerProps
} from 'react-hook-form';
import InputBlock from '../InputBlock';
import 'react-date-picker/dist/DatePicker.css';
import './styles.css';
import { CalendarIcon } from 'src/assets/icons';

type Props<T extends FieldValues> = {
  label?: string;
  error?: string;
  placeholder?: string;
  onChange?: (date: DatePickerProps['value']) => void;
} & DatePickerProps &
  UseControllerProps<T>;

const CalendarInput = <T extends FieldValues>({
  label,
  error,
  required,
  placeholder,
  control,
  name,
  onChange,
  ...props
}: Props<T>) => {
  const {
    field: { onChange: onFieldChange, ref, ...fieldProps }
  } = useController({ control, name });

  const [isPlaceholder, setIsPlaceholder] = useState(Boolean(placeholder));

  const handleFieldChange = (date: DatePickerProps['value']) => {
    onFieldChange(date);
    if (onChange) {
      onChange(date);
    }
  };

  return (
    <InputBlock name={name} label={label} error={error} required={required}>
      <div className="relative">
        {isPlaceholder && (
          <span className="input calendar-placeholder">{placeholder}</span>
        )}
        <DatePicker
          inputRef={ref}
          className="input"
          calendarClassName="calendar"
          showLeadingZeros
          clearIcon={null}
          calendarIcon={CalendarIcon}
          dayPlaceholder="dd"
          monthPlaceholder="mm"
          yearPlaceholder="yyyy"
          onChange={handleFieldChange}
          onFocus={() => setIsPlaceholder(false)}
          {...fieldProps}
          {...props}
        />
      </div>
    </InputBlock>
  );
};

export default CalendarInput;
