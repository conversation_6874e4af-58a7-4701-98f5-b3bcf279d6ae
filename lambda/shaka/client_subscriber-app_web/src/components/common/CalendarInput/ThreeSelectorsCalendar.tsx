import { Fragment, useState } from "react";
import { Listbox, Transition } from "@headlessui/react";
import InputBlock, { InputBlockProps } from "../InputBlock";
import clsx from "clsx";
import { FieldName, Input } from "./types";
import {
  daysOptions,
  getDefaultValue,
  monthsOptions,
  yearsOptions,
} from "./helpers";
import { ChevronDownIcon } from "src/assets/icons/ChevronDown";

interface Props extends InputBlockProps {
  value?: string;
  onChange?: (value: string) => void;
}

export default function ThreeSelectorsCalendar({
  value,
  onChange,
  ...blockProps
}: Props) {
  const [defaultYear, defaultMonth, defaultDay] = value?.split("-") || [];

  const [focusedInput, setFocusedInput] = useState<FieldName | null>(null);

  const [year, setYear] = useState<Input>(
    getDefaultValue(defaultYear, yearsOptions)
  );
  const [month, setMonth] = useState<Input>(
    getDefaultValue(defaultMonth, monthsOptions)
  );
  const [day, setDay] = useState<Input>(
    getDefaultValue(defaultDay, daysOptions)
  );

  type Field = {
    name: FieldName;
    fieldValue: Input;
    setValue: React.Dispatch<React.SetStateAction<Input>>;
    options: Input[];
    defaultValue: string;
  }[];

  const fields: Field = [
    {
      name: "day",
      fieldValue: day,
      setValue: setDay,
      options: daysOptions,
      defaultValue: "Day",
    },
    {
      name: "month",
      fieldValue: month,
      setValue: setMonth,
      options: monthsOptions,
      defaultValue: "Month",
    },
    {
      name: "year",
      fieldValue: year,
      setValue: setYear,
      options: yearsOptions,
      defaultValue: "Year",
    },
  ];

  const handleFieldsChange = (name: FieldName) => (value: Input) => {
    setFocusedInput(null);
    const newValue = value?.data || "";
    let currentYear = year?.data || "";
    let currentMonth = month?.data || "";
    let currentDay = day?.data || "";

    switch (name) {
      case "day":
        setDay(value);
        currentDay = newValue;
        break;
      case "month":
        setMonth(value);
        currentMonth = newValue;
        break;
      case "year":
        setYear(value);
        currentYear = newValue;
        break;
      default:
        break;
    }

    const formattedDate = [currentYear, currentMonth, currentDay]
      .filter(Boolean)
      .map((v) => v.toString().padStart(2, "0"))
      .join("-");

    onChange?.(formattedDate);
  };

  const handleFocusChange = (name: FieldName) => () => {
    setFocusedInput(name);
  };

  return (
    <InputBlock {...blockProps}>
      <div className="grid grid-cols-7 gap-2">
        {fields.map(({ fieldValue, options, defaultValue, name }) => (
          <Listbox
            value={fieldValue}
            onChange={handleFieldsChange(name)}
            key={name}
          >
            <div
              className={clsx(
                "relative",
                name === "year" ? "col-span-3" : "col-span-2"
              )}
            >
              <Listbox.Button
                className={clsx(
                  "input select text-left",
                  focusedInput === name && "focused"
                )}
                onClick={handleFocusChange(name)}
              >
                {fieldValue ? (
                  <span className="block truncate">
                    {fieldValue.label || fieldValue.data}
                  </span>
                ) : (
                  <span className="block truncate text-gray-400 font-normal">
                    {defaultValue}
                  </span>
                )}

                <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                  <ChevronDownIcon className="size-3" />
                </span>
              </Listbox.Button>
              <Transition
                as={Fragment}
                leave="transition ease-in duration-100"
                leaveFrom="opacity-100"
                leaveTo="opacity-0"
              >
                <Listbox.Options className="absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-sm z-10">
                  {options.map((option) => (
                    <Listbox.Option
                      key={option?.data}
                      className={({ active }) =>
                        `option relative cursor-default select-none py-2 px-3 ${
                          active ? "selected" : "text-gray-900"
                        }`
                      }
                      value={option}
                    >
                      {({ selected }) => (
                        <>
                          <span
                            className={`block truncate ${
                              selected ? "font-medium" : "font-normal"
                            }`}
                          >
                            {option?.label || option?.data}
                          </span>
                          {selected ? (
                            <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-amber-600"></span>
                          ) : null}
                        </>
                      )}
                    </Listbox.Option>
                  ))}
                </Listbox.Options>
              </Transition>
            </div>
          </Listbox>
        ))}
      </div>
    </InputBlock>
  );
}
