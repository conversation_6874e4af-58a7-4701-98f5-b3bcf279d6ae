import { RadioGroup } from "@headlessui/react";
import clsx from "clsx";

type Props = {
  options: { label: string; value: string }[];
  value: string;
  onChange: (v: string) => void;
};

export default function RadioButtonGroup({ options, value, onChange }: Props) {
  return (
    <RadioGroup
      value={value}
      onChange={onChange}
      className="flex bg-white rounded-full"
    >
      {options.map((option) => (
        <RadioGroup.Option value={option.value}>
          {({ checked }: { checked: boolean }) => (
            <div
              className={clsx(
                "w-full text-xs font-medium leading-5 focus:outline-none  transition-[background-color]",
                "rounded-full px-6 py-1 cursor-pointer",

                checked
                  ? "bg-gray-800 text-white"
                  : "text-gray-600 hover:text-black"
              )}
            >
              {option.label}
            </div>
          )}
        </RadioGroup.Option>
      ))}
    </RadioGroup>
  );
}
