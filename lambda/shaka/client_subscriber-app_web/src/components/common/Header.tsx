import { Link, useRouterState } from '@tanstack/react-router';
import { ROUTES } from 'src/config/routes';
import LogoCircle from './LogoCircle';
import BackButton from './BackButton';

const Header = () => {
  const { location } = useRouterState();
  const pathname = location.pathname;
  const isDashboard = pathname === ROUTES.Dashboard;
  const isCheckoutRedirect = pathname.includes('card-details/return');
  // potential solution to stuborn bug where the back button showed after closing new subscription drawer
  // can break other things though
  const isSettings =
    pathname === ROUTES.Settings || pathname === ROUTES.Settings + '/';
  const isPerks = pathname === ROUTES.Perks;

  if (isDashboard || isCheckoutRedirect) {
    return null;
  }

  if (isSettings || isPerks) {
    return (
      <div className="absolute top-4 md:top-8 left-5 w-10 h-10 circle-logo small">
        <Link to={ROUTES.Dashboard}>
          <LogoCircle />
        </Link>
      </div>
    );
  }

  return <BackButton />;
};

export default Header;
