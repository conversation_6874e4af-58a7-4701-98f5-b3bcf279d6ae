import clsx from 'clsx';
import { PropsWithChildren } from 'react';

type ChipSize = 'small' | 'medium' | 'large';

interface Props {
  size?: ChipSize;
}

const Chip = ({ children, size = 'small' }: PropsWithChildren<Props>) => {
  return (
    <span
      className={clsx(
        'chip',
        size === 'small' && 'text-[10px] px-3',
        size === 'medium' && 'text-xs px-4 py-1',
        size === 'large' && 'text-sm px-5 py-2'
      )}
    >
      {children}
    </span>
  );
};

export default Chip;
