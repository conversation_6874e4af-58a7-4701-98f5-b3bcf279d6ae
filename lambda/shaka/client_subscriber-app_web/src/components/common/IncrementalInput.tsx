import { useState } from 'react';
import { twMerge } from 'tailwind-merge';
const buttonStyle = 'bg-general rounded-full size-[34px] font-semibold';

export default function IncrementalInput({
  defaultValue = 0,
  extension,
  extensionSingular,
  onChange,
  min,
  max
}: {
  defaultValue: number;
  extension?: string;
  extensionSingular?: string;
  onChange?: (value: number) => void;
  min?: number;
  max?: number;
}) {
  const [currentValue, setCurrentValue] = useState(defaultValue);
  const changeValue = (newValue: number) => () => {
    if ((min && newValue < min) || (max && newValue > max)) return;

    setCurrentValue(newValue);
    onChange && onChange(newValue);
  };

  const disabledMin = currentValue === min;
  const disabledMax = currentValue === max;

  return (
    <div className="flex justify-between items-center">
      <button
        tabIndex={disabledMin ? -1 : 0}
        className={twMerge(
          buttonStyle,
          currentValue === min && 'opacity-90 text-gray-300 pointer-events-none'
        )}
        onClick={changeValue(currentValue - 1)}
      >
        -
      </button>
      <div className="mx-4 w-28 text-center">
        <div className="bg-black text-white px-3 py-3 rounded-xl font-semibold text-xl">
          {currentValue} {currentValue === 1 ? extensionSingular : extension}
        </div>
      </div>
      <button
        tabIndex={disabledMax ? -1 : 0}
        className={twMerge(
          buttonStyle,
          currentValue === max && 'opacity-90 text-gray-300 pointer-events-none'
        )}
        onClick={changeValue(currentValue + 1)}
      >
        +
      </button>
    </div>
  );
}
