import React, { LegacyRef, useState } from 'react';
import InputBlock, { InputBlockProps } from './InputBlock';
import { EyeIcon } from 'src/assets/icons/Eye';
import { EyeCrossedIcon } from 'src/assets/icons/EyeCrossed';
import clsx from 'clsx';
import { twMerge } from 'tailwind-merge';

type Props = InputBlockProps &
  React.InputHTMLAttributes<HTMLInputElement> & {
    leftIcon?: React.ReactNode;
    narrow?: boolean;
  };

const Input = React.forwardRef(function Input(
  {
    label,
    labelAction,
    error,
    required,
    description,
    type = 'text',
    withValidationMark,
    dirty,
    hideErrorMessage,
    leftIcon,
    narrow,
    ...props
  }: Props,
  ref: LegacyRef<HTMLInputElement>
) {
  const [inputType, setInputType] = useState(type);
  const [showDescription, setShowDescription] = useState(false);

  const handleIconClick = () => {
    setInputType(inputType === 'password' ? 'text' : 'password');
  };

  const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    if (props.onFocus) {
      props.onFocus(e);
    }
    setShowDescription(true);
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    if (props.onBlur) {
      props.onBlur(e);
    }
    setShowDescription(false);
  };

  return (
    <InputBlock
      name={props.name}
      label={label}
      error={error}
      labelAction={labelAction}
      required={required}
      description={showDescription && description}
      dirty={dirty}
      withValidationMark={withValidationMark}
      hideErrorMessage={hideErrorMessage}
    >
      <div className="relative">
        {leftIcon && (
          <span
            className={twMerge(
              'absolute top-3.5 left-3 text-black/30',
              narrow && 'top-2'
            )}
          >
            {leftIcon}
          </span>
        )}
        <input
          className={clsx(
            'input',
            type === 'password' && 'icon-right',
            leftIcon && 'icon-left',
            narrow && 'narrow'
          )}
          ref={ref}
          type={inputType}
          {...props}
          onFocus={handleFocus}
          onBlur={handleBlur}
        />
        {type === 'password' && (
          <span
            className="absolute top-3.5 right-3 text-black/30 cursor-pointer hover:text-black/50"
            onClick={handleIconClick}
          >
            {inputType === 'password' ? (
              <EyeCrossedIcon className="size-5" />
            ) : (
              <EyeIcon className="size-5" />
            )}
          </span>
        )}
      </div>
    </InputBlock>
  );
});

export default Input;
