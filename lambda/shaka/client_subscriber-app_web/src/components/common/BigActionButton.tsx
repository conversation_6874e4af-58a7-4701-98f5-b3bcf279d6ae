import clsx from 'clsx';
import { CloseIcon } from 'src/assets/icons';
import { CheckIcon } from 'src/assets/icons/Check';
import { twMerge } from 'tailwind-merge';
import React from 'react';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  actionType: 'submit' | 'cancel';
  submitText?: string;
  cancelText?: string;
  width?: string;
}

export default function BigActionButton({
  actionType,
  submitText = 'Yes',
  cancelText = 'No',
  width,
  disabled,
  ...props
}: ButtonProps) {
  const isSubmit = actionType === 'submit';
  const buttonWidth = width || '4.5rem';
  return (
    <button
      {...props}
      className={twMerge(
        'bg-white rounded-2xl text-center py-3 px-5 flex flex-col items-center justify-center hover:bg-white/50 transition-all duration-200',
        disabled
          ? 'cursor-not-allowed opacity-30 pointer-events-none'
          : 'cursor-pointer',
        width ? `w-[${buttonWidth}]` : '',
      )}
    >
      <span className={clsx(isSubmit ? 'text-green-500' : 'text-red-500')}>
        {isSubmit ? <CheckIcon className="size-4 my-1" /> : <CloseIcon />}
      </span>
      <span className="inline-block text-xs font-semibold mt-0.5">
        {isSubmit ? submitText : cancelText}
      </span>
    </button>
  );
}
