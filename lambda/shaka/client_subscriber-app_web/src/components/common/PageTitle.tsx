import clsx from "clsx";
import { PropsWithChildren } from "react";

interface Props {
  uppercase?: boolean;
  withBottomMargin?: boolean;
}

const PageTitle = ({
  children,
  withBottomMargin = true,
}: PropsWithChildren<Props>) => {
  return (
    <h2
      className={clsx(
        "mt-[1px] font-semibold m-4 text-center uppercase",
        withBottomMargin ? "mb-16" : "mb-5"
      )}
    >
      {children}
    </h2>
  );
};

export default PageTitle;
