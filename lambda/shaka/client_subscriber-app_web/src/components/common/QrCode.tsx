import { SpinnerIcon } from 'src/assets/icons/Spinner';
import { Corner } from 'src/assets/images/Corner';

export default function QrCode({ qr_code }: { qr_code: string }) {
  return (
    <div className="px-8">
      <div className="qr-wrapper relative">
        <Corner className="absolute -top-2 -left-2 " />
        <Corner className="absolute -top-2 -right-2 rotate-90" />
        <Corner className="absolute -bottom-2 -right-2 rotate-180" />
        <Corner className="absolute -bottom-2 -left-2 -rotate-90" />

        {qr_code ? (
          <img
            src={qr_code}
            className="w-full h-full object-contain object-center"
          />
        ) : (
          <div className="w-full h-full bg-gray-200 flex items-center justify-center">
            <SpinnerIcon />
          </div>
        )}
      </div>
    </div>
  );
}
