import { RightArrowIcon } from 'src/assets/icons';

function NextArrow(props: { className: string; onClick: () => void }) {
  const { className, onClick } = props;
  return (
    <div
      className={className}
      style={{
        display: 'block',
        background: 'white',
        borderRadius: '50%',
        color: 'black'
      }}
      onClick={onClick}
    >
      <RightArrowIcon className="absolute top-1 left-1 size-3" />
    </div>
  );
}

function PrevArrow(props: { className: string; onClick: () => void }) {
  const { className, onClick } = props;
  return (
    <div
      className={className}
      style={{
        display: 'block',
        background: 'white',
        borderRadius: '50%',
        color: 'black'
      }}
      onClick={onClick}
    >
      <RightArrowIcon className="absolute top-1 left-1 size-3 rotate-180" />
    </div>
  );
}

export { NextArrow, PrevArrow };
