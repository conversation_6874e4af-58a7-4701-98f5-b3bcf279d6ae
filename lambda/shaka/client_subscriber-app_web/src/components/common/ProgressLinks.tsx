import { useNavigate } from '@tanstack/react-router';
import BackButton from './BackButton';
import Button from './Button';
import { RightArrowIcon } from 'src/assets/icons';
import clsx from 'clsx';
import { MouseEventHandler } from 'react';

export interface ProgressLinksProps {
  hideBackButton?: boolean;
  hideNextButton?: boolean;
  disabledNext?: boolean;
  backTo?: string;
  nextTo?: string;
  absolute?: boolean;
  onNextClick?: () => void;
  isLoading?: boolean;
  onBackClick?: MouseEventHandler<HTMLButtonElement>;
  isModalView?: boolean;
}

const ProgressLinks = ({
  hideBackButton,
  hideNextButton,
  disabledNext,
  backTo,
  nextTo,
  absolute,
  onNextClick,
  isLoading,
  onBackClick,
  isModalView
}: ProgressLinksProps) => {
  const navigate = useNavigate();

  const handleNextCLick: MouseEventHandler<HTMLButtonElement> = () => {
    if (onNextClick) {
      onNextClick();
    }

    if (nextTo) {
      navigate({ to: nextTo });
    }
  };

  return (
    <>
      {!hideBackButton && (
        <div
          className={clsx(
            'bottom-0 left-0',
            isModalView
              ? 'fixed md:left-[calc(50%_-_250px)] p-6 pl-5 sm:bottom-10'
              : absolute
                ? 'absolute'
                : 'fixed md:left-max-nav p-6 pl-5'
          )}
        >
          <BackButton size="medium" to={backTo} onClick={onBackClick} />
        </div>
      )}
      {!hideNextButton && (
        <div
          className={clsx(
            'bottom-0 right-0',
            isModalView
              ? 'fixed sm:right-[calc(50%_-_250px)] p-6 pr-5 sm:bottom-10'
              : absolute
                ? 'absolute'
                : 'fixed sm:right-max-nav p-6 pr-5'
          )}
        >
          <Button
            type="submit"
            color="secondary"
            rightIcon={<RightArrowIcon />}
            disabled={disabledNext}
            onClick={handleNextCLick}
            narrow
            fullWidth
            transparentBg
            isLoading={isLoading}
          >
            Next
          </Button>
        </div>
      )}
    </>
  );
};

export default ProgressLinks;
