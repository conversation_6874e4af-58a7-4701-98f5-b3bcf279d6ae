import clsx from "clsx";
import { CardIcon } from "src/assets/icons/Card";

const paymentMethodsStyle = "rounded-xl bg-white p-5 w-full flex items-center";

export default function PaymentMethods() {
  return (
    <div className="flex gap-3">
      <div
        className={clsx(
          paymentMethodsStyle,
          "justify-between gap-1 border-2 border-black"
        )}
      >
        <CardIcon className="size-6" />{" "}
        <span className="text-base font-semibold">Card</span>
      </div>
      <div className={paymentMethodsStyle}>
        <img src="/paypal.png" alt="paypal" />
      </div>
      <div className={paymentMethodsStyle}>
        <img src="/apple-pay.png" alt="apple-pay" className="w-[80%] m-auto" />
      </div>
    </div>
  );
}
