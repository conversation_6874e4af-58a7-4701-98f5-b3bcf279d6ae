import { twMerge } from 'tailwind-merge';

export default function CountryIcon({
  Icon,
  title,
  size = 6
}: {
  Icon: React.ElementType;
  title: string;
  size?: 3 | 4 | 5 | 6;
}) {
  return (
    <span
      className={twMerge(
        'grid place-items-center w-6 h-6 overflow-hidden rounded-full mask-triangle',
        size && `w-${size} h-${size}`
      )}
    >
      <Icon title={title} className="h-full" />
    </span>
  );
}
