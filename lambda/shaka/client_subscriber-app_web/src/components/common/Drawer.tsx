import { Transition, Dialog } from '@headlessui/react';
import { createPortal } from 'react-dom';
import CloseButton from './CloseButton';
import { twMerge } from 'tailwind-merge';

type DrawerProps = {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  adaptiveHeight?: boolean;
  minHeight?: string;
};

export default function Drawer({
  isOpen,
  onClose,
  title,
  children,
  adaptiveHeight,
  minHeight
}: DrawerProps) {
  return (
    <>
      {createPortal(
        <Transition show={isOpen}>
          <Dialog className="relative z-10" onClose={onClose}>
            <Transition.Child
              enter="ease-in-out duration-100"
              enterFrom="opacity-0"
              enterTo="opacity-100"
              leave="ease-in-out duration-100"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <div className="transition-all fixed inset-0 bg-gray-300/75 backdrop-blur-sm" />
            </Transition.Child>

            <Dialog.Panel
              className={twMerge(
                'fixed inset-0 top-10',
                adaptiveHeight && 'flex items-end'
              )}
            >
              <Transition.Child
                enter="transform transition ease-in-out duration-500 sm:duration-700"
                enterFrom="translate-y-full"
                enterTo="translate-y-0"
                leave="transform transition ease-in-out duration-500 sm:duration-700"
                leaveFrom="translate-y-0"
                leaveTo="translate-y-full"
                className={twMerge(
                  'pointer-events-auto relative h-full bg-[#F3F3F3] pt-6 shadow-xl rounded-t-3xl',
                  adaptiveHeight
                    ? 'min-h-[50%] max-h-[calc(100dvh_-_40px)] h-auto w-full'
                    : 'h-full'
                )}
                style={{
                  minHeight: adaptiveHeight
                    ? minHeight
                      ? minHeight
                      : '50%'
                    : '100%'
                }}
              >
                <div className="relative">
                  {title && (
                    <Dialog.Title className="text-center text-base font-semibold leading-6 text-gray-900 uppercase">
                      {title}
                    </Dialog.Title>
                  )}

                  <div className="absolute top-1/2 -translate-y-1/2 right-0 mr-4">
                    <CloseButton onClose={onClose} />
                  </div>
                </div>
                {children}
              </Transition.Child>
            </Dialog.Panel>
          </Dialog>
        </Transition>,
        document.body
      )}
    </>
  );
}
