import React, { PropsWithChildren } from 'react';
import Loader from './Loader';
import { RightArrowIcon } from 'src/assets/icons';
import { twMerge } from 'tailwind-merge';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  color?: 'primary' | 'secondary' | 'danger' | 'black' | 'gray' | 'white';
  isLoading?: boolean;
  rightIcon?: React.ReactNode;
  fullWidth?: boolean;
  narrow?: boolean;
  transparentBg?: boolean;
  squared?: boolean;
  size?: 'small' | 'medium' | 'large';
  fontSize?: 'small' | 'medium' | 'large' | 'default';
  withArrow?: boolean;
  withBorder?: boolean;
}

const disabledStyles = 'pointer-events-none cursor-not-allowed opacity-25';

const Button = ({
  children,
  disabled,
  isLoading,
  color = 'primary',
  rightIcon,
  fullWidth,
  narrow,
  transparentBg,
  size = 'large',
  fontSize = 'default',
  withArrow,
  squared,
  withBorder,
  ...props
}: PropsWithChildren<ButtonProps>) => {
  return (
    <button
      className={twMerge(
        'button relative md:py-4',
        narrow && 'narrow',
        color,
        disabled && disabledStyles,
        squared && 'squared',
        transparentBg && 'bg-transparent',
        size,
        fontSize === 'small' && 'text-xs',
        fullWidth && 'full-width',
        withBorder && 'border-2 border-black'
      )}
      tabIndex={disabled ? -1 : undefined}
      {...props}
    >
      {isLoading ? <Loader /> : children}
      {rightIcon && (
        <span className={twMerge('ml-2', size === 'small' && 'ml-4')}>
          {rightIcon}
        </span>
      )}
      {withArrow && (
        <RightArrowIcon
          className={twMerge('ml-2 size-5', size === 'small' && 'size-4 ml-4')}
        />
      )}
    </button>
  );
};

export default Button;
