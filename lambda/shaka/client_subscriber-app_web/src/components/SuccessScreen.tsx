import { CheckRoundedFilledIcon } from "src/assets/icons/CheckRoundedFilled";
import Link from "./common/Link";
import { ROUTES } from "src/config/routes";
import { ReactNode } from "react";

interface Props {
  title?: string;
  description?: ReactNode;
}

export default function SuccessScreen({ description, title }: Props) {
  return (
    <>
      <div className="flex flex-col items-center justify-evenly gap-8">
        <div className="validation valid">
          <CheckRoundedFilledIcon className="size-12" />
        </div>
        <p className="text-sm font-bold">{title}</p>
      </div>
      <div className="mx-7 mt-16 mb-28">
        <div className="space-y-4 text-sm">{description}</div>
      </div>
      <div className="w-full">
        <Link
          to={ROUTES.Dashboard}
          color="secondary"
          styleType="button"
          replace
        >
          Go to dashboard
        </Link>
      </div>
    </>
  );
}
