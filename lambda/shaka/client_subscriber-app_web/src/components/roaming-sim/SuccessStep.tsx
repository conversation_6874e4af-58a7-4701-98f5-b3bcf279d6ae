import { CheckRoundedFilledIcon } from 'src/assets/icons/CheckRoundedFilled';
import Link from '../common/Link';
import useSubscription from 'src/hooks/useSubscription';
import { ROUTES } from 'src/config/routes';
import { getCountryLabel } from 'src/helpers';

export default function SuccessStep() {
  const { subscriber } = useSubscription();
  const roamingEsims = subscriber?.roaming_esims;

  return (
    <div className="px-5 h-[70vh] flex flex-col justify-center gap-10 items-center">
      <div className="validation valid">
        <CheckRoundedFilledIcon className="size-12" />
      </div>
      <h3 className="font-semibold text-center">And you are all done!</h3>
      <div className="space-y-4 mb-10 text-sm text-center">
        Your travel eSIM is already installed. Just make sure you enable it on
        your phone once you reach{' '}
        {roamingEsims &&
          getCountryLabel(roamingEsims[roamingEsims.length - 1]?.zone)}
        .
      </div>
      <div className="mx-3">
        <Link
          to={ROUTES.Dashboard}
          color="secondary"
          styleType="button"
          replace
        >
          Go to dashboard
        </Link>
      </div>
    </div>
  );
}
