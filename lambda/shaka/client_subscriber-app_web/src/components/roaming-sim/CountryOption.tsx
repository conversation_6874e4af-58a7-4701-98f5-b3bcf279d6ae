import { Combobox } from '@headlessui/react';
import CountryIcon from 'src/components/common/CountryIcon';
import { twMerge } from 'tailwind-merge';

export default function CountryOption({
  country,
  Icon
}: {
  country: { value: string; label: string };
  Icon?: React.ElementType;
}) {
  return (
    <Combobox.Option
      key={country.value}
      className="cursor-pointer"
      value={country}
    >
      {({ selected, active }) => (
        <div
          className={twMerge(
            'py-4 px-3 rounded-lg font-semibold text-black flex justify-between items-center',
            active && 'bg-[#F3F3F3]',
            !selected && 'hover:bg-[#FAFAFA]'
          )}
        >
          {country.label}

          {Icon && <CountryIcon Icon={Icon} title={country.label} />}
        </div>
      )}
    </Combobox.Option>
  );
}
