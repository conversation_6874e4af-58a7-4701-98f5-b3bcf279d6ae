import { useContext } from 'react';

import {
  EmbeddedCheckout,
  EmbeddedCheckoutProvider
} from '@stripe/react-stripe-js';
import { StripeContext } from 'src/context/StripeContext';
import FullPageLoader from '../common/FullPageLoader';

export default function RoamingSimPayment({
  clientSecret
}: {
  onReturn: () => void;
  clientSecret: string;
}) {
  const { stripePromise } = useContext(StripeContext);

  if (!clientSecret || !stripePromise) {
    return <FullPageLoader />;
  }

  return (
    <EmbeddedCheckoutProvider
      stripe={stripePromise}
      options={{
        clientSecret
      }}
    >
      <EmbeddedCheckout />
    </EmbeddedCheckoutProvider>
  );
}
