/* eslint-disable @typescript-eslint/no-unused-vars */
import * as countriesIcons from 'country-flag-icons/react/3x2';
import { twMerge } from 'tailwind-merge';
import CountryIcon from '../common/CountryIcon';
import { currencyRounded } from 'src/helpers';
import { RoamingSimOffer } from 'src/types/roaming-sim';

export default function OfferOption({
  selected,
  offer,
  onClick,
  selectedCountry
}: {
  selected: boolean;
  offer: RoamingSimOffer;
  onClick: () => void;
  selectedCountry?: {
    value: string;
    label: string;
  };
}) {
  const isUnlimited = offer.data.toLowerCase() === 'unlimited';
  const SelectedCountryIcon =
    countriesIcons[selectedCountry?.value as keyof typeof countriesIcons] ||
    null;

  return (
    <div
      className={twMerge(
        'bg-white rounded-xl px-6 py-4 border border-transparent  cursor-pointer',
        selected ? ' border-black' : 'hover:border-[#E0E0E0]'
      )}
      onClick={onClick}
    >
      <div className="flex justify-between mb-4">
        <h2 className="text-xl font-semibold">
          {isUnlimited ? 'Unlimited' : offer.data.replace('.00', '') + 'GB'}
        </h2>
        <div className="flex gap-2 items-center">
          <span className="text-sm font-semibold text-[#BABABA]">
            {selectedCountry?.label}
          </span>
          {SelectedCountryIcon && (
            <CountryIcon
              Icon={SelectedCountryIcon}
              title={selectedCountry?.label || ''}
              size={4}
            />
          )}
        </div>
      </div>
      <div className="flex justify-between text-sm">
        <span>
          {offer.days} {offer.days === 1 ? 'day' : 'days'}
        </span>
        <span className="text-primary text-base font-semibold">
          {currencyRounded(Number(offer.price))}
        </span>
      </div>
    </div>
  );
}
