import { getExpiresIn } from 'src/helpers/date';
import { RoamingSim } from 'src/types/roaming-sim';
import { twMerge } from 'tailwind-merge';
import * as countriesIcons from 'country-flag-icons/react/3x2';
import { getCountryLabel } from 'src/helpers';
import Button from './common/Button';
import { Link } from '@tanstack/react-router';
import { ROUTES } from 'src/config/routes';
import { setRoamingZone } from 'src/config/localStorageActions';

const roamingSimStatus = {
  active: 'Active plan',
  inactive: 'No active plan',
  expired: 'Plan expired'
};

export default function TravelSimCard({
  status,
  zone,
  expires,
  data,
  plain,
  is_unlimited
}: RoamingSim & { plain?: boolean }) {
  const { used, left } = data || { used: 0, left: 0 };
  const totalData = used + left;
  const percentageUsed = (used / totalData) * 100;
  const zoneLabel = getCountryLabel(zone) || zone;
  const expired = status === 'expired';

  const SelectedCountryIcon = zone
    ? countriesIcons[zone as keyof typeof countriesIcons]
    : null;

  return (
    <div className="plan-card-wrapper">
      <div
        className={twMerge(
          'absolute inset-0 bg-white overflow-hidden flex flex-col size-full text-base py-5 px-6',
          plain ? 'plan-card-detailed' : 'plan-card'
        )}
      >
        <div className="absolute top-0 right-0 w-28 h-28 [clip-path:polygon(100%_0,0%_0,100%_100%)] grid place-items-center mask-triangle">
          {SelectedCountryIcon && (
            <>
              <SelectedCountryIcon className="h-full" />
              <div className="absolute top-0 right-0 w-full h-full bg-gradient-to-tr from-black/70 to-transparent to-65% z-10" />
            </>
          )}
        </div>

        <div className="flex items-center gap-2 mb-2">
          <span className="font-semibold text-lg text-black/10">eSIM</span>
          {plain && (
            <span className="flex items-center gap-1.5 text-[#CCC] text-xs">
              <span
                className={twMerge(
                  'inline-block  rounded-full w-1.5 h-1.5',
                  status === 'active' ? 'bg-green-500' : 'bg-red-500'
                )}
              />
              {roamingSimStatus[status]}
            </span>
          )}
        </div>

        <div
          className={twMerge('grow', !plain && 'flex flex-col justify-center')}
        >
          <h2 className="text-2xl font-semibold text-black">{zoneLabel}</h2>

          <p className="text-base text-black">
            {is_unlimited ? 'Unlimited' : `${totalData.toFixed(1)}GB`}
          </p>
          <div className="flex justify-between items-center mt-2">
            {!plain && (
              <p>
                <span className="flex items-center gap-1.5 text-[#CCC] text-xs">
                  <span
                    className={twMerge(
                      'inline-block  rounded-full w-1.5 h-1.5',
                      status === 'active' ? 'bg-green-500' : 'bg-red-500'
                    )}
                  />
                  {roamingSimStatus[status]}
                </span>
              </p>
            )}
            {plain && status !== 'expired' && (
              <p className="text-[#949494] text-xs ">{getExpiresIn(expires)}</p>
            )}
          </div>
        </div>

        {expired ? (
          <div>
            <Link to={ROUTES.Settings} search={{ 'travel-esim': 2 }}>
              <Button
                color="gray"
                size="small"
                fullWidth
                onClick={() => setRoamingZone(zone)}
              >
                Renew plan
              </Button>
            </Link>
          </div>
        ) : (
          <div className="w-full">
            <div className="flex justify-between font-bold space-x-2 mb-2 text-xs">
              <span className="text-[#CCC]">{used.toFixed(1)} used</span>
              <span className="text-[#949494]">{left.toFixed(1)} left</span>
            </div>
            <div className="relative h-1.5">
              <span className="block size-full rounded-xl bg-black opacity-10" />
              <span
                className="h-full rounded-xl absolute top-0 left-0 bg-[#949494]"
                style={{
                  width: `${percentageUsed}%`
                }}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
