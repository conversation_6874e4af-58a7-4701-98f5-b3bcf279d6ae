import React, { useEffect, useState } from 'react';
import { twMerge } from 'tailwind-merge';
import { CheckRoundedFilledIcon } from 'src/assets/icons/CheckRoundedFilled.tsx';
import Button from 'components/common/Button.tsx';
import { useNavigate } from '@tanstack/react-router';
import { ROUTES } from 'src/config/routes';
import CompatibilityCheckDrawer from 'components/sign-up/CompatibilityCheckDrawer';
import { CheckIcon } from 'src/assets/icons/Check.tsx';
import { getCorrelationId } from 'src/config/localStorageActions.ts';
import { ClockIcon } from 'src/assets/icons/Clock.tsx';
import { LockerIcon } from 'src/assets/icons/Locker.tsx';
import { LeafIcon } from 'src/assets/icons/Leaf.tsx';
import { RightArrowIcon } from 'src/assets/icons';
import { useRequest } from 'src/hooks/useRequest.ts';
import { updateSubscriber } from 'src/api/data.ts';
import useSubscription from 'src/hooks/useSubscription.tsx';

const disabled_physical_sim =
  import.meta.env.VITE_DISABLE_PHYSICAL_SIM === 'true';

type SimTypeOption = 'esim' | 'physical' | 'self-serve';

const optionStyles = 'w-full bg-white rounded-xl min-h-30 p-4 pt-6 relative';
const disabledStyles = 'opacity-50 hover:opacity-80';

const eSimCons = [
  {
    title: 'Set up right now, in minutes',
    description: 'We’ll walk you step-by-step through the process. ',
    icon: <ClockIcon className="size-7" />
  },
  {
    title: 'Stay more secure',
    description: 'Harder to hack if your phone gets stolen',
    icon: <LockerIcon className="size-7" />
  },
  {
    title: 'Do your bit for the planet',
    description: 'No plastic, no packaging, and no delivery emissions',
    icon: <LeafIcon className="size-7" />
  }
];

export function ChooseSimForLocalSim({
  onNextClick,
  selectedSimType,
  handleSimTypeSelect
}: {
  selectedSimType: SimTypeOption;
  handleSimTypeSelect: (simType: SimTypeOption) => void;
  onNextClick: (selectedSimType: SimTypeOption) => void;
}) {
  const { run: runUpdateSubscriber } = useRequest(updateSubscriber);
  const { currentPlan, setSubscriber, setCurrentPlanByCorrelationId } =
    useSubscription();

  const [isCompatibilityConfirmed, setIsCompatibilityConfirmed] =
    useState(false);

  const correlationId = getCorrelationId();

  useEffect(() => {
    if (correlationId) {
      setCurrentPlanByCorrelationId(correlationId);
    }
  }, [correlationId, setCurrentPlanByCorrelationId]);

  const isNextDisabled =
    selectedSimType === 'esim' && !isCompatibilityConfirmed;

  const handleSimTypeSubmit = () => {
    const simType = selectedSimType === 'esim' ? 'esim' : 'physical';

    runUpdateSubscriber({
      sim_type: simType,
      subscription_id: currentPlan.id
    }).then((data) => {
      setSubscriber(data);
      onNextClick(selectedSimType);
    });
  };

  return (
    <div className="h-full overflow-y-auto">
      <div className="flex flex-col gap-4 pb-10">
        <div className="flex-1 flex flex-col gap-4 relative">
          <ESIM
            isCompatibilityConfirmed={isCompatibilityConfirmed}
            setIsCompatibilityConfirmed={setIsCompatibilityConfirmed}
            handleSimTypeSelect={handleSimTypeSelect}
            selectedSimType={selectedSimType}
          />
          <PhysicalSim
            handleSimTypeSelect={handleSimTypeSelect}
            selectedSimType={selectedSimType}
          />
          <SelfActivation
            handleSimTypeSelect={handleSimTypeSelect}
            selectedSimType={selectedSimType}
          />
        </div>
        <button
          disabled={isNextDisabled}
          className={twMerge(
            'fixed bottom-4 right-4 w-fit ml-auto flex items-center justify-center gap-2 px-[20px] py-[12px] rounded-[30px] bg-white/90 text-black text-sm font-semibold',
            isNextDisabled ? 'opacity-25 bg-[#dadada]' : 'cursor-pointer'
          )}
          onClick={handleSimTypeSubmit}
        >
          Next
          <RightArrowIcon />
        </button>
      </div>
    </div>
  );
}

interface Props {
  selectedSimType: SimTypeOption;
  handleSimTypeSelect: (simType: SimTypeOption) => void;
}

export function SelfActivation({
  selectedSimType,
  handleSimTypeSelect
}: Props) {
  return (
    <button
      className={twMerge(
        'w-full relative',
        selectedSimType !== 'self-serve' && disabledStyles,
        disabled_physical_sim ? 'pointer-events-none' : 'cursor-pointer'
      )}
      onClick={() => handleSimTypeSelect('self-serve')}
    >
      <div
        className={twMerge(
          optionStyles,
          'flex flex-col h-full justify-center items-start pb-6'
        )}
      >
        <span className="font-bold text-xl text-left">
          I already have a physical SIM
        </span>
        <p className="text-gray-500 text-xs mt-1">Activate now</p>

        {disabled_physical_sim && (
          <p className="bg-gray-200 absolute top-2.5 right-2.5 text-[10px] rounded-full px-2.5 py-0.5 uppercase font-semibold">
            coming soon
          </p>
        )}
      </div>
    </button>
  );
}

export function PhysicalSim({ selectedSimType, handleSimTypeSelect }: Props) {
  return (
    <button
      className={twMerge(
        'w-full relative',
        selectedSimType !== 'physical' && disabledStyles,
        disabled_physical_sim ? 'pointer-events-none' : 'cursor-pointer'
      )}
      onClick={() => handleSimTypeSelect('physical')}
    >
      <div
        className={twMerge(
          optionStyles,
          'flex flex-col h-full justify-center items-start pb-6'
        )}
      >
        <span className="font-bold text-xl">Physical SIM</span>
        <p className="text-gray-500 text-xs mt-1">3 day delivery</p>
        {disabled_physical_sim && (
          <p className="bg-gray-200 absolute top-2.5 right-2.5 text-[10px] rounded-full px-2.5 py-0.5 uppercase font-semibold">
            coming soon
          </p>
        )}
      </div>
    </button>
  );
}

interface ESIMProps extends Props {
  setIsCompatibilityConfirmed: React.Dispatch<React.SetStateAction<boolean>>;
  isCompatibilityConfirmed: boolean;
}

export function ESIM({
  selectedSimType,
  handleSimTypeSelect,
  setIsCompatibilityConfirmed,
  isCompatibilityConfirmed
}: ESIMProps) {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  const openCompatibilityCheck = () => {
    handleSimTypeSelect('esim');
    setIsDrawerOpen(true);
  };

  const compatibilityConfirmedToggle = () => {
    if (!isCompatibilityConfirmed) {
      handleSimTypeSelect('esim');
    }
    setIsCompatibilityConfirmed(!isCompatibilityConfirmed);
  };

  return (
    <div className="space-y-6">
      <button
        className={twMerge(
          'w-full flex gap-4 items-center cursor-pointer',
          selectedSimType !== 'esim' && disabledStyles
        )}
        onClick={() => handleSimTypeSelect('esim')}
      >
        <div className={twMerge(optionStyles, 'divide-y')}>
          <div className="text-left mb-5">
            <span className="font-bold text-xl">eSIM</span>
            <p className="text-gray-500 text-xs mt-1">Instant activation</p>
          </div>

          <div className="sim-label absolute top-2.5 right-2.5 text-white text-[10px] rounded-full px-2.5 py-0.5 uppercase font-semibold">
            Recommended
          </div>
          <div>
            <div className="pt-5 space-y-4 mb-5">
              {eSimCons.map(({ description, icon, title }) => (
                <div key={title} className="flex items-center gap-5 text-left">
                  {icon}
                  <div className="">
                    <span className="block font-bold text-xs">{title}</span>
                    <p className="text-[10px]">{description}</p>
                  </div>
                </div>
              ))}
            </div>
            <button
              className="rounded-lg border p-2 text-xs text-center"
              onClick={(e) => {
                e.stopPropagation();
                openCompatibilityCheck();
              }}
            >
              Please check that your device is{' '}
              <span className="underline text-green">eSIM compatible</span>
            </button>
            <div className="mt-4 relative">
              <Button
                type="button"
                color="black"
                fullWidth
                squared
                fontSize="small"
                onClick={(e) => {
                  e.stopPropagation();
                  compatibilityConfirmedToggle();
                }}
              >
                <span
                  className={twMerge(
                    'absolute top-2 md:top-3 left-2 md:left-3 bg-[#404040] rounded-md text-white p-1.5 pointer-events-none',
                    isCompatibilityConfirmed && 'bg-green'
                  )}
                >
                  <CheckIcon
                    className="size-3"
                    color={isCompatibilityConfirmed ? 'white' : '#808080'}
                  />
                </span>
                {isCompatibilityConfirmed
                  ? 'Compatibility confirmed'
                  : 'Confirm device compatibility'}
              </Button>
            </div>

            <CompatibilityCheckDrawer
              title="Compatibility check"
              isOpen={isDrawerOpen}
              onClose={() => setIsDrawerOpen(false)}
            />
          </div>
        </div>
      </button>
    </div>
  );
}

export function SuccessStep({
  selectedSimType
}: {
  selectedSimType: SimTypeOption;
}) {
  const navigate = useNavigate();
  const handleGoToDashboard = () => {
    navigate({ to: ROUTES.Dashboard });
    // removeNewSubscriptionStep();
  };
  return (
    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 flex flex-col items-center justify-center space-y-6">
      <div className="validation valid">
        <CheckRoundedFilledIcon className="size-12" />
      </div>
      <div className="w-max font-semibold text-center space-y-2">
        <h3 className="text-xl mb-4">And you are all done!</h3>
        {selectedSimType !== 'esim' && (
          <p className="leading-normal">
            Your SIM is on the way
            <br /> and should be delivered in the next 2 days.
          </p>
        )}
      </div>
      <button
        className="w-max px-[20px] py-[12px] rounded-[30px] bg-black text-white text-sm font-semibold"
        onClick={handleGoToDashboard}
      >
        Go to dashboard
      </button>
    </div>
  );
}
