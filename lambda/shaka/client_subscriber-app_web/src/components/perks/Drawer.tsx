import { Transition, Dialog } from '@headlessui/react';
import { createPortal } from 'react-dom';
import CloseButton from '../common/CloseButton';
import { useState } from 'react';

type DrawerProps = {
  isOpen: boolean;
  onClose?: () => void;
  title?: string;
  children: React.ReactNode;
};

export default function Drawer({
  isOpen,
  onClose = () => null,
  children
}: DrawerProps) {
  const [touchStart, setTouchStart] = useState(0);

  return (
    <>
      {createPortal(
        <Transition show={isOpen}>
          <Dialog className="relative z-10" onClose={onClose}>
            <Transition.Child
              enter="ease-in-out duration-100"
              enterFrom="opacity-0"
              enterTo="opacity-100"
              leave="ease-in-out duration-100"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <div className="transition-all fixed inset-0 bg-gray-300/75 backdrop-blur-sm" />
            </Transition.Child>

            <Dialog.Panel className="fixed inset-0 top-10 max-w-md m-auto">
              <Transition.Child
                enter="transform transition ease-in-out duration-500 sm:duration-700"
                enterFrom="translate-y-full"
                enterTo="translate-y-0"
                leave="transform transition ease-in-out duration-500 sm:duration-700"
                leaveFrom="translate-y-0"
                leaveTo="translate-y-full"
                className="h-full"
              >
                <div
                  className="pointer-events-auto relative h-full bg-[#F3F3F3] shadow-xl overflow-hidden rounded-t-3xl"
                  onTouchStart={(e) => {
                    setTouchStart(e.touches[0].clientY);
                  }}
                  onTouchEnd={(e) => {
                    if (e.changedTouches[0].clientY - touchStart > 50) {
                      onClose();
                    }
                  }}
                >
                  <div className="relative">
                    <div className="absolute top-4 right-4 z-10">
                      <CloseButton onClose={onClose} />
                    </div>
                  </div>
                  {children}
                </div>
              </Transition.Child>
            </Dialog.Panel>
          </Dialog>
        </Transition>,
        document.body
      )}
    </>
  );
}
