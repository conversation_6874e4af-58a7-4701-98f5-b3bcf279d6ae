import { CheckRoundedFilledIcon } from "src/assets/icons/CheckRoundedFilled";

interface Props {
  progress: number;
}

export default function ProgressBar({ progress }: Props) {
  return (
    <div className="w-full bg-white/15 h-[6px] mt-2 rounded-full relative">
      <div
        className="bg-white h-full rounded-full"
        style={{
          width: `${progress}%`,
        }}
      ></div>
      {progress === 100 && (
        <div className="absolute top-0 right-0 -translate-y-1.5 text-black">
          <CheckRoundedFilledIcon className="size-5" />
        </div>
      )}
    </div>
  );
}
