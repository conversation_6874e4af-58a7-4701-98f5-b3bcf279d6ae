import { Link } from '@tanstack/react-router';
import { RightArrowIcon } from 'src/assets/icons';
import { ROUTES } from 'src/config/routes';
import usePerks from 'src/hooks/usePerks';
import { CheckRoundedFilledIcon } from 'src/assets/icons/CheckRoundedFilled';
import { Perk } from 'src/types/perks';
import { useClient } from 'src/hooks/useClient';
import { getCostTypeLabel } from './helpers';
import { AirDropIcon } from 'src/assets/icons/AirDrop';
import { GiftIcon } from 'src/assets/icons/Gift';

const FeaturedPerk = ({
  id,
  image,
  title,
  isClaimed,
  progress,
  eligibilityType,
  cost
}: Perk) => {
  const { pointsNaming } = useClient();

  const left = ((100 - progress) * cost) / 100;
  const costTypeLabel = getCostTypeLabel(eligibilityType, left, pointsNaming);
  const giftType =
    eligibilityType === 'airdrop' || eligibilityType === 'total_spend';

  return (
    <>
      <Link to={ROUTES.Perks} search={{ perk: id }}>
        <div className="bg-white rounded-2xl px-4 py-5 flex gap-5">
          <div className="flex-none w-[68px] h-[68px] overflow-hidden rounded-lg">
            {image ? (
              <img
                alt="perk-cover"
                src={image}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="bg-black/10 w-full h-full" />
            )}
          </div>
          <div className="grow flex flex-col gap-2 justify-center">
            <div className="flex justify-between items-center font-semibold text-sm gap-4">
              <h3>{title}</h3>
              <div className="flex-shrink-0 basis-auto">
                {giftType ? (
                  <>
                    {eligibilityType === 'airdrop' && (
                      <AirDropIcon className="min-w-4 size-4 opacity-80" />
                    )}

                    {eligibilityType === 'total_spend' && (
                      <GiftIcon className="min-w-4 size-4 opacity-80" />
                    )}
                  </>
                ) : (
                  <>
                    {left > 0 ? (
                      <p className="text-primary text-xs">
                        {costTypeLabel} left
                      </p>
                    ) : (
                      <CheckRoundedFilledIcon className="min-w-5 size-5" />
                    )}
                  </>
                )}
              </div>
            </div>

            <div className="w-full">
              <div className="relative h-2">
                <span className="block size-full rounded-xl bg-black opacity-10" />
                <span
                  className="h-full rounded-xl absolute top-0 left-0 bg-black"
                  style={{
                    width: `${progress}%`
                  }}
                />
              </div>
            </div>
            <p className="text-center text-xs font-semibold">
              {isClaimed ? 'Claimed!' : `${progress.toFixed()}%`}
            </p>
          </div>
        </div>
      </Link>
    </>
  );
};

export default function FeaturedPerks() {
  const { featuredPerks } = usePerks();

  if (!featuredPerks.length) return null;

  return (
    <div className="mt-10">
      <div className="flex justify-between items-center mb-4">
        <h2 className="uppercase font-semibold text-md">Featured perk</h2>
        <Link to={ROUTES.Perks} className="inline-flex items-center gap-3">
          <span className="text-xs font-semibold">All perks</span>
          <button className="bg-white rounded-md p-2.5 hover:bg-white/50">
            <RightArrowIcon className="size-3" />
          </button>
        </Link>
      </div>

      <div className="space-y-2 mb-10">
        {featuredPerks.map((perk) => (
          <div key={perk.id}>
            <FeaturedPerk {...perk} />
          </div>
        ))}
      </div>
    </div>
  );
}
