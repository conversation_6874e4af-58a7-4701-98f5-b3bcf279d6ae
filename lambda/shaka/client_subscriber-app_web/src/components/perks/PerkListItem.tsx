import { Link } from '@tanstack/react-router';
import { ROUTES } from 'src/config/routes';
import { Perk } from 'src/types/perks';
import { AirDropIcon } from 'src/assets/icons/AirDrop';
import ProgressBar from './ProgressBar';
import { claimDetails, getCostTypeLabel } from './helpers';
import { twMerge } from 'tailwind-merge';
import { GiftIcon } from 'src/assets/icons/Gift';
import { useClient } from 'src/hooks/useClient';

interface Props {
  perk: Perk;
  canClaimByPoints: boolean;
}

export default function PerkListItem({ perk, canClaimByPoints }: Props) {
  const { pointsNaming } = useClient();
  const { title, eligibilityType, cost, progress, isClaimed, image, amount } =
    perk;

  const currentCost = eligibilityType === 'no_free' ? Number(amount) : cost;
  const costTypeLabel = getCostTypeLabel(
    eligibilityType,
    currentCost,
    pointsNaming
  );
  const perkType = 'Discount';

  const { canBeClaimed, possibleToClaimByPoints } = claimDetails(
    perk,
    canClaimByPoints
  );

  const showClaimEarly =
    eligibilityType !== 'airdrop' &&
    eligibilityType !== 'total_spend' &&
    possibleToClaimByPoints;

  const isFreeRedeem = eligibilityType !== 'no_free';

  return (
    <>
      <Link to={ROUTES.Perks} search={{ perk: perk.id }} className="block mb-5">
        <div className="relative bg-gray-100 rounded-3xl overflow-hidden drop-shadow-lg w-full pb-[100%] -z-[1]">
          {image ? (
            <img
              src={image}
              alt="Skyline"
              loading="eager"
              className="absolute w-full h-full object-cover"
            />
          ) : (
            <div className="bg-black/10 w-full h-full" />
          )}
          <div className="absolute inset-0 top-1/3 bg-gradient-to-t from-black/80 to-transparent pointer-events-none" />
          <div className="absolute top-2 left-0 m-4 bg-white/20 text-white text-xs px-3 py-1 rounded-lg uppercase backdrop-blur-sm">
            {perkType}
          </div>
          <div className="absolute bottom-0 left-0 right-0 p-4 py-6">
            <div className="flex justify-between items-end text-white mb-3">
              <span className="text-lg font-semibold max-w-[60%]">{title}</span>

              <div className="flex flex-col items-end gap-1">
                {progress < 100 &&
                  showClaimEarly &&
                  !isClaimed &&
                  isFreeRedeem && (
                    <span
                      className={twMerge(
                        'bg-gray-100/70 text-black uppercase text-[10px] px-1.5 rounded-full',
                        canClaimByPoints && 'bg-primary'
                      )}
                    >
                      redeem early
                    </span>
                  )}

                {eligibilityType === 'airdrop' && <AirDropIcon />}

                {eligibilityType === 'total_spend' && <GiftIcon />}

                {eligibilityType !== 'airdrop' &&
                  eligibilityType !== 'total_spend' && (
                    <span className="text-lg font-semibold">
                      {costTypeLabel}
                    </span>
                  )}
              </div>
            </div>
            <ProgressBar progress={progress} />
          </div>
          {isClaimed && (
            <div className="absolute inset-0 bg-black/60 text-white flex items-center justify-center uppercase font-semibold text-2xl px-6">
              {canBeClaimed ? (
                <div className="flex flex-col justify-center items-center">
                  <span>ALREADY CLAIMED, </span>
                  <span>CLAIM AGAIN?</span>
                </div>
              ) : (
                'Already claimed'
              )}
            </div>
          )}
        </div>
      </Link>
    </>
  );
}
