import clsx from "clsx";
import { ElementType, PropsWithChildren } from "react";
import { Spring, animated } from "react-spring";
import { GestureState, withGesture } from "react-with-gesture";

const DefaultComponent = ({ children }: PropsWithChildren) => {
  return <div>{children}</div>;
};

const HIGHT_CLICK_THRESHOLD = -30;
const LOW_CLICK_THRESHOLD = -75;
const INITIAL_TRANSLATE = 50;
const PREV_TRANSLATE = -60;
const SLIDE_SCALE_FACTOR = 0.06;
const TRANSLATE_DIFF = 7;

type SlideProps<T> = {
  content: T;
  backSlidesAmount: number;
  index: number;
  moveSlide: (direction: number) => void;
  slideProps?: Record<string, unknown>;
  Component?: ElementType;
  onCardClick?: (slide: unknown) => void;
} & GestureState;

const Slide = <T,>({
  content,
  index,
  delta,
  down,
  moveSlide,
  backSlidesAmount,
  Component = DefaultComponent,
  onCardClick,
}: SlideProps<T>) => {
  const offsetIndex = index;

  const distanceFactor = 1 - Math.abs(offsetIndex) * SLIDE_SCALE_FACTOR;
  const isSlideVisible = Math.abs(offsetIndex) <= backSlidesAmount;
  const opacity = isSlideVisible ? 1 : 0;
  const zIndex = isSlideVisible ? Math.abs(offsetIndex - backSlidesAmount) : -1;
  const top = `${INITIAL_TRANSLATE + offsetIndex / backSlidesAmount}%`;

  const translateYoffset = TRANSLATE_DIFF * Math.abs(offsetIndex);

  let translateY = -INITIAL_TRANSLATE;

  if (offsetIndex === 0 && down) {
    translateY += delta[1] / (backSlidesAmount + 1);
    if (translateY > HIGHT_CLICK_THRESHOLD) {
      moveSlide(-1);
    }
    if (translateY < LOW_CLICK_THRESHOLD) {
      moveSlide(1);
    }
  }

  if (offsetIndex >= 0) {
    translateY += translateYoffset;
  } else if (offsetIndex < 0) {
    translateY -= translateYoffset;
  }

  if (!isSlideVisible) {
    translateY = PREV_TRANSLATE;
  }

  const handleCardClick = () => {
    if (offsetIndex === 0 && onCardClick) {
      onCardClick(content);
    }

    moveSlide(offsetIndex);
  };

  return (
    <Spring
      to={{
        top,
        opacity,
        transform: `translateX(0%) translateY(${translateY}%) scale(${distanceFactor})`,
      }}
    >
      {(style) => (
        <animated.div
          style={{
            ...style,
            zIndex: zIndex,
            touchAction: "none",
          }}
          className={clsx("slideContainer", offsetIndex === 0 && "active")}
        >
          <Component onClick={handleCardClick} {...content} id={offsetIndex}/>
        </animated.div>
      )}
    </Spring>
  );
};

export default withGesture({})(Slide);
