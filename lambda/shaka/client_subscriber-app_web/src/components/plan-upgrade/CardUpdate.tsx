import { useContext } from 'react';
import { getClientSecretCard } from 'src/api/stripe';
import { StripeContext } from 'src/context/StripeContext';
import {
  EmbeddedCheckout,
  EmbeddedCheckoutProvider
} from '@stripe/react-stripe-js';

export default function CardUpdate({
  redirectUrl,
  subscriptionId
}: {
  redirectUrl?: string;
  subscriptionId?: string | number;
}) {
  const { stripePromise } = useContext(StripeContext);

  return (
    <div>
      <EmbeddedCheckoutProvider
        stripe={stripePromise}
        options={{
          fetchClientSecret: getClientSecretCard({
            returnURL: redirectUrl,
            subscriptionId
          })
        }}
      >
        <EmbeddedCheckout />
      </EmbeddedCheckoutProvider>
    </div>
  );
}
