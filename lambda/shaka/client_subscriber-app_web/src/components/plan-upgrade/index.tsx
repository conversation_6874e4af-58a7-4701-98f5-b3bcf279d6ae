import Drawer from '../common/Drawer';
import { useEffect, useState } from 'react';
import Dialog from '../common/Dialog';
import PlansList from './PlansList';
import PlanConfirmation from './PlanConfirmation';
import CardUpdate from './CardUpdate';
import { useNavigate } from '@tanstack/react-router';
import clsx from 'clsx';
import PlanUpdate from './PlanUpdate';
import ProgressLinks from '../common/ProgressLinks';
import useSubscription from 'src/hooks/useSubscription';
import { ROUTES } from 'src/config/routes';
import usePlans from 'src/hooks/usePlans';
import SubscriptionCustomName from '../SubscriptionCustomName';
import { defaultSimName } from 'src/helpers';

function PlanUpgradeContent({
  step,
  absoluteNav
}: {
  step: number;
  absoluteNav?: boolean;
}) {
  const { plans } = usePlans();
  const { currentPlanIndex, currentPlan } = useSubscription();
  const [selectedPlan, setSelectedPlan] = useState<number | null>(() => {
    const savedPlanId = localStorage.getItem('updatePlanId');
    if (!savedPlanId) return null;
    return Number(savedPlanId);
  });

  const handlePlanSelection = (planId: number) => {
    setSelectedPlan(planId);
    localStorage.setItem('updatePlanId', planId.toString());
  };

  const planName = currentPlan.user_subscription_name;
  const prevStep = step - 1;
  const nextStep = step === 2 ? step + 2 : step + 1;

  const showNav = plans.length > 0 && (step === 1 || step === 2);

  return (
    <>
      <p className="mb-2 text-center">
        <SubscriptionCustomName
          planName={planName || `${defaultSimName} ${currentPlanIndex + 1}`}
        />
      </p>
      {step === 1 && (
        <PlansList
          onPlanSelect={handlePlanSelection}
          selectedPlan={selectedPlan}
        />
      )}
      {step === 2 && (
        <PlanConfirmation
          plans={plans}
          planId={selectedPlan}
          planName={planName || `${defaultSimName} ${currentPlanIndex + 1}`}
        />
      )}
      {step === 3 && <CardUpdate redirectUrl={`/settings?plan-change=4`} />}
      {step === 4 && <PlanUpdate />}

      {showNav && (
        <div className="absolute w-full inset-x-0 md:-bottom-4 max-md:px-5 bottom-10">
          <ProgressLinks
            backTo={`/settings?plan-change=${prevStep}`}
            nextTo={`/settings?plan-change=${nextStep}`}
            hideBackButton={step === 1}
            disabledNext={step === 1 && !selectedPlan}
            absolute={absoluteNav}
          />
        </div>
      )}
    </>
  );
}

export default function PlanUpgradeDrawer({ step }: { step: number }) {
  const { currentPlan } = useSubscription();
  const isPlanChanging =
    currentPlan.latest_plan_change?.status === 'in_progress' &&
    currentPlan.latest_plan_change?.change_type !== 'cancel_change';

  const [isMobile] = useState(window.innerWidth < 640);
  const [open, setOpen] = useState(false);

  const navigate = useNavigate();

  useEffect(() => {
    if (isPlanChanging) {
      navigate({ to: ROUTES.Settings });
      return;
    }

    setOpen(true);
  }, []);

  const handleClose = () => {
    localStorage.removeItem('updatePlanId');
    setOpen(false);
    setTimeout(() => {
      navigate({ to: ROUTES.Settings });
    }, 300);
  };

  return (
    <>
      {isMobile ? (
        <Drawer title="Plan upgrade" isOpen={open} onClose={handleClose}>
          <div
            className={clsx(
              'h-full pt-6 px-5 relative',
              step !== 1 && 'overflow-y-auto pb-16'
            )}
          >
            <PlanUpgradeContent step={step} />
          </div>
        </Drawer>
      ) : (
        <Dialog
          title="Plan upgrade"
          isOpen={open}
          onClose={handleClose}
          panelStyles="bg-[#F3F3F3]"
        >
          <div className="relative max-h-[800px] h-[70vh] text-black">
            <div className="h-full">
              <PlanUpgradeContent step={step} absoluteNav />
            </div>
          </div>
        </Dialog>
      )}
    </>
  );
}
