import { useEffect, useMemo } from 'react';
import usePlans from 'src/hooks/usePlans';
import FullPageLoader from '../common/FullPageLoader';
import CurrentPlan from './CurrentPlan';
import PlanPreview from './PlanPreview';
import BlockTitle from './BlockTitle';
import useSubscription from 'src/hooks/useSubscription';

export default function PlansList({
  selectedPlan,
  onPlanSelect
}: {
  selectedPlan: number | null;
  onPlanSelect: (planId: number) => void;
}) {
  const { subscriber, currentPlan } = useSubscription();
  const { plans, isLoading } = usePlans();

  const handlePlanSelection = (planId: number) => () => {
    onPlanSelect(planId);
  };

  const availablePlans = useMemo(
    () => plans.filter((plan) => plan.key !== currentPlan.plan_id),
    [plans, subscriber]
  );

  const plansExist = availablePlans.length !== 0;

  useEffect(() => {
    if (!selectedPlan) return;

    const selectedPlanElement = document.getElementById(
      `plan-preview-${selectedPlan}`
    );
    if (selectedPlanElement) {
      selectedPlanElement.scrollIntoView({ block: 'center' });
    }
  }, []);

  if (isLoading) return <FullPageLoader />;

  return (
    <div className="relative h-full">
      <div className="absolute backdrop-blur-md top-0 -inset-x-5 px-5 bg-gradient-to-t from-[#F3F3F3]/70 to-[#F3F3F3] z-10 max-sm:w-screen">
        <CurrentPlan />
        {plansExist && <BlockTitle>Choose from available plans</BlockTitle>}
      </div>
      <div className="pt-[180px] h-full pb-20 overflow-y-auto space-y-4 relative no-scrollbar">
        {availablePlans.map((plan) => (
          <PlanPreview
            key={plan.key}
            {...plan.clientPlan}
            onClick={handlePlanSelection(plan.key)}
            selected={selectedPlan === plan.key}
          />
        ))}
      </div>
      <div className="absolute -bottom-1 left-0 w-full h-10 bg-gradient-to-t from-[#F3F3F3] to-transparent" />
    </div>
  );
}
