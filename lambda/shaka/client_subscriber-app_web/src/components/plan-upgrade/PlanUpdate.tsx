import { useEffect, useState } from 'react';
import SuccessScreen from '../SuccessScreen';
import PageTitle from '../common/PageTitle';
import Loader from '../common/Loader';
import { supportEmail } from 'src/config/global';
import { updateSubscription } from 'src/api/subscription';
import useSubscription from 'src/hooks/useSubscription';
import usePlans from 'src/hooks/usePlans';

export default function PlanUpdate() {
  const [state, setState] = useState<'plan-update' | 'success' | 'error'>(
    'plan-update'
  );

  const { currentPlan, fetchSubscriber } = useSubscription();
  const { plans } = usePlans();

  useEffect(() => {
    const savedPlanId = localStorage.getItem('updatePlanId');
    const oldPlanPrice = currentPlan.price;
    const newPlanPrice = plans.find(({ key }) => key === Number(savedPlanId))
      ?.clientPlan.price;

    if (!savedPlanId) {
      setState('success');
      return;
    }

    const abortController = new AbortController();

    updateSubscription({
      changeType:
        Number(oldPlanPrice) <= Number(newPlanPrice) ? 'upgrade' : 'downgrade',
      planId: savedPlanId,
      requestConfig: { signal: abortController.signal },
      subscriptionId: currentPlan.id
    })
      .then(() => {
        setState('success');
        fetchSubscriber();
      })
      .catch(() => {
        if (abortController.signal.aborted) return;

        setState('error');
      })
      .finally(() => {
        localStorage.removeItem('updatePlanId');
      });

    return () => {
      abortController.abort();
    };
  }, []);

  if (state === 'error') {
    return (
      <div className="h-full flex flex-col justify-center items-center">
        <PageTitle>Something went wrong</PageTitle>
        <p className="text-center mb-20">
          We are unable to update your plan at the moment. You can contact us by
          email{' '}
          <span className="contact-phone hover:underline">
            <a href={`mailto:${supportEmail}`}>{supportEmail}</a>
          </span>
        </p>
      </div>
    );
  }

  if (state === 'plan-update') {
    return (
      <div className="h-full  flex flex-col justify-center items-center">
        <PageTitle>Transaction in progress</PageTitle>
        <p className="text-center mb-20">
          Your payment is being processed.
          <br />
          Please wait a moment
        </p>
        <Loader />
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col justify-center items-center">
      <SuccessScreen
        title="Upgrade in progress"
        description={
          <p>
            Your plan is being upgraded, we will let you know when it is ready!
          </p>
        }
      />
    </div>
  );
}
