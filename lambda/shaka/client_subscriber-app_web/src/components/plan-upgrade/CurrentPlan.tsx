import { currencyRounded, epochToDate, getRoamingLabel } from 'src/helpers';
import BigActionButton from '../common/BigActionButton';
import useSubscription from 'src/hooks/useSubscription';
import usePlans from 'src/hooks/usePlans';
import { cancelSubscription } from 'src/api/subscription';
import { useState } from 'react';
import BlockTitle from './BlockTitle';
import { useNavigate } from '@tanstack/react-router';
import { ROUTES } from 'src/config/routes';
import NotificationDialog from '../common/NotificationDialog';

export const getDataLabel = (data?: string, sms?: string, voice?: string) => {
  const isAllUnlimited =
    sms === 'unlimited' && voice === 'unlimited' && data === 'unlimited';

  const roamingLabel = getRoamingLabel(data);

  if (isAllUnlimited) {
    return `UNL data, texts & calls, ${roamingLabel}`;
  }

  const dataLabel = data === 'unlimited' ? 'Unlimited data' : `${data} GB`;
  const smsLabel = sms === 'unlimited' ? 'Unlimited SMS' : `${sms} SMS`;
  const voiceLabel = voice === 'unlimited' ? 'Unlimited mins' : `${voice} mins`;

  return (
    <div>{[dataLabel, smsLabel, voiceLabel, roamingLabel].join(', ')}</div>
  );
};

export default function CurrentPlan({
  showCancelButton = true
}: {
  showCancelButton?: boolean;
}) {
  const { plans } = usePlans();
  const { fetchSubscriber, currentPlan } = useSubscription();
  const navigate = useNavigate();

  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const planId = currentPlan.plan_id;
  const plan = plans.find((plan) => {
    return plan.key === planId;
  })?.clientPlan;
  const nexBillDate = epochToDate(currentPlan.next_bill_date_epoch);
  const canCancelPlan = currentPlan.can_cancel;

  const planName = plan?.title;

  const price = Number(plan?.price);
  const data = plan?.data;
  const sms = plan?.sms;
  const voice = plan?.voice;

  const planDescription = getDataLabel(data, sms, voice);

  const handleCancelSubscription = () => {
    cancelSubscription(currentPlan.id).then(() => {
      setIsDialogOpen(false);
      fetchSubscriber();
      closeDialog();
      navigate({ to: ROUTES.Settings });
    });
  };

  const closeDialog = () => {
    setIsDialogOpen(false);
  };

  return (
    <div className="mb-6">
      <BlockTitle>Current plan</BlockTitle>
      <div className="flex gap-2">
        <div className="grow bg-white rounded-2xl py-4 px-5 flex gap-2 justify-between items-center">
          <div>
            <p className="font-semibold text-sm">{planName}</p>
            <p className="text-[0.625rem] leading-snug">{planDescription}</p>
          </div>
          <div>
            <span className="font-semibold text-sm">
              {currencyRounded(price)}
            </span>
          </div>
        </div>
        {showCancelButton && (
          <BigActionButton
            actionType="cancel"
            cancelText="Cancel"
            onClick={() => setIsDialogOpen(true)}
            disabled={!canCancelPlan}
          />
        )}

        <NotificationDialog
          onCancel={closeDialog}
          onSubmit={handleCancelSubscription}
          title="Are you sure you want to cancel this plan?"
          description={
            <>
              In case of cancellation, the plan will remain active until{' '}
              {nexBillDate}
            </>
          }
          isOpen={isDialogOpen}
        />
      </div>
    </div>
  );
}
