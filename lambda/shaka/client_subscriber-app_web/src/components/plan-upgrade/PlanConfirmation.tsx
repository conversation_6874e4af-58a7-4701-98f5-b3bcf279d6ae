import CurrentPlan from './CurrentPlan';
import PlanCard from '../PlanSlideCard';
import BlockTitle from './BlockTitle';
import { ClientPlanDetails } from 'src/types/slide.ts';

interface PlanConfirmationProps {
  planId: number | null;
  plans: ClientPlanDetails[];
  planName: string;
}

export default function PlanConfirmation({
  planId,
  plans,
  planName
}: PlanConfirmationProps) {
  const plan = plans.find((plan) => plan.key === planId);
  return (
    <div className="pb-10">
      <CurrentPlan showCancelButton={false} />

      <BlockTitle>New plan</BlockTitle>
      <PlanCard key={plan?.key} clientPlan={plan?.clientPlan} />

      <div className="mt-8 mx-3 space-y-6">
        <div className="flex justify-between text-xs">
          <span className="font-semibold">SIM card</span>
          <span className="text-gray-500">{planName}</span>
        </div>
        <div className="flex justify-between text-xs">
          <span className="font-semibold">Activation date</span>
          <span className="text-gray-500">Now</span>
        </div>
      </div>
    </div>
  );
}
