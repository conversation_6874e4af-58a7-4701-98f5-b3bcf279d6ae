import { useMemo, useState } from 'react';
import withDrawer, { WithDrawerProps } from 'src/hocs/withDrawer';
import ExplorePlans from '../ExplorePlans';
import PlanConfirmation from '../PlanConfirmation';
import usePlans from 'src/hooks/usePlans';
import ProgressLinks from '../common/ProgressLinks';
import NewPlanPayment from './NewPlanPayment';
import {
  ChooseSimForLocalSim,
  SuccessStep
} from 'components/ChooseSimForLocalSim.tsx';
import AddressDetailsNewSubscription from 'components/AdressDetailsNewSubscription.tsx';
import SimSelfActivation from 'components/SimActivation.tsx';
import EsimSettings from 'components/EsimSettings.tsx';
import { SimTypeOption } from 'components/SelectSimType.tsx';
import { getSimSettingsSelectedType } from 'src/config/localStorageActions.ts';

export enum Step {
  ExplorePlans,
  PlanConfirmation,
  Payment,
  SelectSimType,
  EsimSettings,
  AddressDetails,
  SimActivation,
  Congratulations
}

function NewSubscriptionContent({
  initialStep
}: {
  initialStep?: string | null;
} & WithDrawerProps) {
  const { plans } = usePlans();
  const [planId, setPlanId] = useState<number | null>(null);
  const [stepId, setStepId] = useState(
    initialStep ? Number(initialStep) : Step.ExplorePlans
  );
  const [isOptIn, setIsOptIn] = useState(true);
  const simPreselectedOption = getSimSettingsSelectedType() as SimTypeOption;
  const [selectedSimType, setSelectedSimType] = useState<SimTypeOption>(
    simPreselectedOption || 'esim'
  );

  const selectedPlan = useMemo(() => {
    return plans.find((plan) => plan.clientPlan.id === planId);
  }, [plans, planId]);

  const handlePlanConfirm = (id: number) => {
    setPlanId(id);
    setStepId(Step.PlanConfirmation);
  };

  const handleBack = () => {
    setStepId(Step.SelectSimType);
  };

  const handleSimTypeSelection = (selectedSimType: SimTypeOption) => {
    if (selectedSimType === 'physical') {
      setStepId(Step.AddressDetails);
    } else if (selectedSimType === 'esim') {
      setStepId(Step.EsimSettings);
    } else if (selectedSimType === 'self-serve') {
      setStepId(Step.SimActivation);
    }
  };

  return (
    <div className="px-5 pb-2 min-h-[80dvh] relative">
      {stepId === Step.ExplorePlans && (
        <ExplorePlans
          hideBackButton
          onPlanConfirm={handlePlanConfirm}
          progressLinksModalView
        />
      )}
      {stepId === Step.PlanConfirmation && (
        <div className="relative mt-[22px]">
          <PlanConfirmation
            isOptIn={isOptIn}
            selectedPlan={selectedPlan}
            onOptInChange={setIsOptIn}
          />
          <ProgressLinks
            onBackClick={() => setStepId(Step.ExplorePlans)}
            onNextClick={() => setStepId(Step.Payment)}
            isModalView
          />
        </div>
      )}
      {stepId === Step.Payment && (
        <NewPlanPayment planId={planId!} returnUrl="/new-subscription/return" />
      )}
      {stepId === Step.SelectSimType && (
        <ChooseSimForLocalSim
          selectedSimType={selectedSimType}
          handleSimTypeSelect={setSelectedSimType}
          onNextClick={handleSimTypeSelection}
        />
      )}
      {stepId === Step.AddressDetails && (
        <AddressDetailsNewSubscription
          onBackClick={handleBack}
          onNextClick={() => setStepId(Step.Congratulations)}
        />
      )}
      {stepId === Step.SimActivation && (
        <SimSelfActivation
          onBackClick={handleBack}
          onNextClick={() => setStepId(Step.Congratulations)}
        />
      )}
      {stepId === Step.EsimSettings && (
        <div className="h-full">
          <EsimSettings
            onShareEsim={() => setStepId(Step.Congratulations)}
            onBackClick={() => setStepId(Step.SelectSimType)}
            onNextClick={() => setStepId(Step.Congratulations)}
            progressLinksModalView
          />
        </div>
      )}
      {stepId === Step.Congratulations && (
        <SuccessStep selectedSimType={selectedSimType} />
      )}
    </div>
  );
}

const NewSubscriptionDrawer = withDrawer(NewSubscriptionContent);

export default NewSubscriptionDrawer;
