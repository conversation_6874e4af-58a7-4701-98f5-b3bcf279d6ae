import { InfoIcon } from 'src/assets/icons/Info';
import BigActionButton from './common/BigActionButton';
import { Link, LinkProps } from '@tanstack/react-router';

interface NotificationDialogProps {
  title: string;
  subtitle: string;
  link: LinkProps['to'];
  onDismiss: () => void;
}

export function NotificationDialog({ title, subtitle, link, onDismiss }: NotificationDialogProps) {
  return (
    <div className="grid grid-cols-4 gap-2">
      <Link to={link} className="col-span-3">
        <div className="bg-white rounded-2xl p-4 flex gap-3 items-center">
          <InfoIcon className="size-4 flex-shrink-0" />
          <div>
            <p className="text-[13px] font-semibold">{title}</p>
            <p className="text-[11px]">{subtitle}</p>
          </div>
        </div>
      </Link>
      <BigActionButton
        actionType="cancel"
        cancelText="Dismiss"
        onClick={onDismiss}
      />
    </div>
  );
}
