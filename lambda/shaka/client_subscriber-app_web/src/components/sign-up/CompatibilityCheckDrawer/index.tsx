import { useNavigate } from '@tanstack/react-router';
import { useState } from 'react';
import { updateSubscriber } from 'src/api/data';
import { RightArrowIcon } from 'src/assets/icons';
import { CheckGreenIcon } from 'src/assets/icons/CheckGreen';
import { CrossRedIcon } from 'src/assets/icons/CrossRed';
import Button from 'src/components/common/Button';
import Dropdown from 'src/components/common/Dropdown';
import { eSimCompatibleDevices } from 'src/config/e-sim';
import { ROUTES } from 'src/config/routes';
import withDrawer from 'src/hocs/withDrawer';
import { useRequest } from 'src/hooks/useRequest';
import useSubscription from 'src/hooks/useSubscription';

const brands = Object.keys(eSimCompatibleDevices);
const noDeviceLabel = 'My device is not listed';
const disabled_physical_sim =
  import.meta.env.VITE_DISABLE_PHYSICAL_SIM === 'true';
const clientSupportEmail = window.clientConfig.supportEmail;

const labelStyles = 'text-sm text-[#858585] font-semibold mb-2';

type Brand = keyof typeof eSimCompatibleDevices;

const CompatibilityCheckContent = () => {
  const navigate = useNavigate();
  const { currentPlan } = useSubscription();
  const { run: runUpdateSubscriber } = useRequest(updateSubscriber);

  const [selectedBrand, setSelectedBrand] = useState<Brand | null>(null);
  const [selectedModel, setSelectedModel] = useState<string | null>(null);

  const phoneIsCompatible =
    selectedBrand && selectedModel && selectedModel !== 'null';
  const dropdownValue =
    selectedModel === 'null' ? noDeviceLabel : selectedModel || '';
  const showPhysicalSimButton = !disabled_physical_sim && !phoneIsCompatible;

  const handleBrandSelection = (value: string) => {
    setSelectedBrand(value as Brand);
    setSelectedModel(null);
  };

  const handleEsimActivation = () => {
    runUpdateSubscriber({ sim_type: 'esim', subscription_id: currentPlan.id });
    navigate({ to: ROUTES.EsimSettings });
  };

  const continueWithPhysicalSim = () => {
    runUpdateSubscriber({
      sim_type: 'physical',
      subscription_id: currentPlan.id
    });
    navigate({ to: ROUTES.AddressDetails });
  };

  const options = selectedBrand
    ? [
        ...eSimCompatibleDevices[selectedBrand].map((model) => ({
          label: model,
          value: model
        })),
        { label: noDeviceLabel, value: 'null' }
      ]
    : [];

  return (
    <div className="px-5 pb-2 h-[380px] flex flex-col text-black mb-6">
      <div className="grow">
        <div className="mb-5">
          <p className={labelStyles}>What make of phone do you have?</p>
          <Dropdown
            placeholder="Select brand"
            value={selectedBrand || ''}
            options={brands.map((brand) => ({ label: brand, value: brand }))}
            onSelect={handleBrandSelection}
          />
        </div>
        <div className="mb-5">
          <p className={labelStyles}>What model of phone do you have?</p>
          <Dropdown
            placeholder="Select model"
            value={dropdownValue}
            options={options}
            onSelect={setSelectedModel}
            disabled={!selectedBrand}
          />
        </div>

        {!selectedModel && (
          <p className="text-sm">
            If your device isn't listed here, we recommend opting for a physical
            SIM to ensure compatibility
          </p>
        )}

        {selectedModel && selectedModel !== 'null' && (
          <p className="bg-white text-sm px-3 py-3.5 rounded-2xl flex gap-3 items-center font-semibold border border-[#1EC25F]">
            <CheckGreenIcon className="w-8 h-8" />
            It’s a match. Your device is eSIM enabled.
          </p>
        )}

        {selectedModel === 'null' && (
          <p className="bg-white text-sm px-3 py-3.5 rounded-2xl flex gap-3 items-center font-semibold border border-[#E03E8C]">
            <CrossRedIcon className="min-w-8 h-8" />
            {disabled_physical_sim ? (
              <span>
                This device is not eSIM enabled - please contact{' '}
                <a href={clientSupportEmail} className="underline">
                  {clientSupportEmail}
                </a>{' '}
                for more information.
              </span>
            ) : (
              <span>
                This device is not eSIM enabled - you’ll need a physical SIM
                instead.
              </span>
            )}
          </p>
        )}
      </div>

      {phoneIsCompatible && (
        <Button
          color="black"
          fullWidth
          rightIcon={
            <RightArrowIcon className="size-4 absolute right-8 top-3" />
          }
          fontSize="small"
          onClick={handleEsimActivation}
        >
          Continue with eSIM
        </Button>
      )}
      {showPhysicalSimButton && (
        <Button
          color="black"
          fullWidth
          rightIcon={
            <RightArrowIcon className="size-4 absolute right-8 top-3" />
          }
          fontSize="small"
          onClick={continueWithPhysicalSim}
        >
          Continue with physical SIM
        </Button>
      )}
    </div>
  );
};

const CompatibilityCheckDrawer = withDrawer(CompatibilityCheckContent);

export default CompatibilityCheckDrawer;
