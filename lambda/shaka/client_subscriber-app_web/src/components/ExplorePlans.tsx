import { useForm } from 'react-hook-form';
import usePlans from 'src/hooks/usePlans';
import clsx from 'clsx';
import { useEffect } from 'react';
import PlanSlideCard from 'src/components/PlanSlideCard';
import VerticalCarousel from 'src/components/VerticalCarousel';
import Container from 'src/components/common/Container';
import FullPageLoader from 'src/components/common/FullPageLoader';
import ProgressLinks from 'src/components/common/ProgressLinks';
import { getAwaitingPlanId, setPlanId } from 'src/config/localStorageActions';
import { ROUTES } from 'src/config/routes';
import { currencyRounded } from 'src/helpers';
import { ClientPlanDetails } from 'src/types/slide';
import { twMerge } from 'tailwind-merge';

export default function ExplorePlans({
  hideBackButton,
  onPlanConfirm,
  progressLinksModalView
}: {
  hideBackButton: boolean;
  onPlanConfirm?: (planId: ClientPlanDetails['clientPlan']['id']) => void;
  progressLinksModalView?: boolean;
}) {
  const { plans, isLoading } = usePlans();

  const { watch, handleSubmit, setValue } = useForm<{ planIndex: number }>({
    defaultValues: {
      planIndex: 0
    }
  });

  const selectedPlanIndex = watch('planIndex');

  const onSubmit = () => {
    const selectedPlanId = plans[selectedPlanIndex].clientPlan.id;

    if (!selectedPlanId) return;

    setPlanId(selectedPlanId);

    if (onPlanConfirm) {
      onPlanConfirm(selectedPlanId);
    }
  };

  const onPlanChange = (index: number) => {
    setValue('planIndex', index);
  };

  useEffect(() => {
    if (plans && !isLoading) {
      const selectedPlanId = Number(getAwaitingPlanId());

      if (selectedPlanId) {
        const index = plans?.findIndex(
          (slide) => slide.clientPlan.id === selectedPlanId
        );

        index >= 0 && setValue('planIndex', index);
      }
    }
  }, [isLoading, setValue, plans]);

  if (isLoading) {
    return (
      <Container>
        <FullPageLoader />
      </Container>
    );
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div className="explore-content">
        {plans.length === 1 ? (
          <div className="mb-6">
            <PlanSlideCard clientPlan={plans[0].clientPlan} />
          </div>
        ) : (
          <div className="h-[240px] mb-12">
            <VerticalCarousel<ClientPlanDetails>
              defaultIndex={selectedPlanIndex}
              slides={plans}
              Component={PlanSlideCard}
              showNavigation
              onChange={onPlanChange}
            />
          </div>
        )}

        <div>
          {plans.map((slide, index) => (
            <button
              key={slide.clientPlan.id}
              type="button"
              className={clsx(
                'w-full rounded-lg px-3.5 py-3 flex justify-between bg-white text-sm text-left items-center mb-2',
                index === selectedPlanIndex ? 'text-black' : 'text-black/50'
              )}
              onClick={() => onPlanChange(index)}
            >
              <div className="w-[78%] pr-2 flex items-center justify-between">
                <span className="font-semibold truncate pr-2">
                  {slide.clientPlan.title}
                </span>
                {slide.clientPlan.current_price &&
                  slide.clientPlan.price !== slide.clientPlan.current_price && (
                    <span
                      className={twMerge(
                        'px-2.5 text-white text-[10px] font-semibold rounded-full leading-4',

                        index === selectedPlanIndex ? 'bg-black' : 'bg-black/50'
                      )}
                    >
                      offer
                    </span>
                  )}
              </div>

              <span className="italic text-xs whitespace-nowrap">
                {currencyRounded(
                  Number(
                    slide.clientPlan.current_price || slide.clientPlan.price
                  )
                )}{' '}
                monthly
              </span>
            </button>
          ))}
        </div>
      </div>
      <br />
      <br />
      {window.clientConfig.familyBannerDiscount}
      <ProgressLinks
        hideBackButton={hideBackButton}
        backTo={ROUTES.Home}
        isModalView={progressLinksModalView}
      />
    </form>
  );
}
