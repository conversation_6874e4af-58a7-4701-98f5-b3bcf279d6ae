import { useNavigate, useRouterState } from '@tanstack/react-router';
import clsx from 'clsx';
import { SVGProps, useEffect, useMemo, useRef, useState } from 'react';
import { HomeIcon, SettingsIcon } from 'src/assets/icons';
import { AwardIcon } from 'src/assets/icons/Award';
import { ROUTES } from 'src/config/routes';
import Draggable from 'react-draggable';
import { twMerge } from 'tailwind-merge';
import usePerks from 'src/hooks/usePerks';

const NAV_PADDING = 8;
const NAV_ELEMENT_BASE_WIDTH = 110;
const HOME_STOP_POINT = 10;
const PERKS_STOP_POINT = 88;
const MANAGE_STOP_POINT_WITH_PERKS = 166;
const MANAGE_STOP_POINT_NO_PERKS = 92;
const BIGGER_BUTTON_DIFF = 14;

type Link = {
  key: string;
  title: string;
  icon: (props: SVGProps<SVGSVGElement>) => JSX.Element;
  link: string;
  indicatorWidth: number;
  stopPoint: number;
};

const getActiveLinkIndex = (links: Link[], pathname: string) => {
  return links.findIndex((item) => pathname.includes(item.link));
};

const Navigation = () => {
  const navigate = useNavigate();
  const { location } = useRouterState();
  const { perks } = usePerks();

  const wrapperRef = useRef<HTMLDivElement>(null);

  const pathname = location.pathname;
  const wrapperWidth = wrapperRef.current?.offsetWidth || 0;

  const links = useMemo(() => {
    const withPerks = perks.length > 0;
    const initLinks: Link[] = [
      {
        key: 'home',
        title: 'Home',
        icon: HomeIcon,
        link: ROUTES.Dashboard,
        indicatorWidth: NAV_ELEMENT_BASE_WIDTH,
        stopPoint: HOME_STOP_POINT
      }
    ];

    if (withPerks) {
      initLinks.push({
        key: 'perks',
        title: 'Perks',
        icon: AwardIcon,
        link: ROUTES.Perks,
        indicatorWidth: NAV_ELEMENT_BASE_WIDTH,
        stopPoint: PERKS_STOP_POINT
      });
    }

    initLinks.push({
      key: 'manage',
      title: 'Manage',
      icon: SettingsIcon,
      link: ROUTES.Settings,
      indicatorWidth: NAV_ELEMENT_BASE_WIDTH + BIGGER_BUTTON_DIFF,
      stopPoint: withPerks
        ? MANAGE_STOP_POINT_WITH_PERKS
        : MANAGE_STOP_POINT_NO_PERKS
    });

    return initLinks;
  }, [perks.length]);

  const [activeLinkIndex, setActiveLinkIndex] = useState(
    getActiveLinkIndex(links, pathname)
  );
  const [withTransition, setWithTransition] = useState(false);

  const xPosition = links[activeLinkIndex]?.stopPoint;
  const indicatorWidth = links[activeLinkIndex]?.indicatorWidth;

  const setNewActiveLink = (index: number) => {
    setWithTransition(true);
    setActiveLinkIndex(index);
    navigate({ to: links[index].link });
  };

  const handleDrag = () => {
    setWithTransition(false);
  };

  const snapToNearest = (_: unknown, data: { x: number }) => {
    const xPosition = data.x;

    setWithTransition(true);

    const distances = links.map((link, index) => ({
      distance: Math.abs(link.stopPoint - xPosition) / 2,
      index
    }));

    const nearest = distances.reduce((prev, curr) =>
      prev.distance < curr.distance ? prev : curr
    );
    const nearestLinkIndex = nearest.index;

    setNewActiveLink(nearestLinkIndex);
  };

  const selectNavLink = (index: number) => () => {
    setNewActiveLink(index);
  };

  useEffect(() => {
    const activeLink = getActiveLinkIndex(links, pathname);
    setActiveLinkIndex(activeLink);
  }, [pathname, links]);

  return (
    <div
      className="nav fixed bottom-10 rounded-full h-16 left-1/2 -translate-x-1/2 z-10"
      ref={wrapperRef}
    >
      <div className="relative h-full">
        <div className="px-8 flex items-center gap-10 h-full">
          {links.map(({ title, icon: Icon, key }, index) => (
            <button
              key={key}
              className={clsx(
                'flex items-center gap-2 text-white h-full font-semibold',
                activeLinkIndex !== index && 'flex-col gap-1 justify-center'
              )}
              onClick={selectNavLink(index)}
            >
              <Icon color="white" />

              <span
                className={clsx(
                  activeLinkIndex === index ? 'text-sm' : 'text-xs'
                )}
              >
                {title}
              </span>
            </button>
          ))}
        </div>
        <Draggable
          axis="x"
          bounds={{
            left: NAV_PADDING,
            right: wrapperWidth - indicatorWidth - NAV_PADDING
          }}
          position={{ x: xPosition, y: 0 }}
          onDrag={handleDrag}
          onStop={snapToNearest}
        >
          <div
            className={twMerge(
              'indicator z-20 backdrop-invert backdrop-contrast-100 rounded-full top-2.5',
              withTransition && 'transition-all duration-300'
            )}
            style={{
              width: indicatorWidth
            }}
          ></div>
        </Draggable>
      </div>
    </div>
  );
};

export default Navigation;
