import { convertGb, getBgIndex, phoneFormat } from 'src/helpers';
import { SubscriberPlan } from 'src/types/subscriber';
import clsx from 'clsx';
import PlanStatus from './common/PlanStatus';
import Logo from './common/Logo';
import { twMerge } from 'tailwind-merge';

const isPlainCard = window.clientConfig.plainDashboardCard;

interface Props {
  phoneNumber?: string | null;
  plan: SubscriberPlan;
}

const helperText = (plan: SubscriberPlan) => {
  const {
    sim_activation_status: simStatus,
    subscription_status: subscriptionStatus
  } = plan;

  const isSimActivationPending = simStatus === 'pending-activation';

  const isSubscriptionPending = subscriptionStatus === 'inactive';
  const isSimNotAssigned = simStatus === 'not-assigned';

  if (isSimActivationPending) {
    return <PlanStatus>Your activation request is in progress</PlanStatus>;
  }

  if (isSubscriptionPending || isSimNotAssigned) {
    return (
      <PlanStatus>You should receive your SIM within 3 working days</PlanStatus>
    );
  }

  return null;
};

const PlanCard = ({ plan, phoneNumber }: Props) => {
  const {
    id,
    bg,
    data,
    title,
    plan_id,
    sim_activation_status: simStatus,
    subscription_status: subscriptionStatus,
    sim_type,
    phone_number: planPhoneNumber
  } = plan;

  const bgIndex = getBgIndex(id, bg);
  const { total: usedGb, left: leftGb, is_unlimited: isUnlimited } = data || {};

  const monthLimit = usedGb + (leftGb || 0);
  const used = convertGb(usedGb);
  const left = convertGb(leftGb);

  const isSimActive = simStatus === 'active';
  const isSimCancelled = subscriptionStatus === 'cancelled';
  const simType = sim_type === 'esim' ? 'eSIM' : 'SIM';

  const isSubscriptionActive = subscriptionStatus === 'active';

  const formattedPhoneNumber =
    planPhoneNumber || phoneNumber
      ? phoneFormat(planPhoneNumber || phoneNumber || '')
      : '';

  const showData = isSimActive && isSubscriptionActive;

  return (
    <div>
      <div className="plan-card-wrapper relative w-full m-auto mb-4">
        <div
          className={clsx(
            isPlainCard ? 'plan-card-detailed' : 'plan-card shadow-md',
            'absolute flex flex-col items-center size-full text-lg py-4 px-6 text-white',
            isSimCancelled && 'plan-card--cancelled',
            !plan_id && 'not-active'
          )}
          style={{
            ...(plan_id && {
              backgroundImage: `url(/background/card-${bgIndex}.png)`
            })
          }}
        >
          <div
            className={twMerge(
              'w-full flex justify-between text-black/20 font-bold text-xl card-top'
            )}
          >
            {isPlainCard ? (
              <>
                <div className="plan-card-logo">
                  <Logo disableRedirect />
                </div>
                <span>{simType}</span>
              </>
            ) : (
              <>
                <span>{simType}</span>
                <span className="network">5G</span>
              </>
            )}
          </div>
          <div className={twMerge('plan-card-content', isPlainCard && 'plain')}>
            <h3 className="plan-card-title text-2xl font-bold card-text">
              {title}
            </h3>
            {isSimCancelled && (
              <p className="text-xs max-w-[80%] text-black m-auto mt-2">
                You've cancelled the plan. It will terminate soon
              </p>
            )}
          </div>

          <div className="absolute origin-center right-7 top-1/2 -translate-y-1/2 translate-x-1/2 rotate-90">
            <span className="inline-block text-[11px] plan-number tracking-[3px]">
              {formattedPhoneNumber}
            </span>
          </div>

          {helperText(plan)}

          {showData &&
            (isUnlimited ? (
              <div className="font-bold w-full text-xs">
                <span className="opacity-90">{used} data used</span>
              </div>
            ) : (
              <div className="w-full">
                <div className="flex justify-between font-bold space-x-2 mb-2 text-xs">
                  <span className="card-used opacity-80">{used} used</span>
                  <span className="text-black opacity-20">{left} left</span>
                </div>
                <div className="relative h-2">
                  <span className="block size-full rounded-xl bg-black opacity-10" />
                  <span
                    className="h-full rounded-xl absolute top-0 left-0 data-consumed"
                    style={{
                      width: `${(Number(usedGb) * 100) / Number(monthLimit)}%`
                    }}
                  />
                </div>
              </div>
            ))}
        </div>
      </div>
    </div>
  );
};

export default PlanCard;
