import RadioButtons from 'src/components/card-details/RadioButtons';
import BillingPlanCard from 'src/components/card-details/BillingPlanCard';
import { defaultSimName, epochToDate, getBgIndex } from 'src/helpers';
import CardUpdate from 'src/components/plan-upgrade/CardUpdate';
import useSubscription from 'src/hooks/useSubscription';
import { useState } from 'react';
import { SubscriberPlan } from 'src/types/subscriber';

enum BillingType {
  CONSOLIDATED = 'consolidated',
  PER_PLAN = 'per-plan'
}

export default function MultiPlanBilling() {
  const { subscriber } = useSubscription();

  const [billingType, setBillingType] = useState(BillingType.CONSOLIDATED);
  const [selectedPlan, setSelectedPlan] = useState<SubscriberPlan | null>(null);

  const selectedPlanIndex = subscriber?.plans.findIndex(
    (plan) => plan.id === selectedPlan?.id
  );

  const handleBillingTypeChange = (type: BillingType) => {
    setBillingType(type);
    setSelectedPlan(null);
  };

  return (
    <div>
      <RadioButtons
        options={[
          { label: 'Consolidated billing', value: BillingType.CONSOLIDATED },
          { label: 'Per-plan billing', value: BillingType.PER_PLAN }
        ]}
        onChange={handleBillingTypeChange}
        fullWidth
      />
      {billingType === BillingType.CONSOLIDATED && (
        <div className="mt-4 pb-6">
          <CardUpdate />
        </div>
      )}

      {billingType === BillingType.PER_PLAN &&
        (selectedPlan ? (
          <div className="mt-4 pb-6">
            <h4 className="mt-6 mb-4 text-lg font-semibold">
              {selectedPlan.user_subscription_name ||
                `${defaultSimName} ${selectedPlanIndex! + 1}`}
            </h4>
            <CardUpdate subscriptionId={selectedPlan.id} />
          </div>
        ) : (
          <div className="mt-4 pb-6 space-y-3">
            {subscriber?.plans.map((plan, index) => {
              const planTitle = `${plan.title} - ${plan.user_subscription_name || defaultSimName + (index + 1)}`;
              return (
                <BillingPlanCard
                  key={plan.id}
                  title={planTitle || 'Plan ' + (index + 1)}
                  bgIndex={getBgIndex(plan.id, plan.bg)}
                  billingDate={epochToDate(plan.next_bill_date_epoch)}
                  onClick={() => setSelectedPlan(plan)}
                />
              );
            })}
          </div>
        ))}
    </div>
  );
}
