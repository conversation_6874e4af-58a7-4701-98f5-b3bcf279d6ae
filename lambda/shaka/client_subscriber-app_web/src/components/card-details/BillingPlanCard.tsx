import Button from '../common/Button';

interface BillingPlanCardProps {
  bgIndex?: number;
  title: string;
  billingDate?: string;
  cardEnding?: string;
  onClick: () => void;
}

export default function BillingPlanCard({ bgIndex, title, billingDate, cardEnding, onClick }: BillingPlanCardProps) {
  return (
    <div className="max-w-sm w-full bg-white rounded-[32px] p-4 space-y-4">
      <div
        className="relative rounded-3xl h-32 overflow-hidden bg-cover"
        style={{
          ...(bgIndex && {
            backgroundImage: `url(/background/card-${bgIndex}.png)`
          })
        }}
      >
        <div className="absolute inset-0 flex items-center px-5">
          <span className="text-white text-lg font-bold">{title}</span>
        </div>
      </div>

      <div className="flex justify-between items-center text-xs text-gray-700">
        <span>Next billing date: {billingDate}</span>
        {cardEnding && <span>Card ending {cardEnding}</span>}
      </div>

      <Button color="black" onClick={onClick} size="small" fullWidth>
        Update billing details
      </Button>
    </div>
  );
}
