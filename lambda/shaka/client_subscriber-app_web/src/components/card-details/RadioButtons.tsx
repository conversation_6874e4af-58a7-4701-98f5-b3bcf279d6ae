import { useState, useRef, useEffect } from 'react';
import { Option } from 'src/types/common';
import { twMerge } from 'tailwind-merge';

interface SegmentedControlProps<T> {
  options: Option[];
  defaultIndex?: number;
  onChange?: (value: T) => void;
  fullWidth?: boolean;
}

const SegmentedControl = <T,>({
  options,
  defaultIndex = 0,
  onChange,
  fullWidth
}: SegmentedControlProps<T>) => {
  const [activeIndex, setActiveIndex] = useState(defaultIndex);
  const [indicatorStyle, setIndicatorStyle] = useState({ left: 0, width: 0 });
  const optionRefs = useRef<(HTMLDivElement | null)[]>([]);

  useEffect(() => {
    const activeEl = optionRefs.current[activeIndex];
    if (activeEl) {
      setIndicatorStyle({
        left: activeEl.offsetLeft,
        width: activeEl.offsetWidth
      });
    }
  }, [activeIndex, options]);

  const handleClick = (index: number) => {
    setActiveIndex(index);
    if (onChange) {
      onChange(options[index].value as T);
    }
  };

  return (
    <div
      className={twMerge(
        'relative inline-flex bg-white rounded-full overflow-hidden',
        fullWidth ? 'w-full' : 'w-fit'
      )}
    >
      {options.map((option, index) => (
        <div
          key={option.value}
          ref={(el) => (optionRefs.current[index] = el)}
          onClick={() => handleClick(index)}
          className={twMerge(
            'relative z-10 px-2 py-3 text-sm cursor-pointer whitespace-nowrap transition-colors duration-300 font-semibold',
            fullWidth && 'w-1/2 text-center',
            index === activeIndex ? 'text-white' : 'text-black'
          )}
        >
          {option.label}
        </div>
      ))}

      <div
        className="absolute top-0 bottom-0 bg-black transition-all duration-300 rounded-full"
        style={{
          left: indicatorStyle.left,
          width: fullWidth ? '50%' : indicatorStyle.width
        }}
      />
    </div>
  );
};

export default SegmentedControl;
