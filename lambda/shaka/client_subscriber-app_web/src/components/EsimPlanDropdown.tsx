import { Popover } from '@headlessui/react';
import useSubscription from 'src/hooks/useSubscription';
import { twMerge } from 'tailwind-merge';
import { defaultRoamingSimName, defaultSimName } from 'src/helpers';
import { SubscriberPlanSimItem } from 'src/context/SubscriptionContext.tsx';

function getEsimPlanDropdownOption(plan: SubscriberPlanSimItem) {
  let label;

  switch (plan.type) {
    case 'plan':
      label =
        plan.data.user_subscription_name ||
        `${defaultSimName} ${plan.initialIndex + 1}`;
      break;
    case 'sim':
      label =
        plan.data.custom_name ||
        `${defaultRoamingSimName} ${plan.initialIndex + 1}`;
      break;
    default:
      label = '';
      break;
  }
  return {
    id: plan.id,
    label,
    value: plan.id,
    type: plan.type,
    data: plan.data,
    initialIndex: plan.initialIndex
  };
}

export default function EsimPlanDropdown({
  selectedPlan,
  onChange
}: {
  selectedPlan: SubscriberPlanSimItem | null;
  onChange: (plan: SubscriberPlanSimItem) => void;
}) {
  const { subscriberPlanSimList } = useSubscription();

  const options = subscriberPlanSimList.map((plan) =>
    getEsimPlanDropdownOption(plan)
  );

  const selectedOption = selectedPlan
    ? options.find((option) => option.id === selectedPlan.id)
    : options[0];

  return (
    <Popover className="relative z-50">
      {({ open }) => (
        <>
          {open && (
            <div className="background fixed bg-black/30 w-screen h-screen left-0 top-0" />
          )}

          <Popover.Button
            className={twMerge(
              'rounded-full bg-white px-4 py-1 text-xs focus:outline-none relative'
            )}
          >
            {selectedOption?.label || 'Select Plan'}
          </Popover.Button>

          <Popover.Panel className="absolute z-100 left-1/2 -translate-x-1/2 top-[32px]">
            {({ close }) => (
              <div className="flex flex-col gap-2 p-4 bg-white rounded-3xl min-w-[260px] shadow-2xl">
                {options?.map((option) => (
                  <button
                    key={option.id}
                    onClick={() => {
                      const originalItem = subscriberPlanSimList.find(
                        (item) => item.id === option.id
                      );
                      if (originalItem) {
                        onChange(originalItem);
                      }
                      close();
                    }}
                    className={twMerge(
                      'text-sm text-left bg-[#F3F3F3] px-3.5 py-3.5 rounded-2xl border',
                      option.id === selectedPlan?.id
                        ? 'border-black'
                        : 'border-transparent'
                    )}
                  >
                    {option.label}
                  </button>
                ))}
              </div>
            )}
          </Popover.Panel>
        </>
      )}
    </Popover>
  );
}
