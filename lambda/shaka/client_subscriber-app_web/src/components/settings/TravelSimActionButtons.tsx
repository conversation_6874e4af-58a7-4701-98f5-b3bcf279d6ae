import { travelSimLinks } from 'src/config/settings-buttons';
import SubscriptionActionLink from './SubscriptionActionLink';
import {
  removeRoamingZone,
  setRoamingZone
} from 'src/config/localStorageActions';

export default function TravelSimActionButtons({
  disabled,
  roamingZone
}: {
  disabled?: boolean;
  roamingZone?: string;
}) {
  return (
    <div className="flex justify-center gap-4 mt-6">
      {travelSimLinks.map(({ to, label, search, saveRoamingZone }) => (
        <SubscriptionActionLink
          to={to}
          search={search}
          disabled={disabled}
          key={label}
          className="px-1.5"
          onClick={() => {
            if (saveRoamingZone && roamingZone) {
              setRoamingZone(roamingZone);
            } else {
              removeRoamingZone();
            }
          }}
        >
          {label}
        </SubscriptionActionLink>
      ))}
    </div>
  );
}
