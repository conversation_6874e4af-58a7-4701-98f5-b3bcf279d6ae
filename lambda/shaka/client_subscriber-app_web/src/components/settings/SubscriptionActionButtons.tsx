import { ROUTES } from 'src/config/routes';
import { subscriptionLinks } from 'src/config/settings-buttons';
import SubscriptionActionLink from './SubscriptionActionLink';
import { useMemo } from 'react';

export default function SubscriptionActionButtons({
  isPlanChanging,
  isBoltOnsDisabled
}: {
  isPlanChanging: boolean;
  isBoltOnsDisabled: boolean;
}) {
  const subscriptionActionButtons = useMemo(() => {
    return subscriptionLinks.map(({ to, label, disabled, search, id }) => (
      <SubscriptionActionLink
        to={to}
        search={search}
        disabled={
          disabled ||
          (to === ROUTES.Settings && isPlanChanging) ||
          (id === 'bolt-ons' && isBoltOnsDisabled)
        }
        key={label}
      >
        {label}
      </SubscriptionActionLink>
    ));
  }, [isBoltOnsDisabled, isPlanChanging]);

  return (
    <div className="flex justify-center gap-4 mt-6">
      {subscriptionActionButtons}
    </div>
  );
}
