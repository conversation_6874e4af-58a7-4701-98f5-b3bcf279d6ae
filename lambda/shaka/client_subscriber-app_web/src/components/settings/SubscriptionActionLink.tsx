import { Link, LinkProps } from '@tanstack/react-router';
import { PropsWithChildren } from 'react';
import { twMerge } from 'tailwind-merge';

export const subscriptionActionLinkStyles =
  'flex bg-white px-2 rounded-[100%] text-center text-xs font-bold size-[76px] items-center justify-center hover:bg-white/50';

interface Props {
  disabled?: boolean;
  className?: string;
}

const SubscriptionActionLink = ({
  children,
  disabled,
  className,
  ...props
}: PropsWithChildren<Props> & LinkProps) => (
  <Link
    className={twMerge(
      subscriptionActionLinkStyles,
      className,
      disabled && 'pointer-events-none opacity-50'
    )}
    {...props}
  >
    {children}
  </Link>
);

export default SubscriptionActionLink;
