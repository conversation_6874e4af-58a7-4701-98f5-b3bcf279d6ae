import { create } from 'zustand';
import { Perk } from 'src/types/perks';

type State = { perks: Perk[]; balance: number; isLoaded: boolean };

const defaultState: State = {
  perks: [],
  balance: 0,
  isLoaded: false
};

const usePerksStore = create<State>(() => defaultState);

const selectorPerks = (state: State) => state.perks;
const selectorPointsBalance = (state: State) => state.balance;
const selectorPerksLoaded = (state: State) => state.isLoaded;

const selectorPerkById = (id?: string) => (state: State) =>
  state.perks.find((perk) => perk.id?.toString() == id);

const setPerks = (data: { perks: Perk[]; balance: number }) =>
  usePerksStore.setState(() => ({ ...data, isLoaded: true }));

const resetPerks = () => usePerksStore.setState(defaultState);

export {
  usePerksStore,
  selectorPerks,
  selectorPerkById,
  selectorPerksLoaded,
  selectorPointsBalance,
  setPerks,
  resetPerks
};
