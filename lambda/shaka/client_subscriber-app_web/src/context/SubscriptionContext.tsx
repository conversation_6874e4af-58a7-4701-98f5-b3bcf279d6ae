import {
  useState,
  useEffect,
  useCallback,
  createContext,
  useContext,
  useRef,
  useMemo
} from 'react';
import {
  PlanChangeStatus,
  PlanChangeType,
  Subscriber,
  SubscriberPlan
} from 'src/types/subscriber';
import { fetchSubscriber as getSubscriber } from 'src/api/data';
import { AuthContext } from './AuthContext';
import { ROUTES } from 'src/config/routes';
import { getCurrentPlanIndex } from 'src/config/localStorageActions';
import { setCurrentPlanIndex as saveCurrentPlanIndexToLS } from 'src/config/localStorageActions';
import { RoamingSim } from 'src/types/roaming-sim';

const pendingPhoneStatuses = [
  'inactive',
  'pending-activation',
  'requires-activation'
];

export type SubscriberPlanSimItem =
  | {
      id: number;
      initialIndex: number;
      type: 'plan';
      data: SubscriberPlan;
    }
  | {
      id: number;
      initialIndex: number;
      type: 'sim';
      data: RoamingSim;
    };

export type SubscriptionContextType = {
  subscriber: Subscriber | null;
  isLoading: boolean;
  isSubscriptionLoaded: boolean;
  fetchSubscriber: () => void;
  setSubscriber: (newSubscriber: Subscriber | null) => void;
  currentPlanIndex: number;
  setCurrentPlanIndex: (index: number) => void;
  currentPlan: SubscriberPlan;
  subscriberPlanSimList: SubscriberPlanSimItem[];
  currentRoamingSim: RoamingSim | null;
  activeAndRecentlyExpiredRoamingSims: RoamingSim[];
  hasMultiplePlans: boolean;
  hasEsim: boolean;
  mightHaveEsim?: boolean;
  roamingEsimPlans: RoamingSim[];
  setCurrentPlanByCorrelationId: (correlationId: string) => void;
};

const checkIfSubscriberDataFilled = (subscriber: Subscriber | null) => {
  const {
    sim_activation_status: simStatus = '',
    subscription_status: subscriptionStatus,
    latest_plan_change: latestPlanChange
  } = subscriber?.plans?.[0] || {};

  const isPlanUpgradeInProgress =
    latestPlanChange &&
    latestPlanChange.change_type !== PlanChangeType.CANCEL_CHANGE &&
    latestPlanChange.status === PlanChangeStatus.IN_PROGRESS;

  const isSubscriberDataFilled = Boolean(
    subscriber &&
      subscriber?.address &&
      subscriber?.phone_number &&
      subscriber?.plans &&
      subscriptionStatus !== 'inactive' &&
      !pendingPhoneStatuses.includes(simStatus) &&
      !isPlanUpgradeInProgress
  );

  return isSubscriberDataFilled;
};

export const SubscriptionContext = createContext<SubscriptionContextType>({
  subscriber: null,
  isLoading: true,
  isSubscriptionLoaded: false,
  fetchSubscriber: () => {},
  setSubscriber: () => {},
  currentPlan: {} as SubscriberPlan,
  currentPlanIndex: 0,
  setCurrentPlanIndex: () => {},
  subscriberPlanSimList: [],
  currentRoamingSim: {} as RoamingSim,
  activeAndRecentlyExpiredRoamingSims: [],
  hasMultiplePlans: false,
  hasEsim: false,
  roamingEsimPlans: [],
  setCurrentPlanByCorrelationId: () => {}
});

const formattedPlanIndexFromLS = () => {
  const indexString = getCurrentPlanIndex();
  const index = indexString ? parseInt(indexString, 10) : 0;
  return index;
};

const SubscriptionProvider = ({ children }: React.PropsWithChildren) => {
  const { isAuthenticated } = useContext(AuthContext);

  const [subscriber, setSubscriber] = useState<Subscriber | null>(null);
  const [isPolling, setIsPolling] = useState(true);
  const [isSubscriptionLoaded, setIsSubscriptionLoaded] = useState(false);
  const [currentPlanIndex, setCurrentPlanIndex] = useState<number>(
    formattedPlanIndexFromLS()
  );
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const activeAndRecentlyExpiredRoamingSims = useMemo(() => {
    return (
      subscriber?.roaming_esims?.filter((roamingSim) => {
        const expiresDate = Date.parse(roamingSim.expires);
        const oneWeekAgo = Date.now() - 7 * 24 * 60 * 60 * 1000;

        return expiresDate >= oneWeekAgo || roamingSim.status === 'active';
      }) || []
    );
  }, [subscriber?.roaming_esims]);

  const fetchSubscriber = useCallback(async () => {
    let subscriberData = subscriber;
    setIsSubscriptionLoaded(false);

    try {
      subscriberData = await getSubscriber();
    } catch (e) {
      console.error('Error fetching subscriber data:', e);
    }

    setSubscriber(subscriberData);
    setIsSubscriptionLoaded(true);

    return subscriberData;
  }, []);

  const updateSubscriber = useCallback((newSubscriber: Subscriber | null) => {
    setSubscriber(newSubscriber);
    setIsSubscriptionLoaded(true);
  }, []);

  const pollSubscriber = useCallback(async () => {
    const pathname = window.location.pathname;
    const isSubscriberDataFilled = checkIfSubscriberDataFilled(subscriber);

    if (
      !isSubscriberDataFilled &&
      !pathname.includes(ROUTES.EmailConfirmation)
    ) {
      try {
        const newSubscriberData = await fetchSubscriber();

        const isNewSubscriberDataFilled =
          checkIfSubscriberDataFilled(newSubscriberData);

        if (isNewSubscriberDataFilled) {
          setIsPolling(false);
          return;
        }
      } catch (e) {
        console.error('Error polling subscriber data:', e);
      }
    }
  }, [fetchSubscriber, subscriber]);

  const subscriberPlanSimList = useMemo(() => {
    const planData =
      subscriber?.plans.map((plan, index) => {
        return {
          id: plan.id,
          type: 'plan',
          initialIndex: index,
          data: plan
        } as SubscriberPlanSimItem;
      }) || [];
    const simData =
      activeAndRecentlyExpiredRoamingSims?.map((sim, index) => {
        return {
          id: sim.id,
          type: 'sim',
          initialIndex: index,
          data: sim
        } as SubscriberPlanSimItem;
      }) || [];

    return [...planData, ...simData];
  }, [subscriber?.plans, activeAndRecentlyExpiredRoamingSims]);

  const saveCurrentPlanIndex = (index: number) => {
    if (subscriberPlanSimList[index]) {
      setCurrentPlanIndex(index);
      saveCurrentPlanIndexToLS(index);
    }
  };

  const setCurrentPlanByCorrelationId = (correlationId: string) => {
    const index = subscriberPlanSimList.findIndex(
      (item) => (item.data as SubscriberPlan).correlation_id === correlationId
    );

    if (index !== -1) {
      saveCurrentPlanIndex(index);
    }
  };

  useEffect(() => {
    if (!isAuthenticated()) return;

    if (!isPolling) return;

    intervalRef.current = setInterval(
      pollSubscriber,
      subscriber ? 30 * 1000 : 5 * 1000
    );

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isAuthenticated, isPolling, pollSubscriber]);

  useEffect(() => {
    if (isAuthenticated()) {
      // initial fetch
      fetchSubscriber();
    }
  }, [isAuthenticated, fetchSubscriber]);

  return (
    <SubscriptionContext.Provider
      value={{
        subscriber,
        isLoading: !subscriber,
        isSubscriptionLoaded,
        fetchSubscriber,
        setSubscriber: updateSubscriber,
        currentPlan:
          subscriber?.plans?.[currentPlanIndex] || ({} as SubscriberPlan),
        setCurrentPlanIndex: saveCurrentPlanIndex,
        setCurrentPlanByCorrelationId,
        currentPlanIndex,
        subscriberPlanSimList,
        currentRoamingSim:
          subscriber?.roaming_esims?.[
            currentPlanIndex - subscriber?.plans?.length
          ] || ({} as RoamingSim),
        activeAndRecentlyExpiredRoamingSims,
        hasMultiplePlans: (subscriber?.plans?.length || 1) > 1,
        roamingEsimPlans: subscriber?.roaming_esims || [],
        hasEsim:
          subscriber?.plans
            ?.filter((plan) => plan.sim_serial_fragment != null)
            .some((plan) => plan.sim_type === 'esim') ||
            activeAndRecentlyExpiredRoamingSims.length > 0,
        mightHaveEsim:
          subscriber?.plans?.some((plan) => plan.sim_type === 'esim') ||
          activeAndRecentlyExpiredRoamingSims.length > 0
      }}
    >
      {children}
    </SubscriptionContext.Provider>
  );
};

export default SubscriptionProvider;
