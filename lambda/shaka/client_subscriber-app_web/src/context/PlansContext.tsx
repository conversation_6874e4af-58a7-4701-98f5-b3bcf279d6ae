import { PropsWithChildren, createContext, useState } from 'react';
import { ClientPlanDetails } from 'src/types/slide';

export const PlansContext = createContext<{
  plans: ClientPlanDetails[];
  setPlans: (plans: ClientPlanDetails[]) => void;
}>({ plans: [], setPlans: () => null });

export const PlansProvider = ({ children }: PropsWithChildren) => {
  const [plans, setPlans] = useState<ClientPlanDetails[]>([]);

  return (
    <PlansContext.Provider
      value={{
        plans,
        setPlans
      }}
    >
      {children}
    </PlansContext.Provider>
  );
};
