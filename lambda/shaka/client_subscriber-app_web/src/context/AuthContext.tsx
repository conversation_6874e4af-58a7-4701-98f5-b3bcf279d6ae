import React, { createContext, useEffect, useState } from 'react';
import { jwtDecode } from 'jwt-decode';
import { User } from '../types/user';
import { LoginData } from 'src/types/login';
import {
  LocalKey,
  clearStorage,
  setSubscriberSession
} from 'src/config/localStorageActions';
import { getExpiresAt } from 'src/helpers';
import { resetPerks } from 'src/store/perks';
import { isOpenId } from 'src/config/env-vars';

export type AuthContextType = {
  isAuthenticated: () => boolean;
  login: (data: LoginData) => void;
  logout: () => void;
  user: User | null;
};

export const AuthContext = createContext<AuthContextType>({
  isAuthenticated: () => false,
  login: () => {},
  logout: () => {},
  user: null
});

const AuthProvider = ({ children }: React.PropsWithChildren) => {
  const [user, setUser] = useState<User | null>(null);

  const isAuthenticated = () => {
    return Boolean(localStorage.getItem(LocalKey.ID_TOKEN));
  };

  const validateAuthTokens = ({
    id_token,
    access_token,
    refresh_token
  }: Record<string, string>): {
    id_token: string;
    access_token: string;
    refresh_token: string;
    expires_at: string;
  } | null => {
    const userData = jwtDecode(id_token);

    if ('email' in userData && 'cognito:username' in userData) {
      const expiresAt = getExpiresAt(access_token);

      setUser({
        email: userData.email as string
      });

      return { id_token, access_token, refresh_token, expires_at: expiresAt };
    }

    if (isOpenId) {
      const expiresAt = getExpiresAt(access_token);
      return { id_token, access_token, refresh_token, expires_at: expiresAt };
    }

    return null;
  };

  const login = ({
    access_token,
    id_token,
    refresh_token
  }: {
    access_token: string;
    id_token: string;
    refresh_token: string;
  }) => {
    const validatedData = validateAuthTokens({
      id_token,
      access_token,
      refresh_token
    });

    if (!validatedData) {
      logout();
      return;
    }

    setSubscriberSession(
      validatedData.id_token,
      validatedData.access_token,
      validatedData.expires_at
    );
    localStorage.setItem(LocalKey.REFRESH_TOKEN, validatedData.refresh_token);
  };

  const logout = () => {
    clearStorage();

    setUser(null);

    // clear all store data on logout
    resetPerks();
  };

  useEffect(() => {
    if (!isAuthenticated() && !isOpenId) {
      logout();
    }
  }, []);

  return (
    <AuthContext.Provider value={{ isAuthenticated, login, logout, user }}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthProvider;
