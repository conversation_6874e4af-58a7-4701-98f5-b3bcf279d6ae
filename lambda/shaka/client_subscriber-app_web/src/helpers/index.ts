import { jwtDecode } from 'jwt-decode';
import { DateTime } from 'luxon';
import { countries } from 'src/config/countries';
import { regions } from 'src/config/regions';

export const formatNumber = (
  value: number,
  options: Intl.NumberFormatOptions = {}
) => {
  const formatter = new Intl.NumberFormat('en-US', options);

  return formatter.format(value);
};

export const currencyFormat = (price: number) =>
  formatNumber(price, {
    style: 'currency',
    currency: 'GBP'
  });

export const currencyRounded = (price: number) =>
  currencyFormat(price).replace('.00', '');

export const phoneFormat = (phoneNumber: string) =>
  phoneNumber
    .replace(/(\d{2})(\d{4})(\d{3})(\d{3})/, '$1 $2$3$4')
    .replace('+', '');

export const convertGb = (gb: number | string | null) => {
  if (!gb || (gb as string) === 'unlimited') {
    return gb;
  }

  const number = Number(gb);

  if (number >= 0.1) {
    return number.toFixed(2) + ' GB';
  }

  if (number >= 0.0001) {
    const mb = number * 1024;

    return mb.toFixed(2) + ' MB';
  }

  if (number >= 0.0000001) {
    const kb = number * 1024 * 1024;
    return kb.toFixed(2) + ' KB';
  }

  const bytes = number * 1024 * 1024 * 1024;
  return bytes.toFixed(2) + ' B';
};

export const DEFAULT_EXPIRY_IN_SECONDS = 3600;

export const getExpiresAt = (token: string) => {
  const userData = jwtDecode(token);
  const expirySeconds = userData.exp || new Date().getTime() / 1000;

  return (expirySeconds * 1000).toString();
};

export const getSmsVoiceLabel = (sms: string, voice: string) => {
  const isAllUnlimited = sms === 'unlimited' && voice === 'unlimited';

  if (isAllUnlimited) {
    return 'Unlimited texts & calls';
  }

  const smsLabel = sms === 'unlimited' ? 'Unlimited SMS' : `${sms} SMS`;
  const voiceLabel = voice === 'unlimited' ? 'Unlimited mins' : `${voice} mins`;

  return smsLabel + ' & ' + voiceLabel;
};

export const getRoamingLabel = (data?: string) => {
  if (!data) return '';

  const maxRoaming = '20';
  let roamingValue;

  if (data === 'unlimited' || Number(data) > Number(maxRoaming)) {
    roamingValue = maxRoaming;
  } else {
    roamingValue = data;
  }

  return `${roamingValue}GB EU roaming`;
};

export const getInitials = (name: string) => {
  const [firstName, lastName] = name.split(' ');

  if (!lastName) {
    return `${firstName.charAt(0)}`;
  }

  return `${firstName.charAt(0)}${lastName.charAt(0)}`;
};
export const formateToShortDateString = (date: Date | null) => {
  if (!date) return '';

  date.setMinutes(date.getMinutes() - date.getTimezoneOffset());

  return date.toISOString().substring(0, 10);
};

export const getLocaleDateString = (date: string | null) => {
  if (!date) return '';

  return new Date(date).toLocaleDateString();
};

export const epochToDate = (epoch: number) => {
  const date = new Date(epoch * 1000);
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();

  return `${day}/${month}/${year}`;
};

export const humanReadableDateFormat = (date: Date) => {
  const day = date.getDate();
  const month = date.toLocaleString('default', { month: 'long' });
  const year = date.getFullYear();

  const dayWithSuffix = getDayWithSuffix(day);

  return `${dayWithSuffix} ${month} ${year}`;
};

function getDayWithSuffix(day: number) {
  if (day > 3 && day < 21) return `${day}th`;
  switch (day % 10) {
    case 1:
      return `${day}st`;
    case 2:
      return `${day}nd`;
    case 3:
      return `${day}rd`;
    default:
      return `${day}th`;
  }
}

export const getArticleForTitle = (title?: string) => {
  if (!title) return '';

  const firstLetter = title[0].toLowerCase();
  return ['a', 'e', 'i', 'o', 'u'].includes(firstLetter) ? 'an' : 'a';
};

export const passwordValidation = [
  {
    label: 'Minimum 8 characters',
    isValid: (password: string) => password?.match(/^.{8,}$/)
  },
  {
    label: '1 upper case',
    isValid: (password: string) => password?.match(/^(?=.*[A-Z]).{1,}$/)
  },
  {
    label: '1 special character',
    isValid: (password: string) =>
      password?.match(/^(?=.*[!@#$%^&*(),.?":{}|<>]).{1,}$/)
  },
  {
    label: '1 number',
    isValid: (password: string) => password?.match(/^(?=.*[0-9]).{1,}$/)
  }
];

export const getDateDurationFromNow = (startDate: string) => {
  const end = DateTime.fromJSDate(new Date());
  const start = DateTime.fromISO(startDate);

  const { days = 0 } = end.diff(start, ['days']).toObject();

  return Math.floor(days);
};

export const isMoreThan14Days = (dateString?: string) => {
  if (!dateString) return false;

  const givenDate = new Date(dateString);
  const currentDate = new Date();

  const differenceInMs = currentDate.getTime() - givenDate.getTime();
  const differenceInDays = differenceInMs / (1000 * 60 * 60 * 24);

  return differenceInDays > 14;
};

const backgroundImageNumber = window.clientConfig.backgroundImageNumber || 3;

export const getBgIndex = (id: number, bg?: number) => {
  const bgIndex = ((bg || id || 0) % backgroundImageNumber) + 1;

  return bgIndex;
};

export function checkIsIos11OrHigher() {
  const userAgent = navigator.userAgent || navigator.vendor;

  if (/iPad|iPhone|iPod/.test(userAgent) && /iPhone OS/.test(userAgent)) {
    const iOSVersionMatch = userAgent.match(/OS (\d+)_/);
    if (iOSVersionMatch && iOSVersionMatch.length > 1) {
      const iOSVersion = parseInt(iOSVersionMatch[1], 10);
      return iOSVersion >= 11;
    }
  }
  return false;
}

export const getCountryLabel = (countryCode: string) => {
  const country = countries.find((country) => country.value === countryCode);
  const region = regions.find((region) => region.value === countryCode);

  if (country) {
    return country.label;
  }

  if (region) {
    return region.label;
  }

  return '';
};

export const userActions = {
  moreData: 'add-data',
  newCountry: 'add-country',
  getTravelEsim: 'getTravelEsim'
} as const;

export const formatRoamingDrawerTitle = (
  userAction: string | null,
  roamingEsimLength: number
) => {
  const isFirstRoamingPlan = roamingEsimLength === 0;
  const isSubsequentRoamingPlan = roamingEsimLength > 0;

  switch (userAction) {
    case userActions.moreData:
      return 'ADD MORE DATA';
    case userActions.newCountry:
      return 'ADD NEW COUNTRY';
    case userActions.getTravelEsim:
      if (isFirstRoamingPlan) {
        return 'ADD A ROAMING eSIM';
      } else if (isSubsequentRoamingPlan) {
        return 'Add another roaming eSIM';
      }
      break;
    default:
      return 'ADD A UK SIM';
  }
};

export const defaultSimName = 'UK SIM';
export const defaultRoamingSimName = 'Global roaming eSIM';
