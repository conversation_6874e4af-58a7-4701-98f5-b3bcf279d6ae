const generateHolidaysForNextTwoYears = () => {
  const holidays = [];
  const currentYear = new Date().getFullYear();
  for (let year = currentYear; year <= currentYear + 1; year++) {
    holidays.push(
      new Date(`${year}-01-01`),
      new Date(`${year}-04-18`),
      new Date(`${year}-04-21`),
      new Date(`${year}-05-05`),
      new Date(`${year}-05-26`),
      new Date(`${year}-08-25`),
      new Date(`${year}-12-25`),
      new Date(`${year}-12-26`)
    );
  }
  return holidays;
};

export const isWeekendOrHoliday = (date: Date) => {
  const currentYear = new Date().getFullYear();
  const day = date.getDay();
  const holidays = generateHolidaysForNextTwoYears();
  const startOfTheSecondYear = new Date(currentYear + 2, 0, 1);

  return (
    day === 0 ||
    day === 6 ||
    holidays.some(
      (holiday) =>
        date.getFullYear() === holiday.getFullYear() &&
        date.getMonth() === holiday.getMonth() &&
        date.getDate() === holiday.getDate()
    ) ||
    date >= startOfTheSecondYear
  );
};

export const getTimeDifferenceFromNow = (date: Date) => {
  if (!date) {
    return '';
  }

  const now = new Date();
  const diff = new Date(date).getTime() - now.getTime();
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

  if (days > 1) {
    return `${days} days`;
  }

  if (minutes === 0) {
    return `${hours} hours`;
  }
  if (hours === 0) {
    return `${minutes} minutes`;
  }

  return `${hours} hours, ${minutes} minutes`;
};

export const getExpiresIn = (expires: string) => {
  const now = new Date();
  const expirationDate = new Date(expires);
  const timeDiff = expirationDate.getTime() - now.getTime();
  const hoursLeft = Math.floor(timeDiff / (1000 * 60 * 60));
  const daysLeft = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
  const hoursLeftInDay = hoursLeft % 24;

  if (hoursLeft < 24) {
    return hoursLeft > 0 ? `expires in ${hoursLeft} hours` : 'expired';
  }

  return `expires in ${daysLeft} ${daysLeft > 1 ? 'days' : 'day'}${hoursLeftInDay ? `, ${hoursLeftInDay} ${hoursLeftInDay > 1 ? 'hours' : 'hour'}` : ''} `;
};
