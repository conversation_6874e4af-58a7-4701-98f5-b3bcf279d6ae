const InfoIcon = () => (
  <svg
    width="24"
    height="23"
    viewBox="0 0 24 23"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_3538_1692)">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M12.1117 22.6667C18.3709 22.6667 23.445 17.5926 23.445 11.3333C23.445 5.07411 18.3709 0 12.1117 0C5.85243 0 0.77832 5.07411 0.77832 11.3333C0.77832 17.5926 5.85243 22.6667 12.1117 22.6667ZM9.68308 15.1786C9.12422 15.1786 8.67118 15.6316 8.67118 16.1905C8.67118 16.7494 9.12422 17.2024 9.68308 17.2024H12.1117H14.5402C15.0991 17.2024 15.5521 16.7494 15.5521 16.1905C15.5521 15.6316 15.0991 15.1786 14.5402 15.1786H13.1236V10.5238C13.1236 9.96495 12.6705 9.51191 12.1117 9.51191H10.4926C9.93374 9.51191 9.4807 9.96495 9.4807 10.5238C9.4807 11.0827 9.93374 11.5357 10.4926 11.5357H11.0997V15.1786H9.68308ZM13.7307 6.47619C13.7307 7.37036 13.0058 8.09524 12.1117 8.09524C11.2175 8.09524 10.4926 7.37036 10.4926 6.47619C10.4926 5.58202 11.2175 4.85714 12.1117 4.85714C13.0058 4.85714 13.7307 5.58202 13.7307 6.47619Z"
        fill="black"
      />
    </g>
    <defs>
      <clipPath id="clip0_3538_1692">
        <rect
          width="22.6667"
          height="22.6667"
          fill="white"
          transform="translate(0.77832)"
        />
      </clipPath>
    </defs>
  </svg>
);

const FamilyPlansBanner = () => {
  return (
    <div className="bg-white py-4 px-6 rounded-lg max-w-4xl mx-auto">
      <div className="flex items-center gap-4">
        <InfoIcon />

        <div className="flex-1">
          <h2 className="text-md font-semibold text-black tracking-tight">
            SAVE MORE WITH FAMILY PLANS
          </h2>
          <p className="text-md text-gray-800 leading-relaxed">
            Save 10% on each additional plan you take after your first plan.
          </p>
        </div>
      </div>
    </div>
  );
};

export default FamilyPlansBanner;
