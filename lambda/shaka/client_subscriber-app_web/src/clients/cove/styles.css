body {
  --primary-color: #62D5EC;
  --secondary-color: #ffffff;
  --gray-color: #1a1a1a;
  --danger-color: #ff6969;
  --font-color: #000000;
  --secondary-font-color: #000000;
  --bg-color: #f3f3f3;
}

body {
  background: var(--bg-color);
  color: var(--font-color);
}

.cove .text-primary,
.cove~div .text-primary {
  color: var(--primary-color);
}

.bg-primary {
  background: var(--primary-color);
}

/* Logo */
.cove .logo {
  width: 60%;
  margin: 0 auto;
  transition: all 0.75s;
}

.cove .logo-wrapper.small .logo {
  width: 25%;
}

/* Initials */
.cove .initials {
  background-color: var(--primary-color);
}

/* Link */
.cove .button.primary,
.cove .link-button.primary {
  background: var(--primary-color);
}

.button.secondary,
.link-button.secondary {
  background: var(--secondary-color);
}

/* Input */
.input {
  background: var(--secondary-color);
}

.input:focus,
.input:focus {
  outline-color: var(--primary-color);
}

/* Select */
.option.selected {
  background: var(--primary-color);
  color: var(--secondary-font-color);
}

.select.focused {
  outline: 2px solid var(--primary-color);
}

/* Input validation mark */
.cove .validation.valid,
.cove~div .validation.valid {
  color: var(--primary-color);
}

/* Welcome page */
.cove .welcome-content {
  padding: 0 10%;
}

/* Contact phone */
.cove .contact-phone {
  color: var(--primary-color);
}

/* Plan card */
.cove .plan-card-detailed,
.cove~div .plan-card-detailed {
  color: white;
}

@media screen and (max-width: 375px) {
  .cove .plan-card-detailed {
    padding: 16px 24px;
  }  
}

.cove .card-text,
.cove~div .card-text {
  color: #313131;
}

.otp input {
  border-color: var(--primary-color);
}

span.data-consumed {
  background-color: #e4b85a;
}

@media screen and (max-width: 420px) {
  .cove .dashboard-logo-container {
    width: 5.5rem;
    height: 5.5rem;
  }
}

/* terms */
.list-title {
  margin-bottom: 8px;
  margin-top: 16px;
  font-weight: 600;
}

.list-decimal {
  list-style-type: decimal;
  margin-left: 16px;
}

.big-list-margin li {
  margin-bottom: 16px;
}

.list-alpha {
  list-style-type: lower-alpha;
  margin-left: 16px;
}

.small-list-margin li {
  margin-bottom: 8px;
}

.list-roman {
  list-style-type: lower-roman;
  margin-left: 16px;
}

.list-disc {
  list-style-type: disc;
  margin-left: 16px;
}

.list-circle {
  list-style-type: circle;
  margin-left: 16px;
}

.paragraphs p {
  margin-bottom: 12px;
}

.paragraphs .bold {
  font-weight: 600;
}

/* sim activation */
.cove .sim-activation-logo {
  max-width: 40%;
}