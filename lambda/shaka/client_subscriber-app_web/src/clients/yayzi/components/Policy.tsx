import { policyConfig } from './policy-config';

const paragraphClass = 'gap-2';
const subParagraphClass = 'ml-6 mt-1';
const blockClass = 'mb-4';
const subInfoClass = 'mt-3';

const Policy = () => {
  return (
    <div className="flex flex-col gap-4 text-sm">
      {policyConfig.map(({ title, blocks, paragraphs }, index) => (
        <div key={index}>
          <h4 className="pt-3 font-bold">{title}</h4>
          {blocks && (
            <div className="mt-3">
              {blocks.map(({ paragraph, list }, index) => (
                <div key={index} className={blockClass}>
                  <div className={paragraphClass}>
                    <p>{paragraph}</p>
                  </div>
                  {list && (
                    <ol
                      style={{
                        listStyleType: list.type
                      }}
                    >
                      {list.paragraphs?.map((paragraph, index) => (
                        <li key={index} className={subParagraphClass}>
                          <p>{paragraph}</p>
                        </li>
                      ))}

                      {list.blocks?.map(({ main, list }, index) => (
                        <li key={index} className={subParagraphClass}>
                          {main}
                          {list && (
                            <ol
                              style={{
                                listStyleType: list.type
                              }}
                            >
                              {list.paragraphs?.map((paragraph, index) => (
                                <li key={index} className={subParagraphClass}>
                                  <p>{paragraph}</p>
                                </li>
                              ))}
                            </ol>
                          )}
                        </li>
                      ))}
                    </ol>
                  )}
                </div>
              ))}
            </div>
          )}
          {paragraphs?.map((info, index) => (
            <p key={index} className={subInfoClass}>
              {info}
            </p>
          ))}
        </div>
      ))}
    </div>
  );
};

export default Policy;
