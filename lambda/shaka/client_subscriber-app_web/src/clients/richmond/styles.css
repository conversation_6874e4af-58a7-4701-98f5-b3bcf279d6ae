@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

body {
    --primary-color: #282A63;
    --secondary-color: #FFFFFF;
    --gray-color: #1a1a1a;
    --danger-color: #ff6969;
    --font-color: #000000;
    --secondary-font-color: #FFFFFF;
    --bg-color: #F3F3F3;
}

body {
    background: var(--bg-color);
    color: var(--font-color);
    font-family: 'Poppins', sans-serif;
}

.text-primary {
    color: var(--primary-color);
}

.bg-primary {
    background: var(--primary-color);
}

/* Logo */
.richmond .logo {
    width: 60%;
    margin: 0 auto;
    transition: all 0.75s;
}


/* Initials */
.initials {
    background-color: var(--primary-color);
}


/* Link */
.button.primary,
.link-button.primary {
    background: var(--primary-color);
    color: var(--secondary-font-color);
}

.button.secondary,
.link-button.secondary {
    background: var(--secondary-color);
}

/* Input */
.input {
    background: var(--secondary-color);
}

.input:focus,
.input:focus {
    outline-color: var(--primary-color);
}

/* Select */
.option.selected {
    background: var(--primary-color);
    color: var(--secondary-font-color);
}

.select.focused {
    outline: 2px solid var(--primary-color);
}

/* Input validation mark */
.validation.valid {
    color: var(--primary-color);
}

/* Welcome page */
.richmond .welcome-content {
    padding: 0 10%;
}

/* Contact phone */
.contact-phone {
    color: var(--primary-color);
}

/* Plan card */
.richmond .plan-card-detailed {
    color: var(--secondary-font-color);
}

.otp input {
    border-color: var(--primary-color);
}

span.data-consumed {
    background-color: #E4B85A;
}

.logo-circle.rounded-full.size-full {
    border-radius: 0px;
    position: absolute;
}

.dashboard-logo-container {
    margin-top: 10px;
    z-index: 1;
}

/* sim activation */
.richmond .sim-activation-logo {
    max-width: 20%;
  }