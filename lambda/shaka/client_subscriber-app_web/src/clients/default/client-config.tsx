// fix to avoid error with declare global
export {};

declare global {
  interface Window {
    clientConfig: {
      offer?: JSX.Element;
      familyBannerDiscount?: JSX.Element;
      supportEmail: string;
      supportLink?: string;
      policy?: JSX.Element;
      terms?: JSX.Element;
      help_esim_install?: string;
      help_data_issue?: string;
      backgroundImageNumber?: number;
      plainDashboardCard?: boolean;
      loginSource?: string;
      hidePerksBalance?: boolean;
    };
  }
}

window.clientConfig = {
  supportEmail: '<EMAIL>'
};
