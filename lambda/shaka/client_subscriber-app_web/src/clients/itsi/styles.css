body {
  --primary-color: #ffde59;
  --accent-color: #146b79;
  --secondary-color: #ffffff;
  --gray-color: #1a1a1a;
  --danger-color: #ff6969;
  --font-color: #000000;
  --secondary-font-color: #000000;
  --bg-color: #f3f3f3;
}

body {
  background: var(--bg-color);
  color: var(--font-color);
}

.itsi .text-primary,
.itsi~div .text-primary {
  color: var(--accent-color);
}

.bg-primary {
  background: var(--primary-color);
}

/* Logo */
.itsi .logo {
  width: 40%;
  margin: 0 auto;
  transition: all 0.75s;
}

.itsi .logo-wrapper.small .logo {
  width: 20%;
}

/* Initials */
.initials {
  background-color: var(--primary-color);
}

/* Link */
.button.primary,
.link-button.primary {
  background: var(--primary-color);
  color: var(--secondary-font-color);
}

.button.secondary,
.link-button.secondary {
  background: var(--secondary-color);
}

/* Input */
.input {
  background: var(--secondary-color);
}

.input:focus,
.input:focus {
  outline-color: var(--primary-color);
}

/* Select */
.option.selected {
  background: var(--primary-color);
  color: var(--secondary-font-color);
}

.select.focused {
  outline: 2px solid var(--primary-color);
}

/* Input validation mark */
.itsi .validation.valid,
.itsi~div .validation.valid {
  color: var(--accent-color);
}

/* Welcome page */
.itsi .welcome-content {
  padding: 0 10%;
}

/* Contact phone */
.itsi .contact-phone {
  color: var(--accent-color);
}

.itsi .plan-card-detailed {
  padding-left: 32px;
  padding-right: 32px;
}

/* Plan card */
.itsi .plan-card-detailed,
.itsi~div .plan-card-detailed {
  color: white;
}

.itsi .card-text,
.itsi~div .card-text {
  color: #313131;
}

.otp input {
  border-color: var(--primary-color);
}

span.data-consumed {
  background-color: #e4b85a;
}

/* sim activation */
.itsi .sim-activation-logo {
  max-width: 30%;
}