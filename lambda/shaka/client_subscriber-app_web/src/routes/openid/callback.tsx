import { createFileRoute, useNavigate } from '@tanstack/react-router';
import { useEffect, useRef } from 'react';
import { openidLogin } from 'src/api/openid';
import {
  getOpenIdData,
  removeOpenIdData
} from 'src/config/localStorageActions';
import { useAuth } from 'src/hooks/useAuth';
import { fetchSubscriber } from 'src/api/data';
import { ROUTES } from 'src/config/routes';
import FullPageLoader from 'src/components/common/FullPageLoader';
import useSubscription from 'src/hooks/useSubscription';

export const Route = createFileRoute('/openid/callback')({
  component: Callback
});

function Callback() {
  const { login } = useAuth();
  const navigate = useNavigate();

  const searchParams = new URLSearchParams(window.location.search);
  const code = searchParams.get('code') || '';
  const state = searchParams.get('state') || '';
  const effectRan = useRef(false);
  const { setSubscriber } = useSubscription();

  const loginToOpenId = async () => {
    openidLogin({ code, state, ...getOpenIdData() }).then((res) => {
      login(res);

      fetchSubscriber().then((subscriber) => {
        removeOpenIdData();
        setSubscriber(subscriber);
        if (!subscriber?.plans) {
          navigate({ to: ROUTES.PlanConfirmation });
        } else {
          navigate({ to: ROUTES.Dashboard });
        }
      });
    });
  };

  useEffect(() => {
    if (effectRan.current === false) {
      loginToOpenId();

      effectRan.current = true;
    }
  }, []);

  return <FullPageLoader />;
}
