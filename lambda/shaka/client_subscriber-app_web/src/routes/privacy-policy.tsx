import { createFileRoute, ScrollRestoration } from '@tanstack/react-router';
import BackButton from 'src/components/common/BackButton';
import Container from 'src/components/common/Container';
import DoneButton from 'src/components/common/DoneButton';
import PageTitle from 'src/components/common/PageTitle';

export const Route = createFileRoute('/privacy-policy')({
  component: PolicyWrapper
});

function PolicyWrapper() {
  return (
    <Container>
      <BackButton />
      <ScrollRestoration />
      <PageTitle>Privacy Policy</PageTitle>

      {window.clientConfig?.policy}

      <DoneButton />
    </Container>
  );
}
