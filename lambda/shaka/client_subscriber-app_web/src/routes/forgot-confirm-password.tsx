import { createFileRoute, useNavigate } from '@tanstack/react-router';
import { ROUTES } from 'src/config/routes';
import Container from 'src/components/common/Container';
import { confirmForgotPassword } from 'src/api/auth';
import { useForm } from 'react-hook-form';
import Input from 'src/components/common/Input';
import Button from 'src/components/common/Button';
import Link from 'src/components/common/Link';
import {
  ForgotConfirmPasswordInputs,
  schema
} from 'src/schemas/forget-password';
import { yupResolver } from '@hookform/resolvers/yup';
import ErrorText from 'src/components/common/ErrorText';
import PageTitle from 'src/components/common/PageTitle';
import { useRequest } from 'src/hooks/useRequest';
export const Route = createFileRoute('/forgot-confirm-password')({
  component: ForgotConfirmPassword
});

function ForgotConfirmPassword() {
  const navigate = useNavigate();
  const { run: runConfirmForgotPassword, error } = useRequest(
    confirmForgotPassword,
    {
      defaultErrorText: 'Invalid code or email provided'
    }
  );
  const { email } = Route.useSearch<{ email: string }>();
  const {
    handleSubmit,
    register,
    formState: { errors }
  } = useForm<ForgotConfirmPasswordInputs>({
    resolver: yupResolver(schema),
    defaultValues: {
      email: email || ''
    }
  });

  const onSubmit = (data: ForgotConfirmPasswordInputs) => {
    runConfirmForgotPassword({
      ...data,
      verification_code: data.verification_code.replace(/\s/g, '')
    }).then(() => {
      navigate({ to: ROUTES.Login });
    });
  };

  return (
    <Container>
      <PageTitle>Check your email</PageTitle>
      <p>There you'll find a verification code, enter it below:</p>
      <form className="space-y-6 mt-10" onSubmit={handleSubmit(onSubmit)}>
        <Input
          {...register('verification_code')}
          placeholder="Verification code"
        />
        <Input
          {...register('email')}
          placeholder="Email"
          error={errors.email?.message}
        />
        <Input
          {...register('new_password')}
          placeholder="New password"
          type="password"
          error={errors.new_password?.message}
        />
        <Input
          {...register('new_password_confirmation')}
          placeholder="Confirm new password"
          type="password"
          error={errors.new_password_confirmation?.message}
        />
        <div>
          <ErrorText>{error}</ErrorText>
          <div className="space-y-2">
            <Button type="submit">Submit</Button>
            <Link to={ROUTES.Login} styleType="button" color="secondary">
              Go to login
            </Link>
          </div>
        </div>
      </form>
    </Container>
  );
}
