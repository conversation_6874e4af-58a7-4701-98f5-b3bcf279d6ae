import {
  LinkProps,
  Outlet,
  createFileRoute,
  redirect,
  useRouterState
} from '@tanstack/react-router';
import clsx from 'clsx';
import { useRef } from 'react';
import Container from 'src/components/common/Container';
import Logo from 'src/components/common/Logo';
import { LocalKey } from 'src/config/localStorageActions';
import { ROUTES } from 'src/config/routes';
import { useAuth } from 'src/hooks/useAuth';
import useSignupSteps, {
  checkIfPathNeedsPlanId,
  checkIfPublicPaths,
  checkIfRestrictedPaths
} from 'src/hooks/useSignupSteps';

const MAX_STEP_BAR_WIDTH = 408;
const STEP_BAR_PADDING = 20;

export const Route = createFileRoute('/_signup-flow')({
  component: LayoutComponent,
  beforeLoad: async ({ context, location }) => {
    const isRestrictedPath = checkIfRestrictedPaths(
      location.pathname as LinkProps['to']
    );
    const isPublicPath = checkIfPublicPaths(
      location.pathname as LinkProps['to']
    );
    const isPathNeedsPlanId = checkIfPathNeedsPlanId(
      location.pathname as LinkProps['to']
    );

    if (isRestrictedPath && !context?.auth.isAuthenticated()) {
      throw redirect({
        to: ROUTES.Login,
        search: {
          redirect: location.pathname
        }
      });
    }

    if (isPublicPath && context?.auth.isAuthenticated()) {
      throw redirect({
        to: ROUTES.Dashboard
      });
    }

    if (isPathNeedsPlanId && !localStorage.getItem(LocalKey.PLAN_ID)) {
      throw redirect({
        to: ROUTES.Dashboard
      });
    }
  }
});

function LayoutComponent() {
  const { step, stepsAmount, isIndexPage, stepTitle } = useSignupSteps();
  const { isAuthenticated } = useAuth();
  const stepBar = useRef<HTMLDivElement>(null);
  const { location } = useRouterState();
  const pathname = location.pathname;
  const removeBottomPadding = pathname.includes(ROUTES.EsimSettings);

  const isLoggedIn = isAuthenticated();

  let stepBarWidth = window.innerWidth - 2 * STEP_BAR_PADDING;

  stepBarWidth =
    stepBarWidth > MAX_STEP_BAR_WIDTH ? MAX_STEP_BAR_WIDTH : stepBarWidth;

  const indicatorWidth = stepBarWidth / stepsAmount;

  return (
    <Container
      className="flex flex-col gap-5"
      removeBottomPadding={removeBottomPadding}
    >
      <div
        className={clsx('logo-wrapper z-10 relative', !isIndexPage && 'small')}
      >
        <Logo disableRedirect={isLoggedIn} />
        <div className="absolute w-screen max-w-[475px] h-[150px] -bottom-4 -left-5 backdrop-blur z-[-1] bg-[#f3f3f3c9]" />
        {!isNaN(step!) && (
          <div
            className={clsx('steps mt-5 relative', !isIndexPage && 'visible')}
          >
            <div className="text-center font-semibold uppercase">
              {stepTitle}
            </div>
            <div
              ref={stepBar}
              className={clsx(
                'relative h-1.5 bg-black/5 m-auto mt-5 rounded w-full'
              )}
            >
              <span
                className="absolute h-full top-0 bg-black rounded transition-all duration-200 ease-linear"
                style={{
                  width: `${indicatorWidth}px`,
                  left: (step! - 1) * indicatorWidth + 'px'
                }}
              />
            </div>

            <div className="mt-3 m-auto text-center text-xs text-gray-400">
              Step {step} of {stepsAmount}
            </div>
          </div>
        )}
      </div>

      <div className={clsx('grow flex flex-col', isIndexPage && 'justify-end')}>
        <Outlet />
      </div>
    </Container>
  );
}
