import { createFileRoute, useNavigate } from '@tanstack/react-router';
import SimSelfActivation from 'src/components/SimActivation';
import { ROUTES } from 'src/config/routes';

export const Route = createFileRoute('/_signup-flow/sim-activation')({
  component: SimSelfActivationPage
});

function SimSelfActivationPage() {
  const navigate = useNavigate();

  const onSubmit = () => {
    navigate({ to: ROUTES.FinishSignUp });
  };

  return <SimSelfActivation onNextClick={onSubmit} />;
}
