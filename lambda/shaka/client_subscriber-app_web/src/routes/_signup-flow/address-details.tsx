import { createFileRoute, useNavigate } from '@tanstack/react-router';
import { ROUTES } from 'src/config/routes';
import AddressDetails from 'src/components/Address';

export const Route = createFileRoute('/_signup-flow/address-details')({
  component: AddressDetailsPage
});

function AddressDetailsPage() {
  const navigate = useNavigate();

  const onFormSubmit = () => {
    navigate({ to: ROUTES.FinishSignUp });
  };

  return (
    <AddressDetails onSubmitSuccess={onFormSubmit} backTo={ROUTES.ChooseSim} />
  );
}
