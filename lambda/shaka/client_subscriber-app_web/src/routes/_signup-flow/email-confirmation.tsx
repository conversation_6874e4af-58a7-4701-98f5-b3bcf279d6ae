import { createFileRoute, useNavigate } from '@tanstack/react-router';
import { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import OtpInput from 'react18-input-otp';
import Button from 'src/components/common/Button';
import { RightArrowIcon } from 'src/assets/icons';
import { schema, SignupCodeInputs } from 'src/schemas/signup-code';
import { yupResolver } from '@hookform/resolvers/yup';
import { resendVerificationCode, verify } from 'src/api/auth';
import ErrorText from 'src/components/common/ErrorText';
import { ROUTES } from 'src/config/routes';
import NotificationDialog from 'src/components/common/NotificationDialog';
import useSubscription from 'src/hooks/useSubscription';
import { Subscriber } from 'src/types/subscriber';
import { useRequest } from 'src/hooks/useRequest';
import { useAuth } from 'src/hooks/useAuth';
import { InfoOutlineIcon } from 'src/assets/icons/InfoOutline';

function EmailConfirmation() {
  const supportEmail = window.clientConfig.supportEmail;
  const navigate = useNavigate();
  const { logout } = useAuth();

  const { run: runVerify, error } = useRequest(verify);

  const { subscriber, setSubscriber } = useSubscription();

  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [countdown, setCountdown] = useState(60);

  const {
    handleSubmit,
    control,
    watch,
    formState: { isValid }
  } = useForm<SignupCodeInputs>({
    resolver: yupResolver(schema)
  });

  const watchCode = watch('code');

  const onSubmit = (data: SignupCodeInputs) => {
    runVerify({ code: data.code }).then(() => {
      setSubscriber({ ...subscriber, is_verified: true } as Subscriber);
    });
  };

  const onCodeResend = () => {
    resendVerificationCode().then(() => {
      setCountdown(60);
    });
  };

  const handleSupportClick = () => {
    setIsPopupOpen(true);
  };

  const closePopup = () => {
    setIsPopupOpen(false);
  };

  const handleStartAgain = () => {
    logout();
    setSubscriber(null);
    navigate({ to: ROUTES.SignUp });
  };

  useEffect(() => {
    let timer: NodeJS.Timeout;

    if (countdown > 0) {
      timer = setInterval(() => {
        setCountdown((prevCountdown) => prevCountdown - 1);
      }, 1000);
    }

    return () => {
      clearInterval(timer);
    };
  }, [countdown]);

  useEffect(() => {
    if (subscriber?.is_verified) {
      navigate({ to: ROUTES.PlanConfirmation });
    }
  }, [navigate, subscriber]);

  useEffect(() => {
    if (watchCode?.length === 4) {
      onSubmit({ code: watchCode });
    }
  }, [watchCode]);

  return (
    <>
      <div className="text-center font-semibold uppercase">
        Confirm your email
      </div>

      <div className="grow text-center mt-[20vh] max-short:mt-[15vh]">
        <p className="text-sm text-gray-500 whitespace-pre-line mb-10">
          We’ve sent a 4 digit passcode to
          <span className="block font-bold">{subscriber?.email}</span>
          please confirm it below:
        </p>

        <form onSubmit={handleSubmit(onSubmit)}>
          <Controller
            control={control}
            name="code"
            render={({ field }) => (
              <OtpInput
                {...field}
                value={field.value}
                placeholder="____"
                shouldAutoFocus
                isInputNum
                containerStyle={{
                  display: 'flex',
                  justifyContent: 'center',
                  gap: 16,
                  margin: 'auto'
                }}
                inputStyle={{
                  borderRadius: '10px',
                  width: '60px',
                  height: '48px'
                }}
                className="otp"
              />
            )}
          />

          <div className="mt-8">
            <ErrorText>{error}</ErrorText>
            <Button
              type="submit"
              rightIcon={
                <span className="absolute right-4 top-[14px]">
                  <RightArrowIcon />
                </span>
              }
              disabled={!isValid}
            >
              Submit
            </Button>
          </div>

          <div className="mt-6">
            <Button
              color="secondary"
              type="button"
              disabled={countdown > 0}
              onClick={onCodeResend}
            >
              {countdown
                ? `Send code again (${countdown}s)`
                : 'Send code again'}
            </Button>
          </div>
        </form>
      </div>

      <button
        className="text-center text-gray-500 text-sm flex items-center gap-2 justify-center hover:text-gray-600 mt-6"
        onClick={handleSupportClick}
      >
        <InfoOutlineIcon />
        <span>Code not received?</span>
      </button>

      <NotificationDialog
        isOpen={isPopupOpen}
        onCancel={closePopup}
        cancelButtonText="Close"
        cancelActionColor="default"
        dialogType="info"
        title="Code not received?"
        description={
          <div className="text-sm text-gray-500">
            <p className="mb-4">
              Typo in your email address? You can simply{' '}
              <button className="text-sm underline" onClick={handleStartAgain}>
                start again
              </button>
            </p>

            <p>
              If you still didn’t receive the code after re-sending, contact{' '}
              <span className="underline">
                <a href={`mailto:${supportEmail}`}>{supportEmail}</a>
              </span>
            </p>
          </div>
        }
      />
    </>
  );
}

export const Route = createFileRoute('/_signup-flow/email-confirmation')({
  component: EmailConfirmation
});
