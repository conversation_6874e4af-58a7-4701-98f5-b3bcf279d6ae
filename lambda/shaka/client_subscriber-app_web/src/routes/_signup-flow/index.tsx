import { createFileRoute, Navigate } from '@tanstack/react-router';
import React from 'react';
import Link from 'src/components/common/Link';
import { ROUTES } from 'src/config/routes';

export const DemoPlans: React.FC = () => {
  const {
    clientProps: { isSignupDisabled }
  } = Route.useRouteContext();

  if (isSignupDisabled) {
    return <Navigate to={ROUTES.Login} replace={true} />;
  }

  return (
    <div className="welcome-content space-y-6">
      <Link to={ROUTES.ExplorePlans} styleType="button" withArrow>
        Explore plans
      </Link>
      <Link
        to={ROUTES.Login}
        color="secondary"
        styleType="button"
        search={{ back: true }}
        withArrow
      >
        Log in
      </Link>
      <div />
    </div>
  );
};

export const Route = createFileRoute('/_signup-flow/')({
  component: DemoPlans
});
