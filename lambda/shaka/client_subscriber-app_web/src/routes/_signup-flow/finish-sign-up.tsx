import { createFileRoute } from '@tanstack/react-router';
import { useEffect } from 'react';
import { CheckRoundedFilledIcon } from 'src/assets/icons/CheckRoundedFilled';
import Link from 'src/components/common/Link';
import WhiteBlock from 'src/components/common/WhiteBlock';
import { removeSimSettingsSelectedType } from 'src/config/localStorageActions';
import { ROUTES } from 'src/config/routes';
import { useClient } from 'src/hooks/useClient';
import useSubscription from 'src/hooks/useSubscription';

export const Route = createFileRoute('/_signup-flow/finish-sign-up')({
  component: FinishSignUp
});

export default function FinishSignUp() {
  const { clientName } = useClient();
  const { currentPlan } = useSubscription();
  const { sim_type } = currentPlan;
  const isEsim = sim_type === 'esim';

  useEffect(() => {
    removeSimSettingsSelectedType();
  }, []);

  return (
    <div className="grow flex flex-col -mb-12 [@media(max-width:400px)]:-mb-5">
      <div className="grow flex flex-col justify-center items-center gap-8 mb-8">
        <div className="validation valid">
          <CheckRoundedFilledIcon className="size-12" />
        </div>
        <span className="font-semibold mb-4">And you are all done!</span>
        <Link
          to={ROUTES.Dashboard}
          color="black"
          styleType="button"
          replace
          size="small"
          withArrow
        >
          Go to dashboard
        </Link>
      </div>

      {isEsim && (
        <WhiteBlock title="If you’re still not connected">
          <div className="space-y-2 text-sm">
            <div className="flex gap-2 items-center">
              <CheckRoundedFilledIcon
                className="size-4 text-white"
                circleColor="black"
              />
              <span>
                Ensure{' '}
                <span className="font-semibold">Mobile Data is toggled ON</span>
              </span>
            </div>
            <div className="flex gap-2 items-center">
              <CheckRoundedFilledIcon
                className="size-4 text-white"
                circleColor="black"
              />
              <span>
                Ensure your{' '}
                <span className="font-semibold">eSIM is turned ON </span>
              </span>
            </div>
            <div className="flex gap-2 ">
              <CheckRoundedFilledIcon
                className="size-4 text-white mt-0.5 min-w-4"
                circleColor="black"
              />
              <span>
                Ensure <span className="font-semibold">{clientName}</span> is
                selected as your network
              </span>
            </div>
          </div>
        </WhiteBlock>
      )}

      <div className="mt-4 -mb-2 text-right text-xs">
        {isEsim ? 'Still need help? Talk to our' : 'Need help? Talk to our'}{' '}
        <a
          href={`mailto:${window.clientConfig.supportEmail}`}
          className="text-[#347DA8] underline"
        >
          support
        </a>
        .
      </div>
    </div>
  );
}
