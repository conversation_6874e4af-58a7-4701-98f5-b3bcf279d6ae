import { createFileRoute } from '@tanstack/react-router';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { sendEsimInstructions } from 'src/api/auth';
import { CheckRoundedFilledIcon } from 'src/assets/icons/CheckRoundedFilled';
import Button from 'src/components/common/Button';
import Input from 'src/components/common/Input';
import Link from 'src/components/common/Link';
import { ROUTES } from 'src/config/routes';
import { useRequest } from 'src/hooks/useRequest';
import useSubscription from 'src/hooks/useSubscription';

export const Route = createFileRoute('/_signup-flow/esim-confirmation')({
  component: AddressDetails
});

type Inputs = { email: string };

function AddressDetails() {
  const { subscriber, fetchSubscriber } = useSubscription();
  const { run: runSendEsimInstructions } = useRequest(sendEsimInstructions);

  const [isSent, setIsSent] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors }
  } = useForm<Inputs>({
    defaultValues: {
      email: subscriber?.email
    }
  });

  const onFormSubmit = (data: Inputs) => {
    runSendEsimInstructions(data.email)
      .then(() => {
        fetchSubscriber();
        setIsSent(true);
      })
      .catch((error) => {
        setError(error.response?.data.error || 'Something went wrong');
      });
  };

  if (isSent) {
    return (
      <>
        <div className="grow flex flex-col items-center justify-center gap-8">
          <div className="validation valid">
            <CheckRoundedFilledIcon className="size-12" />
          </div>
          <span className="text-sm font-bold">And you are all done!</span>
          <div className="mx-10 text-sm">
            We have just sent you an email with your new eSIM - follow the
            instruction to install it
          </div>
        </div>
        <div className="mx-7">
          <Link
            to={ROUTES.Dashboard}
            color="secondary"
            styleType="button"
            replace
          >
            Go to dashboard
          </Link>
        </div>
      </>
    );
  }

  return (
    <div className="mx-10 mt-10">
      <p className="text-sm">
        Almost done, just let us know where to email the eSIM and instructions
        to:
      </p>

      <form className="mt-10" onSubmit={handleSubmit(onFormSubmit)}>
        <div className="mb-16">
          <Input
            placeholder="email address"
            {...register('email')}
            error={errors.email?.message}
          />
        </div>

        {error && <p className="text-red</select>-500">{error}</p>}
        <Button type="submit" color="secondary">
          Submit
        </Button>
      </form>
    </div>
  );
}
