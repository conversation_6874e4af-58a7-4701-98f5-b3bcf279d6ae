import { createFileRoute } from '@tanstack/react-router';
import { getOpenIdUrl } from 'src/api/openid';
import Button from 'src/components/common/Button';
import { setOpenIdData } from 'src/config/localStorageActions';
import { useRequest } from 'src/hooks/useRequest';

export const Route = createFileRoute('/_signup-flow/openid/login')({
  component: OpenIdLogin
});

function OpenIdLogin() {
  const { run: runGetOpenIdUrl } = useRequest(getOpenIdUrl);

  const handleLoginClick = () => {
    runGetOpenIdUrl().then((res) => {
      const { auth_url, state_plaintext, code_verifier } = res;
      setOpenIdData({ state: state_plaintext, code: code_verifier });
      window.location.href = auth_url;
    });
  };

  return (
    <div className="grow flex flex-col justify-center">
      <div className="text-center mb-6">
        Continue with your{' '}
        <span className="font-semibold">{window.clientConfig.loginSource}</span>
      </div>
      <Button onClick={handleLoginClick}>Login</Button>
    </div>
  );
}
