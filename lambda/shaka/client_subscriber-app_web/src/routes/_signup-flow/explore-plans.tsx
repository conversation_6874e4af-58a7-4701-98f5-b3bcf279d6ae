import { createFileRoute, useNavigate } from '@tanstack/react-router';
import { useCallback } from 'react';
import ExplorePlans from 'src/components/ExplorePlans';
import { ROUTES } from 'src/config/routes';

export const Route = createFileRoute('/_signup-flow/explore-plans')({
  component: ExplorePlansPage,
  beforeLoad: ({ context }) => {
    return {
      isAuthUser: context?.auth.isAuthenticated()
    };
  }
});

function ExplorePlansPage() {
  const { isAuthUser } = Route.useRouteContext();
  const navigate = useNavigate();

  const handlePlanConfirm = useCallback(() => {
    if (isAuthUser) {
      navigate({ to: ROUTES.PlanConfirmation });
      return;
    }
    navigate({ to: ROUTES.SignUp });
  }, [isAuthUser, navigate]);

  return (
    <ExplorePlans hideBackButton={isAuthUser} onPlanConfirm={handlePlanConfirm} />
  );
}
