import { createFileRoute, Navigate, useRouter } from '@tanstack/react-router';
import { useEffect } from 'react';
import { checkSessionStatus } from 'src/api/stripe';
import Button from 'src/components/common/Button';
import Link from 'src/components/common/Link';
import Loader from 'src/components/common/Loader';
import PageTitle from 'src/components/common/PageTitle';
import {
  removeStripeInfo,
  setCorrelationId,
  setStripeSessionInfo
} from 'src/config/localStorageActions';
import { ROUTES } from 'src/config/routes';
import useSubscription from 'src/hooks/useSubscription';
import { PaymentStatus, SessionStatus } from 'src/types/checkout';

export const Route = createFileRoute('/_signup-flow/checkout/return')({
  component: CheckoutReturn,
  loaderDeps: ({
    search: { session_id }
  }: {
    search: Record<string, string>;
  }) => ({
    session_id
  }),
  loader: ({ deps: { session_id } }) =>
    checkSessionStatus(session_id).then((res) => {
      setStripeSessionInfo(session_id, res.expires_at);

      return { status: res.status, paymentStatus: res.payment_status };
    })
});

function CheckoutReturn() {
  const { history } = useRouter();
  const { subscriber, fetchSubscriber } = useSubscription();
  const { status, paymentStatus } = Route.useLoaderData<{
    paymentStatus: PaymentStatus;
    status: SessionStatus;
  }>();
  const isSessionCompleted = status === SessionStatus.Complete;
  const isPaymentPaid = paymentStatus === PaymentStatus.Paid;

  const correlationId = new URLSearchParams(window.location.search).get(
    'correlation_id'
  );

  const onBack = () => history.back();

  useEffect(() => {
    if (isSessionCompleted && isPaymentPaid) {
      removeStripeInfo();
    }
  }, [isPaymentPaid, isSessionCompleted]);

  useEffect(() => {
    const interval = setInterval(() => {
      if (!subscriber?.plans) {
        fetchSubscriber();
      }
    }, 500);
    return () => clearInterval(interval);
  }, [subscriber, fetchSubscriber]);

  if (!isSessionCompleted) {
    return (
      <div className="grow flex flex-col justify-center">
        <PageTitle>Payment failed</PageTitle>
        <p className="text-center mb-20">
          We are unable to process your payment. Please try again.
        </p>
        <Button onClick={onBack}>Return to payment</Button>
      </div>
    );
  }

  if (!isPaymentPaid) {
    return (
      <div className="grow flex flex-col justify-center">
        <PageTitle>We are waiting for your payment</PageTitle>
        <p className="mb-20 text-center">
          While we wait you can fill in the data to get your SIM.
        </p>
        <div className="space-y-2">
          <Link to={ROUTES.AddressDetails} styleType="button">
            Continue
          </Link>
          <Button onClick={onBack}>Return to payment</Button>
        </div>
      </div>
    );
  }

  const planWithMatchingCorrelationId = subscriber?.plans?.find(
    (plan) => plan.correlation_id === correlationId
  );
  if (!planWithMatchingCorrelationId) {
    setCorrelationId(correlationId!);
    return (
      <div className="grow flex flex-col justify-center items-center">
        <PageTitle>Transaction in progress</PageTitle>
        <p className="text-center mb-20">
          Your payment is being processed.
          <br />
          Please wait a moment
        </p>
        <Loader />
      </div>
    );
  }

  return <Navigate to={ROUTES.ChooseSim} replace />;
}
