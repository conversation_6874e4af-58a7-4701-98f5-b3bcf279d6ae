import { createFileRoute } from "@tanstack/react-router";
import React, { useContext, useEffect, useState } from "react";
import { getClientSecretSignUp } from "src/api/stripe";
import {
  EmbeddedCheckoutProvider,
  EmbeddedCheckout,
} from "@stripe/react-stripe-js";
import { StripeElementsOptions } from "@stripe/stripe-js";
import FullPageLoader from "src/components/common/FullPageLoader";
import { StripeContext } from "src/context/StripeContext";

export const Purchase: React.FC = () => {
  const [options, setOptions] = useState<StripeElementsOptions | null>(null);
  const { stripePromise } = useContext(StripeContext);

  useEffect(() => {
    const planId = localStorage.getItem("plan_id");
    const clientSecret = localStorage.getItem("client_secret");
    const sessionExpiresAt = localStorage.getItem("session_expires_at");
    const isSecretValid =
      sessionExpiresAt && new Date(Number(sessionExpiresAt)) > new Date();

    if (clientSecret && isSecretValid) {
      setOptions({
        clientSecret,
      });
      return;
    }

    getClientSecretSignUp(planId ?? "").then((res) => {
      setOptions({
        clientSecret: res.client_secret,
      });
      localStorage.setItem("client_secret", res.client_secret);
    });
  }, []);

  if (!options) {
    return (
      <div className="w-full h-full">
        <FullPageLoader />
      </div>
    );
  }

  return (
    <EmbeddedCheckoutProvider stripe={stripePromise} options={options}>
      <EmbeddedCheckout />
    </EmbeddedCheckoutProvider>
  );
};

export const Route = createFileRoute("/_signup-flow/checkout/")({
  component: Purchase,
});
