import { createFileRoute, Navigate } from '@tanstack/react-router';
import { useMemo, useState } from 'react';
import Container from 'src/components/common/Container';
import FullPageLoader from 'src/components/common/FullPageLoader';
import { ROUTES } from 'src/config/routes';
import usePlans from 'src/hooks/usePlans';
import ProgressLinks from 'src/components/common/ProgressLinks';
import useSubscription from 'src/hooks/useSubscription';
import { useRequest } from 'src/hooks/useRequest';
import { updateSubscriber } from 'src/api/data';
import { getAwaitingPlanId } from 'src/config/localStorageActions';
import PlanConfirmation from 'src/components/PlanConfirmation';

export const Route = createFileRoute('/_signup-flow/plan-confirmation')({
  component: PlanConfirmationPage
});

export function PlanConfirmationPage() {
  const { run: runUpdateSubscriber } = useRequest(updateSubscriber);
  const { plans, isLoading } = usePlans();
  const { setSubscriber } = useSubscription();

  const [isOptIn, setIsOptIn] = useState(true);

  const selectedPlan = useMemo(() => {
    const planId = getAwaitingPlanId();
    const selectedPlan = plans.find(
      (plan) => plan.clientPlan.id === parseInt(planId!)
    );

    return selectedPlan;
  }, [plans]);

  const handleNextClick = () => {
    runUpdateSubscriber({ send_marketing: isOptIn }).then((data) => {
      setSubscriber(data);
    });
  };

  if (!selectedPlan && !isLoading) {
    <Navigate to={ROUTES.ExplorePlans} replace={true} />;
  }

  if (isLoading) {
    return (
      <Container>
        <FullPageLoader />
      </Container>
    );
  }

  return (
    <div className="mt-3">
      <PlanConfirmation
        selectedPlan={selectedPlan}
        isOptIn={isOptIn}
        onOptInChange={setIsOptIn}
      />

      <ProgressLinks
        backTo={ROUTES.ExplorePlans}
        nextTo={ROUTES.Checkout}
        onNextClick={handleNextClick}
      />
    </div>
  );
}
