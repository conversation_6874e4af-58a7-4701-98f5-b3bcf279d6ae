/* eslint-disable @typescript-eslint/no-unused-vars */
import { createFileRoute } from '@tanstack/react-router';
import EsimSettings from 'src/components/EsimSettings';
import { ROUTES } from 'src/config/routes';

export const Route = createFileRoute('/_signup-flow/esim-settings')({
  component: EsimSettingsPage
});

function EsimSettingsPage() {
  return <EsimSettings nextTo={ROUTES.FinishSignUp} />;
}
