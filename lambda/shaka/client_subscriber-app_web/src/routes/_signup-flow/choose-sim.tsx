import { useEffect } from 'react';
import {
  createFileRoute,
  LinkProps,
  Navigate,
  useNavigate
} from '@tanstack/react-router';
import SelectSimType, { SimTypeOption } from 'src/components/SelectSimType';
import { ROUTES } from 'src/config/routes';
import useSubscription from 'src/hooks/useSubscription';
import { getCorrelationId } from 'src/config/localStorageActions.ts';

function ChooseSimPage() {
  const navigate = useNavigate();
  const { currentPlan, setCurrentPlanByCorrelationId } = useSubscription();
  const correlationId = getCorrelationId();

  useEffect(() => {
    if (correlationId) {
      setCurrentPlanByCorrelationId(correlationId);
    }
  }, [correlationId, setCurrentPlanByCorrelationId]);

  const handleSimTypeSubmit = (selectedSimType: SimTypeOption) => {
    let nextTo: LinkProps['href'] = ROUTES.EsimSettings;
    if (selectedSimType === 'physical') {
      nextTo = ROUTES.AddressDetails;
    }
    if (selectedSimType === 'self-serve') {
      nextTo = ROUTES.SimActivation;
    }

    navigate({
      to: nextTo
    });
  };

  if (!currentPlan?.sim_type) {
    return <Navigate to={ROUTES.ExplorePlans} />;
  }

  return <SelectSimType onNextClick={handleSimTypeSubmit} />;
}

export const Route = createFileRoute('/_signup-flow/choose-sim')({
  component: ChooseSimPage
});
