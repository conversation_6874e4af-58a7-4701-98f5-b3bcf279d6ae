import { createFileRoute, ScrollRestoration } from '@tanstack/react-router';
import React, { useRef } from 'react';
import Slider from 'react-slick';
import {
  hideNotificationDialog,
  NotificationDialog as NotificationDialogType
} from 'src/api/subscription';
import { NotificationDialog } from 'src/components/NotificationDialog';

import PlanCard from 'src/components/PlanCard';
import TravelSimCard from 'src/components/TravelSimCard';
import FullPageLoader from 'src/components/common/FullPageLoader';
import LogoCircle from 'src/components/common/LogoCircle';
import FeaturedPerks from 'src/components/perks/FeaturedPerks';
import { ROUTES } from 'src/config/routes';
import { checkIsIos11OrHigher, isMoreThan14Days } from 'src/helpers';
import { useRequest } from 'src/hooks/useRequest';
import useSubscription from 'src/hooks/useSubscription';
import { PacCodeStatus, SubscriberPlan } from 'src/types/subscriber';
import { twMerge } from 'tailwind-merge';

const planPlaceholder = {
  id: 0,
  sim_activation_status: 'not-assigned',
  subscription_status: 'inactive'
} as SubscriberPlan;

const settings = {
  dots: false,
  infinite: false,
  speed: 250,
  slidesToShow: 0.98,
  slidesToScroll: 1,
  arrows: false,
  centerMode: true,
  centerPadding: '20px'
};

const isPlainCard = window.clientConfig.plainDashboardCard;

export const Dashboard: React.FC = () => {
  const { run: runHideNotificationDialog } = useRequest(hideNotificationDialog);

  const {
    subscriber,
    isLoading,
    setSubscriber,
    currentPlan,
    currentPlanIndex,
    setCurrentPlanIndex,
    activeAndRecentlyExpiredRoamingSims
  } = useSubscription();

  const sliderRef = useRef<Slider | null>(null);

  const { pac_code_status: pacCodeStatus, notifications } = currentPlan;

  const {
    show_number_porting_progress,
    show_number_transfer,
    show_set_up_esim,
    show_update_apn
  } = notifications || {};

  const isKeepNumberExpired = isMoreThan14Days(subscriber?.join_date);
  const isKeepNumberVisible =
    subscriber && !isKeepNumberExpired && show_number_transfer;

  const isPacCodePending = pacCodeStatus === PacCodeStatus.PENDING;
  const isPacCodeInProgress = pacCodeStatus === PacCodeStatus.IN_PROGRESS;
  const isPacCodeRequested =
    (isPacCodePending || isPacCodeInProgress) && show_number_porting_progress;

  const isSetUpEsimVisible =
    currentPlan.sim_type === 'esim' && show_set_up_esim;

  const isIos = checkIsIos11OrHigher();
  const isUpdateApnVisible =
    isIos && currentPlan.sim_type === 'esim' && show_update_apn;

  const handleHideDialog = (type: NotificationDialogType) => () => {
    runHideNotificationDialog({ type, subscription_id: currentPlan.id }).then(
      (data) => {
        setSubscriber(data);
      }
    );
  };

  if (isLoading) {
    return <FullPageLoader />;
  }

  return (
    <>
      <ScrollRestoration />

      <div className={twMerge(isPlainCard ? 'mt-4' : 'mt-10')}>
        <div className="relative">
          {!isPlainCard && (
            <div className="absolute left-0 right-0 -translate-y-1/2 dashboard-logo-container">
              <LogoCircle />
            </div>
          )}
          <div className="w-screen max-w-md -translate-x-5 relative">
            <Slider
              {...settings}
              ref={sliderRef}
              initialSlide={currentPlanIndex}
              afterChange={setCurrentPlanIndex}
            >
              {subscriber?.plans &&
                subscriber.plans?.map((plan) => (
                  <div key={plan.id}>
                    <div className="mr-2">
                      <PlanCard
                        key={plan.id}
                        plan={plan}
                        phoneNumber={plan.phone_number}
                      />
                    </div>
                  </div>
                ))}

              {Boolean(activeAndRecentlyExpiredRoamingSims.length) &&
                activeAndRecentlyExpiredRoamingSims.map((roamingSim) => (
                  <div key={roamingSim.id}>
                    <div className="mr-2">
                      <TravelSimCard {...roamingSim} />
                    </div>
                  </div>
                ))}
              {!subscriber?.plans && (
                <PlanCard plan={planPlaceholder} phoneNumber="" />
              )}
            </Slider>
          </div>
        </div>

        {/* Notifications */}
        <div className="space-y-3">
          {isKeepNumberVisible && (
            <NotificationDialog
              title="Keep your old number"
              subtitle="Click here to port your number"
              link={ROUTES.PortNumber}
              onDismiss={handleHideDialog('number_transfer')}
            />
          )}

          {isPacCodeRequested && (
            <NotificationDialog
              title="Porting in progress"
              subtitle="Click to see the current porting status"
              link={ROUTES.PortNumber}
              onDismiss={handleHideDialog('number_porting_progress')}
            />
          )}

          {isUpdateApnVisible && (
            <NotificationDialog
              title="Update APN settings"
              subtitle="APN setting needs to be changed on iPhone"
              link={ROUTES.EsimSettingInternal}
              onDismiss={handleHideDialog('update_apn')}
            />
          )}

          {isSetUpEsimVisible && (
            <NotificationDialog
              title="Setup your eSIM"
              subtitle="Review eSIM installation instructions"
              link={ROUTES.EsimSettingInternal}
              onDismiss={handleHideDialog('set_up_esim')}
            />
          )}
        </div>

        <FeaturedPerks />
        {window.clientConfig?.offer}
      </div>
    </>
  );
};

export const Route = createFileRoute('/_auth/_layout/dashboard')({
  component: Dashboard
});
