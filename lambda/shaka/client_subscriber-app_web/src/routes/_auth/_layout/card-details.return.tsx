import { createFileRoute } from "@tanstack/react-router";
import Link from "src/components/common/Link";
import PageTitle from "src/components/common/PageTitle";
import { ROUTES } from "src/config/routes";

export const Route = createFileRoute("/_auth/_layout/card-details/return")({
  component: CardDetailsReturn,
});

export function CardDetailsReturn() {
  return (
    <div className="space-y-4">
      <PageTitle>Card details updated successfully</PageTitle>
      <p>Your card details have been updated successfully.</p>
      <Link to={ROUTES.Dashboard} styleType="button">
        Go to dashboard
      </Link>
    </div>
  );
}
