import { createFileRoute, useNavigate } from '@tanstack/react-router';
import { ROUTES } from 'src/config/routes';
import { useForm } from 'react-hook-form';
import Input from 'src/components/common/Input';
import Button from 'src/components/common/Button';
import { yupResolver } from '@hookform/resolvers/yup';
import { ChangePasswordFormInputs, schema } from 'src/schemas/change-password';
import { useAuth } from 'src/hooks/useAuth';
import { changePassword } from 'src/api/auth';
import PageTitle from 'src/components/common/PageTitle';
import ErrorText from 'src/components/common/ErrorText';
import { useRequest } from 'src/hooks/useRequest';
import { passwordValidation } from 'src/helpers';
import { useMemo, useState } from 'react';

const ChangePassword = () => {
  const auth = useAuth();
  const navigate = useNavigate();
  const {
    run: runChangePassword,
    error,
    isLoading
  } = useRequest(changePassword);
  const [isPasswordValidationVisible, setIsPasswordValidationVisible] =
    useState(false);

  const handleLogOut = () => {
    auth.logout();
    navigate({ to: ROUTES.Login });
  };

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, dirtyFields }
  } = useForm<ChangePasswordFormInputs>({
    resolver: yupResolver(schema),
    mode: 'all'
  });

  const newPassword = watch('new_password');

  const onSubmit = (data: ChangePasswordFormInputs) => {
    runChangePassword(data).then(() => {
      handleLogOut();
    });
  };

  const isOldPasswordInValid = useMemo(
    () => passwordValidation.some(({ isValid }) => !isValid(newPassword)),
    [newPassword]
  );

  return (
    <div>
      <PageTitle>Change password</PageTitle>

      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="mt-5 space-y-12 mb-20">
          <div className="mr-6">
            <Input
              label="Old password"
              type="password"
              error={errors?.old_password?.message}
              {...register('old_password')}
            />
          </div>

          <Input
            label="New password"
            type="password"
            {...register('new_password')}
            withValidationMark
            error={errors?.new_password?.message}
            dirty={dirtyFields.new_password}
            onBlur={() => setIsPasswordValidationVisible(true)}
            onFocus={() => setIsPasswordValidationVisible(false)}
            hideErrorMessage={!isPasswordValidationVisible}
            description={
              isOldPasswordInValid && (
                <p className="flex flex-col gap-1">
                  {passwordValidation.map(({ label, isValid }) => (
                    <span
                      key={label}
                      className={
                        isValid(newPassword)
                          ? 'text-green-500'
                          : 'text-gray-500'
                      }
                    >
                      {label}
                      <br />
                    </span>
                  ))}
                </p>
              )
            }
          />
        </div>

        <div className="mt-8">
          <ErrorText>{error}</ErrorText>

          <Button type="submit" isLoading={isLoading}>
            Submit
          </Button>
        </div>
      </form>
    </div>
  );
};

export const Route = createFileRoute('/_auth/_layout/settings/change-password')(
  {
    component: ChangePassword
  }
);
