import { yupResolver } from '@hookform/resolvers/yup';
import { createFileRoute, useNavigate } from '@tanstack/react-router';
import { useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import { updateSubscriber } from 'src/api/data';
import { CheckRoundedFilledIcon } from 'src/assets/icons/CheckRoundedFilled';
import Button from 'src/components/common/Button';
import { Calendar } from 'src/components/common/CalendarInput';
import ErrorText from 'src/components/common/ErrorText';
import Input from 'src/components/common/Input';
import PageTitle from 'src/components/common/PageTitle';
import { isOpenId } from 'src/config/env-vars';
import { ROUTES } from 'src/config/routes';
import { useRequest } from 'src/hooks/useRequest';
import useSubscription from 'src/hooks/useSubscription';
import { schema, SubscriberInputs } from 'src/schemas/subscriber';
import { twMerge } from 'tailwind-merge';

const Subscriber = () => {
  const navigate = useNavigate();
  const { subscriber, setSubscriber} = useSubscription();
  const { run: runUpdateSubscriber, error } = useRequest(updateSubscriber);
  const {
    control,
    register,
    handleSubmit,
    formState: { errors },
    trigger,
    setValue,
    watch
  } = useForm({
    defaultValues: {
      fullName: subscriber?.name || '',
      dob: subscriber?.date_of_birth as string,
      sendMarketing: subscriber?.send_marketing
    },
    resolver: yupResolver(schema),
    mode: 'all'
  });

  const watchName = watch('fullName');
  const watchDob = watch('dob');

  const handleInputChange = (
    fieldName: 'fullName' | 'dob' | 'sendMarketing',
    value: string | boolean
  ) => {
    setValue(fieldName, value);
    trigger(fieldName);
  };

  const onSubmit = (data: SubscriberInputs) => {
    runUpdateSubscriber({
      date_of_birth: data.dob,
      name: data.fullName,
      send_marketing: data.sendMarketing
    }).then((res) => {
      setSubscriber(res);
      toast.success('Your data has been updated successfully.');
    });
  };

  useEffect(() => {
    if (isOpenId) {
      navigate({ to: ROUTES.Settings });
    }
  }, [navigate]);

  return (
    <>
      <PageTitle>Change details</PageTitle>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="space-y-12 mb-20">
          <Input
            label="Full name"
            {...register('fullName')}
            onChange={(event) =>
              handleInputChange('fullName', event.target.value)
            }
            error={errors.fullName?.message}
            dirty={Boolean(watchName)}
            withValidationMark
          />

          <Controller
            control={control}
            name="dob"
            render={({ field: { value, onBlur } }) => (
              <Calendar
                value={value}
                onChange={(val) => {
                  onBlur();
                  handleInputChange('dob', val);
                }}
                label="Date of Birth"
                dirty={Boolean(watchDob)}
                error={errors?.dob?.message}
                hideErrorMessage={errors?.dob?.message === 'hidden'}
                withValidationMark
              />
            )}
          />

          <Controller
            control={control}
            name="sendMarketing"
            render={({ field: { value } }) => (
              <button
                type="button"
                className="group flex gap-4 items-center text-sm"
                onClick={() => handleInputChange('sendMarketing', !value)}
              >
                <CheckRoundedFilledIcon
                  className={twMerge(
                    'size-5 xl:group-hover:text-black/45 cursor-pointer transition-all',
                    value ? 'text-black' : 'text-white'
                  )}
                />
                <span className="font-semibold">
                  Opt-in for exclusive discounts & offers?
                </span>
              </button>
            )}
          />
        </div>

        <div>
          <ErrorText>{error}</ErrorText>
          <Button type="submit">Update</Button>
        </div>
      </form>
    </>
  );
};

export const Route = createFileRoute('/_auth/_layout/settings/subscriber')({
  component: Subscriber
});
