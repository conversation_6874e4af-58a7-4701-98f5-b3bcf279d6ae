import { createFileRoute } from '@tanstack/react-router';
import { useState } from 'react';
import { cancelSubscription } from 'src/api/subscription';
import Button from 'src/components/common/Button';
import Dialog from 'src/components/common/Dialog';
import PageTitle from 'src/components/common/PageTitle';
import { epochToDate } from 'src/helpers';
import useSubscription from 'src/hooks/useSubscription';

export const Route = createFileRoute(
  '/_auth/_layout/settings/cancel-subscription'
)({
  component: CancelSubscription
});

function CancelSubscription() {
  const { fetchSubscriber, currentPlan } = useSubscription();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isSubscriptionCancelled, setIsSubscriptionCancelled] = useState(false);

  const nextBillDate = epochToDate(Number(currentPlan.next_bill_date_epoch));

  const handleCancelSubscription = () => {
    cancelSubscription(currentPlan.id).then(() => {
      setIsDialogOpen(false);
      setIsSubscriptionCancelled(true);
      fetchSubscriber();
    });
  };

  const handleCancelDialog = () => {
    setIsDialogOpen(false);
  };

  if (
    isSubscriptionCancelled ||
    currentPlan.subscription_status === 'cancelled'
  ) {
    return (
      <div>
        <PageTitle>Plan cancelled</PageTitle>
        <p>Your subscription has been cancelled.</p>
      </div>
    );
  }

  return (
    <div>
      <PageTitle>Cancel plan</PageTitle>
      <p>
        Upon cancellation, you will still be able to use your current allowance
        until {nextBillDate}, after which your service will terminate and you
        will no longer incur charges.
      </p>
      <div className="mt-16">
        <Button onClick={() => setIsDialogOpen(true)} color="danger">
          Cancel plan
        </Button>
      </div>

      <Dialog
        isOpen={isDialogOpen}
        onClose={handleCancelDialog}
        rightButton={
          <Button onClick={handleCancelSubscription} color="danger">
            Yes
          </Button>
        }
        leftButton={<Button onClick={handleCancelDialog}>No</Button>}
        title="Cancel plan"
      >
        Are you sure you want to cancel your subscription? This action is
        irreversible.
      </Dialog>
    </div>
  );
}
