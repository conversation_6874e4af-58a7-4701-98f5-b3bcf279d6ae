import { createFileRoute, <PERSON> } from '@tanstack/react-router';
import PageTitle from 'src/components/common/PageTitle';
import useChat from 'src/hooks/useChat';
import { show } from '@intercom/messenger-js-sdk';
import { twMerge } from 'tailwind-merge';
import { ROUTES } from 'src/config/routes';
import { QRCodeIcon } from 'src/assets/icons/QRCode';
import { NumberPortingIcon } from 'src/assets/icons/NumberPorting';
import { CloudWarning } from 'src/assets/icons/CloudWarning';
import { InformationDeskIcon } from 'src/assets/icons/InformationDesk';
import { MailSendIcon } from 'src/assets/icons/MailSend';
import { OnlineSupportIcon } from 'src/assets/icons/OnlineSupport';

const intercomKey = import.meta.env.VITE_INTERCOM_KEY;

const commonStyle =
  'p-4 py-6 flex justify-between items-center gap-3 mt-2 bg-white rounded-lg hover:bg-white/50';

export const Route = createFileRoute('/_auth/_layout/settings/support')({
  component: Support
});

const quickHelp = [
  {
    title: 'I am having eSIM issues',
    icon: <QRCodeIcon className="w-7 h-7 -my-0.5" />,
    link: window.clientConfig.help_esim_install,
    blank: true
  },
  {
    title: 'I want to keep my number',
    icon: <NumberPortingIcon />,
    link: ROUTES.PortNumber
  },
  {
    title: 'My data isn’t working',
    icon: <CloudWarning />,
    link: window.clientConfig.help_data_issue,
    blank: true
  }
];

function Support() {
  useChat();

  const supportEmail = window.clientConfig.supportEmail;
  const supportLink = window.clientConfig.supportLink;

  const contactUsLinks = [
    {
      title: 'Knowledge base',
      icon: <InformationDeskIcon />,
      link: supportLink,
      description: 'Immediate resolution',
      blank: true
    },
    {
      title: 'Email support',
      icon: <MailSendIcon />,
      link: `mailto:${supportEmail}`,
      description: '~1 hour resolution'
    }
  ];

  const openChat = () => {
    show();
  };

  return (
    <div>
      <PageTitle>Request help</PageTitle>
      <h4 className="font-semibold text-base mb-2">I need quick help</h4>
      {quickHelp.map(({ link, icon, title, blank }) => (
        <Link
          to={link}
          className={commonStyle}
          target={blank && link ? '_blank' : undefined}
        >
          <span className="text-xs font-semibold">{title}</span>
          {icon}
        </Link>
      ))}

      <h4 className="font-semibold text-base mt-8 mb-2">Contact us</h4>
      {contactUsLinks.map(({ link, icon, title, description, blank }) => (
        <a
          href={link}
          className={twMerge(commonStyle, 'py-6')}
          target={blank && link ? '_blank' : undefined}
        >
          <span className="flex gap-4 items-center">
            {icon}
            <span className="text-xs font-semibold">{title}</span>
          </span>
          <span className="text-[10px] text-green max-w-[110px] text-right font-medium">
            {description}
          </span>
        </a>
      ))}
      {intercomKey && (
        <button
          onClick={openChat}
          className={twMerge(commonStyle, 'py-6 w-full h-[68px]')}
        >
          <span className="flex gap-4 items-center">
            <OnlineSupportIcon />
            <span className="text-xs font-semibold">Online chat</span>
          </span>
          <span className="text-[10px] text-green max-w-[110px] text-right font-medium">
            Available weekdays 9am - 6pm
          </span>
        </button>
      )}
    </div>
  );
}
