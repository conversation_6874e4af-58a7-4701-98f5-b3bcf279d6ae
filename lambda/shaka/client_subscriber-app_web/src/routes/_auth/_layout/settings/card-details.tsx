import { createFileRoute } from '@tanstack/react-router';
import PageTitle from 'src/components/common/PageTitle';
import useSubscription from 'src/hooks/useSubscription';
import CardUpdate from 'src/components/plan-upgrade/CardUpdate';
import MultiPlanBilling from 'src/components/card-details/MultiPlanBilling';

export const Route = createFileRoute('/_auth/_layout/settings/card-details')({
  component: CardDataDetails
});

function CardDataDetails() {
  const { hasMultiplePlans } = useSubscription();

  return (
    <div>
      <PageTitle withBottomMargin={hasMultiplePlans}>Update billing</PageTitle>

      {hasMultiplePlans ? <MultiPlanBilling /> : <CardUpdate />}
    </div>
  );
}
