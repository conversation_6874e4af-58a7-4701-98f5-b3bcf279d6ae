import { createFileRoute } from '@tanstack/react-router';
import { ROUTES } from 'src/config/routes';
import { Controller, useForm } from 'react-hook-form';
import Input from 'src/components/common/Input';
import Button from 'src/components/common/Button';
import { useEffect, useMemo, useState } from 'react';
import Link from 'src/components/common/Link';
import { yupResolver } from '@hookform/resolvers/yup';
import { PortNumberFormInputs, schema } from 'src/schemas/port-number';
import useSubscription from 'src/hooks/useSubscription';
import PageTitle from 'src/components/common/PageTitle';
import ErrorText from 'src/components/common/ErrorText';
import { PacCodeStatus } from 'src/types/subscriber';
import { OneInputCalendar } from 'src/components/common/CalendarInput';
import {
  defaultSimName,
  epochToDate,
  formateToShortDateString
} from 'src/helpers';
import { isWeekendOrHoliday } from 'src/helpers/date';
import { twMerge } from 'tailwind-merge';
import Dropdown from 'src/components/common/Dropdown';
import { portPhoneNumber } from 'src/api/subscription';

const pacStatusDescriptions: { [key in PacCodeStatus]: string } = {
  [PacCodeStatus.PENDING]: 'Pending',
  [PacCodeStatus.IN_PROGRESS]: 'In progress',
  [PacCodeStatus.ERRORED]: 'Failed',
  [PacCodeStatus.DONE]: 'Complete',
  [PacCodeStatus.NOT_SET]: ''
};

const commonStyle =
  'p-4 py-5 flex gap-3 mt-2 bg-white rounded-lg md:hover:bg-white/50';

const PortNumber = () => {
  const { setSubscriber, currentPlan, subscriber, currentPlanIndex } =
    useSubscription();

  const oneSubscriptionFlow = subscriber?.plans?.length === 1;
  const multipleSubscriptionFlow = (subscriber?.plans?.length || 0) > 1;

  const isSimActive = currentPlan.sim_activation_status === 'active';

  const [isRequestSent, setIsRequestSent] = useState(false);
  const [isPromptHidden, setIsPromptHidden] = useState<'manual' | 'sms' | null>(
    null
  );
  const [isPortAnotherNumber, setIsPortAnotherNumber] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    control,
    setValue,
    register,
    handleSubmit,
    formState: { errors }
  } = useForm<PortNumberFormInputs>({
    resolver: yupResolver(schema)
  });

  const { isPacCodePending, isPacCodeInProgress, isPacCodeFailed } =
    useMemo(() => {
      if (oneSubscriptionFlow) {
        const plan = subscriber?.plans[0];
        return {
          isPacCodePending: plan.pac_code_status === PacCodeStatus.PENDING,
          isPacCodeInProgress:
            plan.pac_code_status === PacCodeStatus.IN_PROGRESS,
          isPacCodeFailed: plan.pac_code_status === PacCodeStatus.ERRORED
        };
      }

      return {
        isPacCodePending: false,
        isPacCodeInProgress: false,
        isPacCodeFailed: false
      };
    }, [subscriber?.plans]);

  const portingHistory = useMemo(() => {
    return subscriber?.plans.filter(
      (plan) =>
        plan.pac_code.status === PacCodeStatus.PENDING ||
        plan.pac_code.status === PacCodeStatus.IN_PROGRESS ||
        plan.pac_code.status === PacCodeStatus.ERRORED ||
        plan.pac_code.status === PacCodeStatus.DONE
    );
  }, [subscriber?.plans]);

  const planOptions = useMemo(
    () =>
      subscriber?.plans
        ? subscriber.plans
            .filter((plan) => plan.pac_code.status === PacCodeStatus.NOT_SET)
            .map((plan, index) => {
              const planNameWithDefault =
                plan.user_subscription_name || `${defaultSimName} ${index + 1}`;
              const planWithDefaultAndTitle = `${plan.title} - ${planNameWithDefault}`;

              return {
                label: plan.title
                  ? planWithDefaultAndTitle
                  : planNameWithDefault,
                value: index
              };
            })
        : [],
    [subscriber?.plans]
  );

  const onSubmit = (data: PortNumberFormInputs) => {
    portPhoneNumber({
      ...data,
      subscription_id: subscriber!.plans[data.selectedPlan!].id,
      date: formateToShortDateString(data.date)
    })
      .then((newSubscriberData) => {
        setIsRequestSent(true);
        setSubscriber(newSubscriberData);
      })
      .catch((error) => {
        setError(error.response?.data.error || 'Something went wrong');
      });
  };

  const isTileDisabled = useMemo(
    () =>
      ({ date, view }: { date: Date; view: string }) => {
        if (view === 'month') {
          const isToday = date.toDateString() === new Date().toDateString();
          return isWeekendOrHoliday(date) || isToday;
        }
        return false;
      },
    []
  );

  const togglePrompt = (pacRequestType: 'sms' | 'manual') => {
    setIsPromptHidden(pacRequestType);
  };

  useEffect(() => {
    if (subscriber && subscriber.plans && oneSubscriptionFlow) {
      setValue('selectedPlan', currentPlanIndex);
    }
  }, [subscriber]);

  if (oneSubscriptionFlow && isPacCodeFailed) {
    return (
      <div className="space-y-4">
        <PageTitle>Something went wrong</PageTitle>
        <p>There was a problem porting your number, please contact support</p>

        <Link to={ROUTES.Support} styleType="button">
          Contact support
        </Link>
        <Link to={ROUTES.Dashboard} styleType="button" color="secondary">
          Home
        </Link>
      </div>
    );
  }

  if (oneSubscriptionFlow && isPacCodeInProgress) {
    return (
      <div className="space-y-4">
        <PageTitle>Port in progress</PageTitle>

        <p>
          We have successfully received your request and your number porting
          will happen automatically.
        </p>

        <Link to={ROUTES.Dashboard} styleType="button">
          Home
        </Link>
      </div>
    );
  }

  if ((oneSubscriptionFlow && isRequestSent) || isPacCodePending) {
    return (
      <div className="space-y-4">
        <PageTitle>Request received</PageTitle>
        {isSimActive ? (
          <p>
            We have successfully received your request and your number porting
            will happen automatically.
          </p>
        ) : (
          <p>
            Your number porting request will happen automatically when the SIM
            is activated.
          </p>
        )}

        <Link to={ROUTES.Dashboard} styleType="button">
          Home
        </Link>
      </div>
    );
  }

  if (
    multipleSubscriptionFlow &&
    !isPortAnotherNumber &&
    portingHistory?.length
  ) {
    return (
      <>
        <div className="space-y-4">
          <PageTitle>Multiple subscriptions</PageTitle>
          <h4 className="font-semibold mb-2">Porting history</h4>
          <div>
            {portingHistory.map((plan) => (
              <div
                className={twMerge(commonStyle, 'block pointer-events-none')}
              >
                <p className="flex justify-between items-center font-semibold text-sm">
                  <span
                    className={twMerge(
                      plan.pac_code.status === PacCodeStatus.DONE &&
                        'text-[#1EC25F]',
                      plan.pac_code.status === PacCodeStatus.ERRORED &&
                        'text-[#FF4D4F]'
                    )}
                  >
                    {pacStatusDescriptions[plan.pac_code.status]}
                  </span>
                  <span>{plan.pac_code.number}</span>
                </p>
                <p className="flex justify-between items-center text-sm mt-3">
                  <span>{epochToDate(plan.pac_code.expected_on)}</span>
                  <span>{plan.title}</span>
                </p>
              </div>
            ))}
          </div>
        </div>
        {Boolean(planOptions.length) && (
          <div className="mt-12">
            <Button onClick={() => setIsPortAnotherNumber(true)}>
              Port another number
            </Button>
          </div>
        )}
      </>
    );
  }

  return (
    <div className="pb-10">
      <PageTitle>Number porting</PageTitle>
      <h4 className="font-semibold mb-4">
        Request PAC from your previous network
      </h4>

      <button
        className={twMerge(
          commonStyle,
          'flex-col w-full overflow-hidden items-start transition-all gap-4',
          isPromptHidden === 'sms' ? 'max-h-[200px]' : 'max-h-[60px]'
        )}
        onClick={() => togglePrompt('sms')}
      >
        <span className="text-sm font-semibold">Request PAC by SMS</span>
        <span className="font-normal text-left text-[13px]">
          Send a text message <strong>“PAC”</strong> to <strong>65075</strong>{' '}
          to obtain your PAC code.
        </span>
      </button>

      <button
        className={twMerge(
          commonStyle,
          'flex-col w-full overflow-hidden items-start transition-all gap-4',
          isPromptHidden === 'manual' ? 'max-h-[200px]' : 'max-h-[60px]'
        )}
        onClick={() => togglePrompt('manual')}
      >
        <span className="flex justify-between items-center">
          <span className="text-sm font-semibold">Request PAC manually</span>
        </span>
        <span className="font-normal text-left text-[13px]">
          Speak to your current network provider and request your PAC, then
          enter it in the form below. This code is valid for 30 days.
        </span>
      </button>

      <form onSubmit={handleSubmit(onSubmit)}>
        <h4 className="font-semibold mb-2 mt-6">Already have a PAC?</h4>
        <div className="mt-5 space-y-4 mb-10">
          <Controller
            name="selectedPlan"
            control={control}
            render={({ field: { value, onChange } }) => (
              <div>
                <Dropdown
                  placeholder={
                    <span className="text-[#9ca3af] font-normal">
                      Select plan
                    </span>
                  }
                  value={planOptions[value as number]?.label || ''}
                  options={planOptions}
                  onSelect={onChange}
                />

                <ErrorText>{errors?.selectedPlan?.message}</ErrorText>
              </div>
            )}
          />
          <Input
            placeholder="Phone number to keep"
            error={errors?.phoneNumber?.message}
            {...register('phoneNumber')}
          />

          <Input
            placeholder="PAC e.g. PAC1234"
            error={errors?.pacCode?.message}
            {...register('pacCode')}
          />

          <div>
            <p className="text-sm mt-6 mb-2">Porting date (optional)</p>
            <OneInputCalendar
              error={errors?.date?.message}
              minDate={new Date()}
              control={control}
              name="date"
              placeholder="Port date (default: ASAP)"
              tileDisabled={isTileDisabled}
            />
          </div>
        </div>

        <ErrorText>{error}</ErrorText>

        <Button type="submit" onClick={() => setIsPortAnotherNumber(false)}>
          Submit
        </Button>
      </form>
    </div>
  );
};

export const Route = createFileRoute('/_auth/_layout/settings/port-number')({
  component: PortNumber
});
