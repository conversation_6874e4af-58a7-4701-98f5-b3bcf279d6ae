import { createFileRoute, useNavigate } from '@tanstack/react-router';
import { ROUTES } from 'src/config/routes';
import useSubscription from 'src/hooks/useSubscription';
import { formatRoamingDrawerTitle, getInitials } from 'src/helpers';
import { useState } from 'react';
import { useAuth } from 'src/hooks/useAuth';
import PageTitle from 'src/components/common/PageTitle';
import SupportLink from 'src/components/settings/SupportLink';
import SettingsBlock from 'src/components/settings/SettingsBlock';
import { PlanChangeStatus, PlanChangeType } from 'src/types/subscriber';
import { subscriberLinks, supportLinks } from 'src/config/settings-buttons';
import NotificationDialog from 'src/components/common/NotificationDialog';
import BoltOnsDrawer from 'src/components/bolt-ons';
import BoltonPaymentResult from 'src/components/bolt-ons/BoltOnPaymentResult';
import PlanUpgradeDrawer from 'src/components/plan-upgrade';
import useBoltOns from 'src/hooks/useBoltOns';
import SubscriptionSwiper from 'src/components/SubscriptionSwiper';
import SubscriptionActionButtons from 'src/components/settings/SubscriptionActionButtons';
import SubscriptionNotifications from 'src/components/settings/SubscriptionNotifications';
import TravelSimActionButtons from 'src/components/settings/TravelSimActionButtons';
import RoamingSimDrawer from 'src/components/roaming-sim';
import OtherLinksBlock from 'src/components/settings/OtherLinksBlock';
import { RoamingEsimStep } from 'src/types/roaming-sim';

const Settings = () => {
  const auth = useAuth();
  const navigate = useNavigate();
  const {
    subscriber,
    setSubscriber,
    currentPlan,
    subscriberPlanSimList,
    currentPlanIndex,
    roamingEsimPlans,
    currentRoamingSim
  } = useSubscription();
  const { boltOns } = useBoltOns();

  const searchParams = new URLSearchParams(window.location.search);
  const planChangeStep = searchParams.get('plan-change');
  const modal = searchParams.get('modal');
  const isBoltOnsOpen = modal === 'bolt-ons';
  const isBoltOnsPaymentOpen = modal === 'bolt-ons-payment';
  const isBoltOnsDisabled =
    (boltOns?.length === 0 && !currentPlan.roaming_bolt_on_eu) ||
    currentPlan.sim_activation_status !== 'active';
  const travelSimStep = searchParams.get('travel-esim') ? parseInt(searchParams.get('travel-esim') as string) : null;
  const userAction = searchParams.get('action');

  const isPlanChanging =
    currentPlan.latest_plan_change?.status === PlanChangeStatus.IN_PROGRESS &&
    currentPlan.latest_plan_change.change_type !== PlanChangeType.CANCEL_CHANGE;

  const subscriberPlanSim = subscriberPlanSimList[currentPlanIndex];

  const [isLogOutConfirmation, setIsLogOutConfirmation] = useState(false);
  const [isPlanButtonsShown, setIsPlanButtonsShown] = useState(true);

  const closeConfirmation = () => setIsLogOutConfirmation(false);

  const handleLogOut = () => {
    setSubscriber(null);
    auth.logout();
    navigate({ to: ROUTES.Login });
  };

  const closeDrawer = () => {
    setTimeout(() => {
      navigate({ to: '/settings' });
    }, 300);
  };

  const handlePlanChange = (index: number) => {
    setIsPlanButtonsShown(Boolean(subscriberPlanSimList[index]));
  };
  return (
    <>
      <PageTitle withBottomMargin={false}>manage</PageTitle>
      {planChangeStep && <PlanUpgradeDrawer step={Number(planChangeStep)} />}

      {isBoltOnsOpen && (
        <BoltOnsDrawer isOpen onClose={closeDrawer} title="BoltOns" />
      )}
      {isBoltOnsPaymentOpen && <BoltonPaymentResult />}

      {travelSimStep && (
        <RoamingSimDrawer
          isOpen
          onClose={closeDrawer}
          title={formatRoamingDrawerTitle(userAction, (travelSimStep == RoamingEsimStep.SETTINGS || travelSimStep == RoamingEsimStep.SUCCESS) ? roamingEsimPlans?.length - 1 : roamingEsimPlans?.length) || ''}
          withOverflow={false}
        />
      )}

      <SubscriptionSwiper onSwipe={handlePlanChange} />

      {isPlanButtonsShown ? (
        <>
          {subscriberPlanSim?.type === 'plan' && (
            <SubscriptionActionButtons
              isBoltOnsDisabled={isBoltOnsDisabled}
              isPlanChanging={isPlanChanging}
            />
          )}

          {subscriberPlanSim?.type === 'sim' && (
            <TravelSimActionButtons
              roamingZone={
                subscriberPlanSim.type === 'sim'
                  ? currentRoamingSim?.zone
                  : undefined
              }
            />
          )}
        </>
      ) : (
        <div className="h-[100px]" />
      )}

      <SubscriptionNotifications />

      <SettingsBlock title="Support">
        {supportLinks.map(({ to, label, Icon }) => (
          <SupportLink to={to} key={label}>
            <Icon className="h-3 w-3" />
            {label}
          </SupportLink>
        ))}
      </SettingsBlock>

      <SettingsBlock title={subscriber?.name || 'User data'}>
        {subscriber?.name && (
          <span className="uppercase initials">
            {getInitials(subscriber?.name)}
          </span>
        )}
        {subscriberLinks.map(({ to, label, Icon }) => (
          <SupportLink to={to} key={label}>
            <Icon className="h-3 w-3" />
            {label}
          </SupportLink>
        ))}
      </SettingsBlock>

      <div className="mb-8">
        <SettingsBlock title="Other">
          <OtherLinksBlock onLogout={() => setIsLogOutConfirmation(true)} />
        </SettingsBlock>
      </div>

      <NotificationDialog
        isOpen={isLogOutConfirmation}
        onCancel={closeConfirmation}
        onSubmit={handleLogOut}
        title="Sign out"
        description="Are you sure you wish to sign out?"
        cancelActionColor="default"
        submitActionColor="default"
      />
    </>
  );
};

export const Route = createFileRoute('/_auth/_layout/settings/')({
  component: Settings
});
