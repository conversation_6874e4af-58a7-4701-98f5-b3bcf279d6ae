import { Navigate } from '@tanstack/react-router';
import { createFileRoute } from '@tanstack/react-router';
import { ROUTES } from 'src/config/routes';

export const Route = createFileRoute('/_auth/_layout/bolt-ons/return')({
  component: BoltOnsReturn
});

export function BoltOnsReturn() {
  return (
    <Navigate
      to={ROUTES.Settings}
      search={{ modal: 'bolt-ons-payment' }}
      replace
    />
  );
}
