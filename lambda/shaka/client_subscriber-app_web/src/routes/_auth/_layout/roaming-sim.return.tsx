import { Navigate, useRouter } from '@tanstack/react-router';
import { createFileRoute } from '@tanstack/react-router';
import { ROUTES } from 'src/config/routes';
import { RoamingEsimStep } from 'src/types/roaming-sim';
import {
  removeStripeInfo,
  setCorrelationId,
  setStripeSessionInfo
} from 'src/config/localStorageActions.ts';
import PageTitle from 'components/common/PageTitle.tsx';
import Loader from 'components/common/Loader.tsx';
import useSubscription from 'src/hooks/useSubscription.tsx';
import { PaymentStatus, SessionStatus } from 'src/types/checkout.ts';
import { useEffect } from 'react';
import Button from 'components/common/Button.tsx';
import { checkSessionStatus } from 'src/api/stripe.ts';

export const Route = createFileRoute('/_auth/_layout/roaming-sim/return')({
  component: RoamingSimReturn,
  loaderDeps: ({
    search: { session_id }
  }: {
    search: Record<string, string>;
  }) => ({
    session_id
  }),
  loader: ({ deps: { session_id } }) =>
    checkSessionStatus(session_id).then((res) => {
      setStripeSessionInfo(session_id, res.expires_at);

      return { status: res.status, paymentStatus: res.payment_status };
    })
});

export function RoamingSimReturn() {
  const { history } = useRouter();
  const { subscriber, fetchSubscriber, setCurrentPlanByCorrelationId } =
    useSubscription();
  const { status, paymentStatus } = Route.useLoaderData<{
    paymentStatus: PaymentStatus;
    status: SessionStatus;
  }>();
  const isSessionCompleted = status === SessionStatus.Complete;
  const isPaymentPaid = paymentStatus === PaymentStatus.Paid;

  const correlationId = new URLSearchParams(window.location.search).get(
    'correlation_id'
  );

  const onBack = () => history.back();

  useEffect(() => {
    if (isSessionCompleted && isPaymentPaid) {
      removeStripeInfo();
    }
  }, [isPaymentPaid, isSessionCompleted]);

  useEffect(() => {
    const interval = setInterval(() => {
      if (!subscriber?.plans) {
        fetchSubscriber();
      }
    }, 500);
    return () => clearInterval(interval);
  }, [subscriber, fetchSubscriber]);

  const planWithMatchingCorrelationId = subscriber?.roaming_esims?.find(
    (roaming_plan) => roaming_plan.correlation_id === correlationId
  );

  useEffect(() => {
    if (planWithMatchingCorrelationId) {
      setCurrentPlanByCorrelationId(
        planWithMatchingCorrelationId.correlation_id
      );
    }
  }, [planWithMatchingCorrelationId, setCurrentPlanByCorrelationId]);

  if (!isSessionCompleted) {
    return (
      <div className="grow flex flex-col justify-center">
        <PageTitle>Payment failed</PageTitle>
        <p className="text-center mb-20">
          We are unable to process your payment. Please try again.
        </p>
        <Button onClick={onBack}>Return to payment</Button>
      </div>
    );
  }

  if (!planWithMatchingCorrelationId) {
    setCorrelationId(correlationId!);
    return (
      <div className="grow flex flex-col justify-center items-center h-screen">
        <PageTitle>Transaction in progress</PageTitle>
        <p className="text-center mb-20">
          Your payment is being processed.
          <br />
          Please wait a moment
        </p>
        <Loader />
      </div>
    );
  }

  const showEsimSettings = planWithMatchingCorrelationId.show_esim_settings;

  if (showEsimSettings) {
    return (
      <Navigate
        to={ROUTES.Settings}
        search={{
          'travel-esim': RoamingEsimStep.SETTINGS + 1,
          action: 'getTravelEsim'
        }}
        replace
      />
    );
  } else {
    return (
      <Navigate
        to={ROUTES.Settings}
        search={{
          'travel-esim': RoamingEsimStep.SUCCESS + 1,
          action: 'getTravelEsim'
        }}
        replace
      />
    );
  }
}
