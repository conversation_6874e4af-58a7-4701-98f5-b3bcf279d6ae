type BoltonExtension = {
  total: number;
  used: number;
};

export type ActivatedBolton = {
  id: number;
  type: 'data' | 'voice';
  title: string;
  expires: string;
  data?: BoltonExtension;
  voice?: BoltonExtension;
  status: 'active' | 'inactive';
};

export type BoltOn = {
  id: number;
  title: string;
  price: string;
  data?: string;
  voice?: string;
};

export type RoamingBoltOn = {
  title: string;
  id: number;
  data: string;
  voice: string;
  sms: string;
  price: number;
  zone: string;
  valid_until: Date;
};

export type PurchasedRoamingBolton = {
  title: string;
  id: number;
  data: string;
  voice: string;
  sms: string;
  price: number;
  zone: string;
  valid_until: Date;
  country_code: string;
};

export type BoltOnCountry = {
  label: string;
  zone: string;
  value: string;
};

export type EUBoltOn = {
  day_price: number;
  days_used: number;
  days_total: number;
};
