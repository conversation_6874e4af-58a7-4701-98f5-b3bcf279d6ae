import { ActivatedBolton, EUBoltOn, PurchasedRoamingBolton } from './boltons';
import { RoamingSim } from './roaming-sim';

export type SimActivationStatus =
  | 'active'
  | 'inactive'
  | 'not-assigned'
  | 'pending-activation'
  | 'requires-activation';

type SubscriptionStatus = 'active' | 'inactive' | 'cancelled';

export enum PacCodeStatus {
  NOT_SET = 'not-set',
  PENDING = 'pending',
  IN_PROGRESS = 'in-progress',
  ERRORED = 'errored',
  DONE = 'done'
}

type ComponentData = {
  total: number;
  left: number | null;
  is_unlimited?: boolean;
};

export enum PlanChangeType {
  UPGRADE = 'upgrade',
  DOWNGRADE = 'downgrade',
  CANCELLATION = 'cancellation',
  CANCEL_CHANGE = 'cancel_change'
}

export enum PlanChangeStatus {
  IN_PROGRESS = 'in_progress',
  LOCKED = 'locked',
  COMPLETE = 'complete',
  ERRORED = 'errored',
  CANCELLED = 'cancelled'
}

type PlanChange = {
  change_type: PlanChangeType;
  id: number;
  status: PlanChangeStatus;
  subscriber_status_display: 'Pending';
  target_plan_change_id: null;
  target_plan_id: number;
};

export type SubscriberPlan = {
  id: number;
  correlation_id: string;
  bg?: number;
  plan_id: number;
  title: string;
  sim_activation_status: SimActivationStatus;
  data: ComponentData;
  voice: ComponentData;
  sms: ComponentData;
  subscription_status: SubscriptionStatus;
  pac_code_status: PacCodeStatus;
  sim_serial_fragment?: string;
  price: string;
  next_bill_date_epoch: number;
  self_activated: boolean;
  can_cancel: boolean;
  can_cancel_change: boolean;
  can_downgrade: boolean;
  can_upgrade: boolean;
  plan_changes: PlanChange[] | null;
  latest_plan_change: PlanChange | null;
  sim_type: 'esim' | 'physical';
  bolt_ons: ActivatedBolton[];
  next_bill_amount: string | null;
  roaming_bolt_on: PurchasedRoamingBolton;
  roaming_bolt_on_eu: EUBoltOn;
  pac_code: {
    status: PacCodeStatus;
    number: string | null;
    expected_on: number;
  };
  notifications: {
    show_number_transfer: boolean;
    show_number_porting_progress: boolean;
    show_update_apn: boolean;
    show_set_up_esim: boolean;
  };
  phone_number: string | null;
  user_subscription_name: string;
  esim_status: string;
};

export type Subscriber = {
  id: number;
  name: string;
  address: string;
  email: string;
  sim_fragment: string | null;
  phone_number: string | null;
  plans: SubscriberPlan[];
  date_of_birth: string | null;
  is_verified: boolean;
  join_date: string;
  send_marketing: boolean;
  roaming_esims: RoamingSim[];
};
