#*#
*~
.terraform.lock.hcl
terraform.tfstate
terraform.tfstate.backup
.terraform
__pycache__
.venv
.DS_Store
.newvenv/
.nodeenv
.python-version
*.plaintext
local_settings.py
static/
.idea/
/lambda/shaka/client_subscriber-app_web/.yarnrc.yml
/lambda/shaka/client_subscriber-app_web/.yarn/install-state.gz
/lambda/shaka/simp-webapp/.yarnrc.yml
/smtp.py
/lambda/shaka/next-subscriber-app/todo/openapi-client-automation.md
/todo
/lambda/shaka/next-subscriber-app/todo/

/lambda/shaka/next-subscriber-app/client-config/deliveroo.env
