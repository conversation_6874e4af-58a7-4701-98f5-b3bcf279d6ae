#!/bin/bash -e
SCRIPT_DIR=$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )

for directory in `find ./lambda/shaka/ -mindepth 1 -maxdepth 1 -type d`
do
    cd "$SCRIPT_DIR"
    cd "$directory"
    if [[ "$directory" != *"cdr_db"* ]] && [[ "$directory" != *"client_dashboard"* ]] && [[ "$directory" != *"nexus"* ]] && [[ "$directory" != *"frontend"* ]]; then
        python -m unittest discover
    fi
done

cd $SCRIPT_DIR
cd lambda/shaka/cdr_db
python manage.py test --settings=cdr_db.test_settings --noinput
cd $SCRIPT_DIR
cd lambda/shaka/client_dashboard
python manage.py test --settings=client_dashboard.test_settings --noinput
cd $SCRIPT_DIR
cd lambda/shaka/nexus
python manage.py test --settings=nexus.test_settings --noinput



cd $SCRIPT_DIR
cd lambda/shaka/async_api
python -m unittest discover -v

cd "$SCRIPT_DIR"
cd lambda/shaka/client_dashboard_frontend

cd $SCRIPT_DIR
cd lambda/shaka/next-subscriber-app
pnpm test

CI=true yarn test

